<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardViewRoot"
    android:background="@android:color/transparent"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:clickable="true"
        android:paddingHorizontal="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintHeight_percent="0.7"
        android:background="@drawable/background_bottom_sheet"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical">
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/rectangle"
            android:layout_centerHorizontal="true"
            android:layout_width="wrap_content"
            android:layout_height="5dp"
            android:layout_gravity="center"
            android:layout_marginVertical="8dp"
            android:adjustViewBounds="true"
            android:src="@drawable/ic_bottom_sheet_dimmer" />

        <RelativeLayout
            android:clickable="true"
            android:layout_below="@+id/rectangle"
            android:id="@+id/rl_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/textView_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="10dp"
                android:layout_toStartOf="@+id/img_dismiss"
                android:layout_toLeftOf="@+id/img_dismiss"
                android:text="@string/select_country"
                android:textColor="@android:color/black"
                android:textSize="20sp"/>

            <ImageView
                android:id="@+id/img_dismiss"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_alignParentEnd="true"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:alpha="0.7"
                android:clickable="true"
                android:contentDescription="@string/dismiss_button_content_description"
                android:focusable="true"
                android:padding="4dp"
                app:srcCompat="@drawable/ic_clear_black_24dp" />
        </RelativeLayout>
        <LinearLayout
            android:id="@+id/rl_query_holder"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="@drawable/border_input_text_search"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_below="@+id/rl_title">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_marginStart="14dp"
                android:layout_marginEnd="10dp"
                android:adjustViewBounds="true"
                android:background="@null"
                android:src="@drawable/ic_search" />

            <EditText
                android:id="@+id/editText_search"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="14dp"
                android:layout_weight="1"
                android:background="@null"
                android:cursorVisible="true"
                android:fontFamily="@font/roboto"
                android:hint="@string/search_hint_text"
                android:lines="1"
                android:textColor="#000"
                android:textColorHint="#938F99"
                android:textCursorDrawable="@drawable/cursor_drawable"
                android:textSize="18sp" />

            <ImageView
                android:id="@+id/img_clear_query"
                android:layout_width="wrap_content"
                android:layout_height="15dp"
                android:layout_marginEnd="16dp"
                android:adjustViewBounds="true"
                android:background="@null"
                android:src="@drawable/ic_delete_text"
                android:visibility="gone" />
        </LinearLayout>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_countryDialog"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/rl_query_holder"
            android:overScrollMode="never"
            android:scrollbars="none"
            android:dividerHeight="2dp" />

        <com.futuremind.recyclerviewfastscroll.FastScroller
            android:id="@+id/fastscroll"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignTop="@+id/recycler_countryDialog"
            android:layout_alignEnd="@+id/recycler_countryDialog"
            android:layout_alignRight="@+id/recycler_countryDialog"
            android:layout_alignBottom="@+id/recycler_countryDialog"
            android:orientation="vertical" />

        <TextView
            android:id="@+id/textView_noresult"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/rl_query_holder"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="16dp"
            android:text="@string/no_result_found"
            android:textColor="@android:color/primary_text_light"
            android:visibility="gone" />

    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>