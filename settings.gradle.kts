pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
//        jcenter()
        maven { url = uri("https://www.jitpack.io") }
        maven { url = uri("https://maven.aliyun.com/repository/public")   }
        pluginManagement {
            repositories.configureEach {
                if (this is MavenArtifactRepository && url.toString().startsWith("https://jcenter.bintray.com/")) {
                    println("Repository $url replaced by gradlePluginPortal().")
                    remove(this)
                }
            }
        }
    }
}

rootProject.name = "Cashier Food Order"
include(":app", ":ccp")
 