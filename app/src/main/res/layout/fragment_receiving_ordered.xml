<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/mainBackground">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutFirst"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="6dp"
        android:background="@color/mainBackground"
        android:paddingHorizontal="6dp"
        android:paddingTop="6dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.6">

        <LinearLayout
            android:id="@+id/layoutTopPart"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingBottom="6dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:id="@+id/dropdownTable"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_marginEnd="6dp"
                android:layout_weight="1"
                android:background="@drawable/background_language_spiner"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="10dp">

                <TextView
                    android:id="@+id/tvTable"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:maxLines="1"
                    android:text="@string/all_table"
                    android:textColor="@color/black"
                    android:textSize="12sp"
                    tools:text="" />

                <androidx.cardview.widget.CardView
                    android:id="@+id/cardViewFilterTable"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:elevation="0dp"
                    android:visibility="gone"
                    app:cardBackgroundColor="@color/main_red"
                    app:cardCornerRadius="12.5dp"
                    app:cardElevation="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1"
                    app:layout_constraintStart_toEndOf="@id/tvTableID"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvCount"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="25dp"
                        android:gravity="center_vertical|end"
                        android:maxLines="1"
                        android:paddingHorizontal="8dp"
                        android:paddingVertical="3dp"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold"
                        tools:text="+1" />
                </androidx.cardview.widget.CardView>

                <ImageView
                    android:id="@+id/arrowTable"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:src="@drawable/ic_dropdown"
                    android:visibility="visible"
                    tools:ignore="ContentDescription" />
            </LinearLayout>


            <com.metathought.food_order.casheir.ui.widget.CustomSearchView
                android:id="@+id/edtSearch"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                app:search_hint="@string/hint_phone_number" />

            <TextView
                android:id="@+id/tvClearFilter"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:gravity="center_vertical"
                android:paddingHorizontal="10dp"
                android:text="@string/clear_filter"
                android:textColor="@color/primaryColor"
                android:textSize="@dimen/_14ssp" />

        </LinearLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutPendingList"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="6dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layoutTopPart"
            app:layout_constraintWidth_percent="0.5">

            <androidx.cardview.widget.CardView
                android:id="@+id/contentCardView"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.scwang.smart.refresh.layout.SmartRefreshLayout
                    android:id="@+id/refreshLayout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.scwang.smart.refresh.header.MaterialHeader
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/orderListRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:clipToPadding="false"
                        android:overScrollMode="never"
                        android:paddingBottom="16dp"
                        android:scrollbars="none"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="10"
                        tools:listitem="@layout/accept_ordered_item" />

                    <com.scwang.smart.refresh.footer.ClassicsFooter
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </com.scwang.smart.refresh.layout.SmartRefreshLayout>
            </androidx.cardview.widget.CardView>

            <ProgressBar
                android:id="@+id/pbOrderedList"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutMenu"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:paddingStart="6dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/layoutPendingList"
            app:layout_constraintTop_toBottomOf="@id/layoutTopPart"
            app:layout_constraintWidth_percent="0.5">

            <!--            <FrameLayout-->
            <!--                android:id="@+id/layoutTableID"-->
            <!--                android:layout_width="0dp"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginEnd="10dp"-->
            <!--                app:layout_constraintEnd_toStartOf="@id/cardStatus"-->
            <!--                app:layout_constraintStart_toStartOf="parent"-->
            <!--                app:layout_constraintTop_toTopOf="parent">-->

            <!--                <TextView-->
            <!--                    android:id="@+id/tvTableID"-->
            <!--                    style="@style/FontLocalization"-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:ellipsize="end"-->
            <!--                    android:maxLines="1"-->
            <!--                    android:text=""-->
            <!--                    android:textColor="@color/black"-->
            <!--                    android:textSize="24sp"-->
            <!--                    android:textStyle="bold"-->
            <!--                    tools:text="@tools:sample/lorem/random" />-->
            <!--            </FrameLayout>-->

            <!--            <androidx.cardview.widget.CardView-->
            <!--                android:id="@+id/cardStatus"-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:elevation="0dp"-->
            <!--                app:cardBackgroundColor="@color/confirm_backgroud_color"-->
            <!--                app:cardCornerRadius="5dp"-->
            <!--                app:cardElevation="0dp"-->
            <!--                app:layout_constraintEnd_toEndOf="parent"-->
            <!--                app:layout_constraintHorizontal_bias="1"-->
            <!--                app:layout_constraintStart_toEndOf="@id/layoutTableID"-->
            <!--                app:layout_constraintTop_toTopOf="parent">-->

            <!--                <TextView-->
            <!--                    android:id="@+id/imgDelete"-->
            <!--                    style="@style/FontLocalization"-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:gravity="center_vertical|end"-->
            <!--                    android:maxLines="1"-->
            <!--                    android:paddingHorizontal="10dp"-->
            <!--                    android:paddingVertical="5dp"-->
            <!--                    android:text=""-->
            <!--                    android:textColor="@color/confirm_text_color"-->
            <!--                    android:textSize="@dimen/_16ssp"-->
            <!--                    android:textStyle="bold" />-->
            <!--            </androidx.cardview.widget.CardView>-->

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginBottom="6dp"
                android:background="@drawable/background_white_radius_12dp"
                android:orientation="vertical"
                android:padding="6dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/orderedInfoRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="8"
                    tools:listitem="@layout/pending_order_menu_item" />

            </LinearLayout>


            <ProgressBar
                android:id="@+id/pbOrderedInfo"
                android:layout_width="25dp"
                android:layout_height="25dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <include
            android:id="@+id/layoutEmptyList"
            layout="@layout/layout_empty_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutResumeOrder"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/layoutFirst"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/layoutDetailInfo"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginVertical="6dp"
            android:layout_marginEnd="6dp"
            android:background="@drawable/background_white_radius_12dp"
            android:orientation="vertical"
            android:paddingBottom="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">


            <include
                android:id="@+id/layoutOrderInfo"
                layout="@layout/layout_order_info"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@drawable/background_dialog"
                android:orientation="vertical" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="10dp">

                <LinearLayout
                    android:id="@+id/btnCancelOrder"
                    android:layout_width="wrap_content"
                    android:layout_height="50dp"
                    android:background="@drawable/button_outline_background_enable"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:minWidth="100dp"
                    android:orientation="horizontal"
                    android:paddingHorizontal="20dp"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/no_accept_order"
                        android:textColor="@color/primaryColor"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/btnReceiving"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:background="@drawable/button_login_background"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvAutoCancelTime"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/receiving_orders"
                        android:textColor="@color/mainWhite"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <ProgressBar
                        android:id="@+id/pbSubmit"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_marginStart="10dp"
                        android:indeterminate="true"
                        android:indeterminateTint="@color/mainWhite"
                        android:indeterminateTintMode="src_atop"
                        android:progressTint="@color/mainWhite"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>
            </LinearLayout>


        </LinearLayout>


        <ProgressBar
            android:id="@+id/pbOrderedInfo2"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <include
            android:id="@+id/layoutEmptyDetail"
            layout="@layout/layout_empty_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>