<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/background_radius_16_dialog"
    android:orientation="vertical"
    android:paddingHorizontal="10dp"
    android:paddingTop="10dp"
    android:paddingBottom="12dp"
    android:layout_marginBottom="10dp"
    app:layout_constraintBottom_toTopOf="@id/llMenu"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@id/layoutTableID">

    <TextView
        android:id="@+id/tvTitle"
        style="@style/FontLocalization"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/add_good_info"
        android:textColor="@color/black60"
        android:textSize="@dimen/_14ssp" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/receiverInfoRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:itemCount="1"
        tools:listitem="@layout/pending_order_menu_item" />


    <LinearLayout
        android:id="@+id/llCancelTime"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        tools:visibility="visible">

        <TextView
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="15dp"
            android:text="@string/cancel_time"
            android:textColor="@color/black60"
            android:textSize="@dimen/_14ssp" />

        <TextView
            android:id="@+id/tvCancelTime"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="end"
            android:maxLines="2"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            android:textStyle="bold"
            tools:text="2024/04/02 12:00:00" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/llCancelReason"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="15dp"
            android:text="@string/cancel_reason"
            android:textColor="@color/black60"
            android:textSize="@dimen/_14ssp" />


<!--        <com.metathought.food_order.casheir.ui.widget.ExpandTextView-->
        <!--            android:id="@+id/tvCancelReason"-->
        <!--            style="@style/FontLocalization"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_weight="1"-->
        <!--            android:gravity="top|end"-->
        <!--            android:textColor="@color/black"-->
        <!--            android:textSize="@dimen/_14ssp"-->
        <!--            android:textStyle="bold"-->
        <!--            android:visibility="visible" />-->
        <TextView
            android:id="@+id/tvCancelReason"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="top|end"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            android:textStyle="bold"
            android:visibility="visible" />

    </LinearLayout>


</LinearLayout>
