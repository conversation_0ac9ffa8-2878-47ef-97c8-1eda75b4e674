<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"


    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:ignore="UseCompoundDrawables">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:text="@string/pay_by_balance"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp">

            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutPhoneNumber"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/hint_phone_number_required"
                    android:textColorHint="@color/bg_progress"
                    app:startIconDrawable="@drawable/ic_rectangle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtPhoneNumber"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:inputType="phone"
                        android:maxLength="14"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.hbb20.CountryCodePicker
                    android:id="@+id/countryCodeHolder"
                    style="@style/FontLocalization"
                    android:layout_width="70dp"
                    android:layout_height="50dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="3dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:ccpDialog_keyboardAutoPopup="false"
                    app:ccp_defaultPhoneCode="855"
                    app:ccp_showFlag="false"
                    app:ccp_showNameCode="false"
                    app:ccp_textSize="15sp"
                    app:layout_constraintStart_toStartOf="parent" />
            </FrameLayout>

            <LinearLayout
                android:id="@+id/btnSearch"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="10dp"
                android:layout_marginTop="3dp"
                android:background="@drawable/button_login_background"
                android:clickable="false"
                android:enabled="false"
                android:focusable="false"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="25dp">

                <ImageView
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:indeterminate="true"
                    android:indeterminateTint="@color/mainWhite"
                    android:indeterminateTintMode="src_atop"
                    android:progressTint="@color/mainWhite"
                    android:src="@drawable/ic_search"
                    app:tint="@color/white" />
            </LinearLayout>
        </LinearLayout>


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:background="@drawable/background_dialog_info"
            android:orientation="vertical"
            android:padding="16dp">


            <TextView
                android:id="@+id/tvTitle"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/detail"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/layoutInfor"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/tvTitle"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"

                        android:text="@string/member_name_with_colon"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tvMemberName"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:gravity="end"
                        android:maxLines="3"
                        android:text="---"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/my_balance_with_colon"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tvBalance"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:gravity="end"
                        android:singleLine="true"
                        android:text="---"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/amount_payable_with_colon"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tvTotal"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:gravity="end"
                        android:singleLine="true"
                        android:text="---"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </LinearLayout>
            </LinearLayout>

            <ProgressBar
                android:id="@+id/pbLoadMember"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_below="@id/tvTitle"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:layout_marginStart="10dp"
                android:indeterminate="true"
                android:indeterminateTint="@color/mainWhite"
                android:indeterminateTintMode="src_atop"
                android:progressTint="@color/mainWhite"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvNotFound"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:text="@string/user_not_found"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:visibility="gone"
                tools:visibility="visible" />
        </RelativeLayout>


        <LinearLayout
            android:id="@+id/btnPay"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="30dp"
            android:background="@drawable/button_login_background"
            android:clickable="false"
            android:enabled="false"
            android:focusable="false"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/pay"
                android:textColor="@color/mainWhite"
                android:textSize="18sp" />

        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
