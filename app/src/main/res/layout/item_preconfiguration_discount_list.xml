<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="86dp"
    android:layout_marginBottom="10dp"
    android:background="@drawable/background_white_radius_12dp"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvUnit"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="2dp"
                android:gravity="center"
                android:text="$"
                android:textColor="@color/primaryColor"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvAmount"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/primaryColor"
                android:textSize="20sp"
                tools:text="9.99" />

            <TextView
                android:id="@+id/tvPercent"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:gravity="center"
                android:text="% 0FF"
                android:textColor="@color/primaryColor"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                android:visibility="gone" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvLimitDesc"
            style="@style/FontLocalization"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center"
            android:textColor="@color/black60"
            android:textSize="@dimen/_12ssp"
            android:textStyle="bold"
            tools:text="$99" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvName"
            style="@style/FontLocalization"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="2"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp"
            android:textStyle="bold"
            tools:text="商品名称" />

        <TextView
            android:id="@+id/tvMaxDiscount"
            style="@style/FontLocalization"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="start"
            android:textColor="@color/black60"
            android:textSize="@dimen/_12ssp"
            android:textStyle="bold"
            tools:text="商品名称" />
    </LinearLayout>


    <ImageView
        android:id="@+id/checkbox"
        style="@style/FontLocalization"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_gravity="center"
        android:layout_marginEnd="16dp"
        android:scaleType="centerCrop"
        android:src="@drawable/coupon_checkbox_circle_drawable"
        android:visibility="visible" />


</LinearLayout>