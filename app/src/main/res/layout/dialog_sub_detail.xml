<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_percent="0.8"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.85">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginBottom="10dp"
            android:gravity="center_vertical"
            android:paddingHorizontal="16dp"
            tools:ignore="UseCompoundDrawables">

            <TextView
                android:id="@+id/tvDishedName"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="start"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold"
                tools:text="Item Detail" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <include
            android:id="@+id/layoutReachLimitPurchase"
            layout="@layout/reach_limit_purchase_layout" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never"
            android:scrollbars="none"
            android:layout_weight="1"
            android:paddingHorizontal="40dp"
            android:paddingTop="@dimen/_10ssp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />


        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnConfirm"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_gravity="bottom"
            android:layout_marginStart="24dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="24dp"
            android:text="@string/confirmed"
            android:textSize="16sp"
            android:textStyle="bold"
            android:visibility="gone"
            app:cornerRadius="6dp" />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>