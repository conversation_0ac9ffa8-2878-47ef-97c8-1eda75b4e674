<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/background_e7e7e7_radius_20dp"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:dialog_title="@string/merge_order" />

        <LinearLayout
            android:id="@+id/layoutTopPart"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="24dp">

            <LinearLayout
                android:id="@+id/dropdownFilter"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginEnd="20dp"
                android:layout_weight="1"
                android:background="@drawable/background_white_radius_100"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/dropdownTable"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tvFilter"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="start"
                    android:text="@string/all_order"
                    android:textColor="@color/black"
                    android:textSize="12sp" />

                <ImageView
                    android:id="@+id/arrowFilter"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:src="@drawable/ic_dropdown"
                    android:visibility="visible"
                    tools:ignore="ContentDescription" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/dropdownTable"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:background="@drawable/background_white_radius_100"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tvClearFilter"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.49">

                <TextView
                    android:id="@+id/tvTable"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:maxLines="1"
                    android:text="@string/all_table"
                    android:textColor="@color/black"
                    android:textSize="12sp"
                    tools:text="" />

                <androidx.cardview.widget.CardView
                    android:id="@+id/cardViewFilterTable"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:elevation="0dp"
                    android:visibility="gone"
                    app:cardBackgroundColor="@color/main_red"
                    app:cardCornerRadius="12.5dp"
                    app:cardElevation="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1"
                    app:layout_constraintStart_toEndOf="@id/tvTableID"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvCount"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="25dp"
                        android:gravity="center_vertical|end"
                        android:maxLines="1"
                        android:paddingHorizontal="8dp"
                        android:paddingVertical="3dp"
                        android:text="+1"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold" />
                </androidx.cardview.widget.CardView>

                <ImageView
                    android:id="@+id/arrowTable"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:src="@drawable/ic_dropdown"
                    android:visibility="visible"
                    tools:ignore="ContentDescription" />
            </LinearLayout>

            <TextView
                android:id="@+id/tvClearFilter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="10dp"
                android:text="@string/clear_filter"
                android:textColor="@color/primaryColor"
                android:textSize="@dimen/_14ssp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


        </LinearLayout>

        <TextView
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="@string/main_order"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            android:textStyle="bold" />

        <include
            android:id="@+id/layoutOrder"
            layout="@layout/merge_ordered_item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <TextView
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="10dp"
            android:text="@string/mergeable_order"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            android:textStyle="bold" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvList"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:scrollbars="none"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/merge_ordered_item" />
        <include
            android:id="@+id/layoutEmptyDetail"
            layout="@layout/layout_empty_data"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:visibility="gone"
            android:layout_gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />
        <LinearLayout
            android:id="@+id/btnMerge"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/button_login_background"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:orientation="horizontal"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tvMerge"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/merge_order"
                android:textColor="@color/mainWhite"
                android:textSize="18sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>


</LinearLayout>
