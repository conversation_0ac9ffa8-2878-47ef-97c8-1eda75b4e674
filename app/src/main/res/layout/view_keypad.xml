<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 第一行 - 4个TextView -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="6dp"
        android:layout_weight="1"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/gridRow1Col1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp" />

        <TextView
            android:id="@+id/gridRow1Col2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="2"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp" />

        <TextView
            android:id="@+id/gridRow1Col3"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="3"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp" />

        <!--        <TextView-->
        <!--            android:id="@+id/gridRow1Col4"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="match_parent"-->
        <!--            android:layout_weight="1"-->
        <!--            android:background="@drawable/background_white_radius_4dp"-->
        <!--            android:clickable="true"-->
        <!--            android:focusable="true"-->
        <!--            android:gravity="center"-->
        <!--            android:text="+"-->
        <!--            android:textColor="@color/black"-->
        <!--            android:textSize="16sp" />-->
        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp">

            <TextView
                android:id="@+id/gridRow1Col4"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="100000"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/ivEdit1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|right"
                android:padding="5dp"
                android:src="@drawable/icon_weight_edit" />
        </FrameLayout>

    </LinearLayout>

    <!-- 第二行 - 4个TextView -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="6dp"
        android:layout_weight="1"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/gridRow2Col1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="4"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp" />

        <TextView
            android:id="@+id/gridRow2Col2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="5"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp" />

        <TextView
            android:id="@+id/gridRow2Col3"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="6"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp" />

        <!--        <TextView-->
        <!--            android:id="@+id/gridRow2Col4"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="match_parent"-->
        <!--            android:layout_weight="1"-->
        <!--            android:background="@drawable/background_white_radius_4dp"-->
        <!--            android:clickable="true"-->
        <!--            android:focusable="true"-->
        <!--            android:gravity="center"-->
        <!--            android:text="-"-->
        <!--            android:textColor="@color/black"-->
        <!--            android:textSize="16sp" />-->
        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp">

            <TextView
                android:id="@+id/gridRow2Col4"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="50000"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/ivEdit2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|right"
                android:padding="5dp"
                android:src="@drawable/icon_weight_edit" />
        </FrameLayout>

    </LinearLayout>

    <!-- 第三行 - 4个TextView -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="6dp"
        android:layout_weight="1"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/gridRow3Col1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="7"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp" />

        <TextView
            android:id="@+id/gridRow3Col2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="8"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp" />

        <TextView
            android:id="@+id/gridRow3Col3"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="9"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp" />

        <!--        <TextView-->
        <!--            android:id="@+id/gridRow3Col4"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="match_parent"-->
        <!--            android:layout_weight="1"-->
        <!--            android:background="@drawable/background_white_radius_4dp"-->
        <!--            android:clickable="true"-->
        <!--            android:focusable="true"-->
        <!--            android:gravity="center"-->
        <!--            android:text="×"-->
        <!--            android:textColor="@color/black"-->
        <!--            android:textSize="16sp" />-->
        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp">

            <TextView
                android:id="@+id/gridRow3Col4"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="10000"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/ivEdit3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|right"
                android:padding="5dp"
                android:src="@drawable/icon_weight_edit" />
        </FrameLayout>

    </LinearLayout>

    <!-- 新增第四行（原第四行变为第五行） -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="6dp"
        android:layout_weight="1"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/gridRow4Col1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="."
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp" />

        <TextView
            android:id="@+id/gridRow4Col2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="0"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp" />

        <!--        <TextView-->
        <!--            android:id="@+id/gridRow4Col3"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="match_parent"-->
        <!--            android:layout_marginEnd="6dp"-->
        <!--            android:layout_weight="1"-->
        <!--            android:background="@drawable/background_white_radius_4dp"-->
        <!--            android:clickable="true"-->
        <!--            android:focusable="true"-->
        <!--            android:gravity="center"-->
        <!--            android:text="("-->
        <!--            android:textColor="@color/black"-->
        <!--            android:textSize="16sp" />-->
        <FrameLayout
            android:id="@+id/gridRow4Col3"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@drawable/icon_keyboard_delete" />
        </FrameLayout>

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/background_white_radius_4dp">

            <TextView
                android:id="@+id/gridRow4Col4"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="5000"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/ivEdit4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|right"
                android:padding="5dp"
                android:src="@drawable/icon_weight_edit" />
        </FrameLayout>

        <!--        <TextView-->
        <!--            android:id="@+id/gridRow4Col4"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="match_parent"-->
        <!--            android:layout_weight="1"-->
        <!--            android:background="@drawable/background_white_radius_4dp"-->
        <!--            android:clickable="true"-->
        <!--            android:focusable="true"-->
        <!--            android:gravity="center"-->
        <!--            android:text=")"-->
        <!--            android:textColor="@color/black"-->
        <!--            android:textSize="16sp" />-->


    </LinearLayout>

    <!-- 第五行 - 2个TextView (1格 + 3格) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/gridRow5Col1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="6dp"
            android:layout_weight="0.985"
            android:background="@drawable/background_white_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="@string/clear_input"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp" />

        <TextView
            android:id="@+id/gridRow5Col2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:background="@drawable/background_primary_color_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:text="@string/confirm2"
            android:textColor="@color/white"
            android:textSize="@dimen/_16ssp" />

    </LinearLayout>

</LinearLayout>
