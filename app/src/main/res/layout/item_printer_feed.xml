<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvFeedName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:textColor="@color/black"
        android:textSize="@dimen/_printer_default_sp"
        tools:text="Large Normal ice, Normal sugar" />

    <LinearLayout
        android:id="@+id/llBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/llItemIndex"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.6"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

        </LinearLayout>

        <LinearLayout
            android:id="@+id/llFeedName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_weight="1.2"
            android:orientation="vertical"
            android:textColor="@color/black50"
            android:textSize="14sp"
            android:visibility="invisible"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tvFeedNameEn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_printer_default_sp"
                tools:text="Large Normal ice, Normal sugar" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/llFeedFoodCount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvFeedFoodCount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:gravity="center_horizontal"
                android:textColor="@color/black"
                android:textSize="@dimen/_printer_default_sp"
                tools:text="x1" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/llFeedFoodPrice"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_weight="1.4"
            android:gravity="center"
            android:orientation="vertical"
            android:text="@string/items"
            android:textColor="@color/black50"
            android:textSize="14sp"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tvFeedFoodPrice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:textColor="@color/black"
                android:textSize="@dimen/_printer_default_sp"
                tools:text="$9.9" />

        </LinearLayout>

<!--        <LinearLayout-->
<!--            android:id="@+id/llDiscountPrice"-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_weight="1.4"-->
<!--            android:orientation="vertical"-->
<!--            android:visibility="gone"-->
<!--            tools:visibility="visible">-->

<!--        </LinearLayout>-->

        <LinearLayout
            android:id="@+id/llFeedFoodTotal"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_weight="1.4"
            android:gravity="center"
            android:orientation="vertical"
            android:text="@string/items"
            android:textColor="@color/black50"
            android:textSize="14sp"
            tools:visibility="visible">


        </LinearLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/tvFeedNameKm"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:ellipsize="end"
        android:textColor="@color/black"
        android:textSize="@dimen/_printer_default_sp"
        android:visibility="gone"
        tools:text="Large Normal ice, Normal sugar"
        tools:visibility="visible" />

</LinearLayout>
