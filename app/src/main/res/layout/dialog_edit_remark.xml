<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutMain"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:padding="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:dialog_title="@string/remark"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/llGoodInfo"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@drawable/background_efefef_radius_12"
            android:orientation="horizontal"
            android:paddingHorizontal="10dp"
            android:paddingVertical="16dp"
            app:layout_constraintBottom_toTopOf="@id/edtRemark"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/topBar">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scrollbars="none"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHeight_max="200dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:id="@+id/layoutContent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="top"
                        android:layout_marginEnd="2dp"
                        android:layout_weight="1.8"
                        android:orientation="vertical"
                        android:text="@string/items"
                        android:textColor="@color/black50"
                        android:textSize="@dimen/_14ssp">

                        <TextView
                            android:id="@+id/tvName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:ellipsize="end"
                            android:maxLines="2"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            tools:text="Lemonade" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tvTmpSign"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="5dp"
                                android:ellipsize="end"
                                android:maxLines="1"
                                android:paddingHorizontal="4dp"
                                android:paddingVertical="2dp"
                                android:text="@string/temporary"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_11ssp"
                                android:visibility="gone"
                                tools:visibility="visible" />

                            <TextView
                                android:id="@+id/tvDiscountActivity"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:ellipsize="end"
                                android:maxLines="1"
                                android:paddingHorizontal="4dp"
                                android:paddingVertical="2dp"
                                android:textColor="@color/primaryColor"
                                android:textSize="@dimen/_11ssp"
                                android:visibility="gone"
                                tools:text="第二份半价"
                                tools:visibility="visible" />

                        </LinearLayout>


                        <TextView
                            android:id="@+id/tvSpecification"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:ellipsize="end"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp"
                            android:visibility="gone"
                            tools:text="Large Normal ice, Normal sugar"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="top"
                        android:layout_marginEnd="2dp"
                        android:layout_weight="0.5"
                        android:gravity="end">

                        <TextView
                            android:id="@+id/tvQTY"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="5dp"
                            android:maxLength="4"
                            android:paddingHorizontal="5dp"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            tools:text="10" />
                    </LinearLayout>

                    <include
                        android:id="@+id/layoutPrice"
                        layout="@layout/layout_item_price_view"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1" />
                    <!--                    <LinearLayout-->
                    <!--                        android:layout_width="0dp"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_gravity="top"-->
                    <!--                        android:layout_weight="1"-->
                    <!--                        android:gravity="end"-->
                    <!--                        android:orientation="vertical">-->

                    <!--                        <TextView-->
                    <!--                            android:id="@+id/tvFoodPrice"-->
                    <!--                            android:layout_width="wrap_content"-->
                    <!--                            android:layout_height="wrap_content"-->
                    <!--                            android:layout_gravity="end"-->
                    <!--                            android:ellipsize="end"-->
                    <!--                            android:maxLines="2"-->
                    <!--                            android:textColor="@color/black"-->
                    <!--                            android:textSize="@dimen/_16ssp"-->
                    <!--                            tools:text="$9.9" />-->

                    <!--                        <TextView-->
                    <!--                            android:id="@+id/tvWeight"-->
                    <!--                            android:layout_width="wrap_content"-->
                    <!--                            android:layout_height="wrap_content"-->
                    <!--                            android:layout_gravity="end"-->
                    <!--                            android:ellipsize="end"-->
                    <!--                            android:maxLines="2"-->
                    <!--                            android:textColor="@color/black"-->
                    <!--                            android:textSize="@dimen/_16ssp"-->
                    <!--                            android:visibility="gone"-->
                    <!--                            tools:text="$9.9"-->
                    <!--                            tools:visibility="visible" />-->

                    <!--                        <TextView-->
                    <!--                            android:id="@+id/tvVipPrice"-->
                    <!--                            style="@style/FontLocalization"-->
                    <!--                            android:layout_width="wrap_content"-->
                    <!--                            android:layout_height="wrap_content"-->
                    <!--                            android:layout_marginStart="2dp"-->
                    <!--                            android:layout_marginTop="4dp"-->
                    <!--                            android:drawablePadding="2dp"-->
                    <!--                            android:textColor="@color/member_price_color"-->
                    <!--                            android:textSize="12sp"-->
                    <!--                            android:textStyle="bold"-->
                    <!--                            app:drawableStartCompat="@drawable/icon_vip"-->
                    <!--                            tools:text="$0.00" />-->

                    <!--                        &lt;!&ndash;                    <TextView&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        android:id="@+id/tvOriginalPrice"&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        android:layout_width="wrap_content"&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        android:layout_height="wrap_content"&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        android:layout_gravity="center_horizontal"&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        android:layout_marginTop="4dp"&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        android:ellipsize="end"&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        android:foreground="@drawable/strike_price"&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        android:maxLines="3"&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        android:textColor="@color/black60"&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        android:textSize="@dimen/_12ssp"&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        android:visibility="gone"&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        tools:text="12.99"&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        tools:visibility="visible" />&ndash;&gt;-->

                    <!--                        &lt;!&ndash;                    <ImageView&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        android:id="@+id/ivWarn"&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        android:layout_width="wrap_content"&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        android:layout_height="wrap_content"&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        android:src="@drawable/icon_warn_green"&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        android:visibility="gone"&ndash;&gt;-->
                    <!--                        &lt;!&ndash;                        tools:visibility="visible" />&ndash;&gt;-->
                    <!--                    </LinearLayout>-->
                </LinearLayout>
            </ScrollView>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <EditText
            android:id="@+id/edtRemark"
            android:layout_width="470dp"
            android:layout_height="140dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/background_white_border_adb4d2_radius_10dp"
            android:gravity="top"
            android:hint="@string/please_input_mark"
            android:lineSpacingExtra="5dp"
            android:maxLength="140"
            android:padding="10dp"
            android:textColor="@color/black80"
            android:textColorHint="@color/color_ffADB4d2"
            android:textSize="@dimen/_14ssp"
            app:layout_constraintBottom_toTopOf="@id/btnYes"
            app:layout_constraintEnd_toStartOf="@id/rvList"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/llGoodInfo" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvList"
            android:layout_width="470dp"
            android:layout_height="0dp"
            android:layout_marginStart="24dp"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:layout_constraintBottom_toBottomOf="@id/edtRemark"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/edtRemark"
            app:layout_constraintTop_toTopOf="@id/edtRemark"
            tools:itemCount="2"
            tools:listitem="@layout/quick_remark_item" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnYes"
            style="@style/FontLocalization"
            android:layout_width="470dp"
            android:layout_height="50dp"
            android:layout_marginTop="40dp"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:orientation="horizontal"
            android:text="@string/confirm2"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/_18ssp"
            android:textStyle="bold"
            app:backgroundTint="@color/primaryColor"
            app:cornerRadius="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/edtRemark"
            app:strokeColor="@color/primaryColor"
            app:strokeWidth="0dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>
