<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="60dp"

    android:layout_gravity="center_vertical"
    android:paddingStart="10dp"
    android:orientation="horizontal">

    <TextView

        android:id="@+id/btnDetail"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:gravity="center"

        android:text="@string/detail"
        android:textColor="@color/primaryColor"
        android:textSize="@dimen/_14ssp" />

    <TextView
        android:id="@+id/btnCancel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:gravity="center"
        android:text="@string/cancel_top_up"
        android:textColor="@color/red_clear_data"
        android:textSize="@dimen/_14ssp" />

</LinearLayout>
