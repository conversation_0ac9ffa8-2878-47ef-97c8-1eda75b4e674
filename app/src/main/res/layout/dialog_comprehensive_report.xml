<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="24dp"
            app:dialog_title="@string/comprehensive_report" />

        <!-- 搜索条件行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:weightSum="4">

            <!-- 订单类型 -->
            <LinearLayout
                android:id="@+id/dropdownOrderType"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:background="@drawable/background_white_border_black12_radius_100"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="10dp">

                <TextView
                    android:id="@+id/tvOrderType"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:maxLines="1"
                    android:text="@string/order_type"
                    android:textColor="@color/black20"
                    android:textSize="@dimen/_14ssp" />


                <ImageView
                    android:id="@+id/arrowOrderType"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:padding="4dp"
                    android:src="@drawable/ic_dropdown"
                    android:visibility="visible"
                    tools:ignore="ContentDescription" />
            </LinearLayout>

            <!-- 合并类型 -->
            <LinearLayout
                android:id="@+id/dropdownMergeType"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:background="@drawable/background_white_border_black12_radius_100"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="10dp">

                <TextView
                    android:id="@+id/tvMergeType"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:maxLines="1"
                    android:text="合并时间"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />

                <ImageView
                    android:id="@+id/arrowMergeType"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:src="@drawable/ic_dropdown"
                    android:visibility="visible"
                    tools:ignore="ContentDescription" />
            </LinearLayout>

            <!-- 快捷时间选择 -->
            <LinearLayout
                android:id="@+id/dropdownQuickTime"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:background="@drawable/background_white_border_black12_radius_100"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="10dp">

                <TextView
                    android:id="@+id/tvQuickTime"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:maxLines="1"
                    android:text="今天"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />

                <ImageView
                    android:id="@+id/arrowQuickTime"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:src="@drawable/ic_dropdown"
                    android:visibility="visible"
                    tools:ignore="ContentDescription" />
            </LinearLayout>

            <!-- 时间选择 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:paddingHorizontal="4dp">

                <com.metathought.food_order.casheir.ui.widget.CalendarTextView
                    android:id="@+id/tvCalendar"
                    style="@style/commonCalendarTextViewStyle"
                    android:layout_width="match_parent"
                    android:background="@drawable/background_white_border_black12_radius_100"
                    app:layout_constraintStart_toEndOf="@id/dropdownFilter"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="01 03, 2024 - 01 03, 2024" />
            </LinearLayout>

            <TextView
                android:id="@+id/btnReset"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="10dp"
                android:background="@drawable/background_white_border_black12_radius_100"
                android:gravity="center"
                android:paddingHorizontal="16dp"
                android:text="@string/reset"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp" />

            <TextView
                android:id="@+id/btnSearch"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:background="@drawable/background_primary_color_radius_100dp"
                android:gravity="center"
                android:paddingHorizontal="16dp"
                android:text="@string/search"
                android:textColor="@color/white"
                android:textSize="@dimen/_14ssp" />
        </LinearLayout>

        <!-- 新增打印和导出按钮行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="32dp"
            android:layout_marginTop="16dp"
            android:gravity="start"
            android:orientation="horizontal"
            android:paddingHorizontal="24dp">

            <TextView
                android:id="@+id/tvPrint"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:background="@drawable/background_white_border_primary_radius_100"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:paddingHorizontal="16dp"
                android:text="@string/printer"
                android:textColor="@color/primaryColor"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvExport"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="10dp"
                android:background="@drawable/background_white_border_primary_radius_100"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:paddingHorizontal="16dp"
                android:text="@string/export"
                android:textColor="@color/primaryColor"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

        </LinearLayout>
        <!-- 这里添加综合报表的具体内容 -->
        <!--        <TextView-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_marginTop="16dp"-->
        <!--            android:text="@string/comprehensive_report_content" />-->
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
