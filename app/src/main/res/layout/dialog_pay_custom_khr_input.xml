<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_dialog"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    android:paddingHorizontal="24dp">

    <com.metathought.food_order.casheir.ui.widget.DialogTopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="25dp"
        app:dialog_title="@string/modify_amount" />


    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/textInputLayoutAmount"
        style="@style/CustomOutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:hint="@string/amount_require"
        android:orientation="horizontal"
        app:startIconDrawable="@drawable/icon_km_unit"
        app:startIconTint="@color/black">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edtAmount"
            style="@style/FontLocalization"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:cursorVisible="false"
            android:gravity="center_vertical"
            android:inputType="numberDecimal"
            android:maxLines="1"
            android:paddingVertical="8dp"
            android:paddingStart="35dp"
            android:singleLine="true"
            android:textColor="@color/black"
            android:textColorHint="@color/black40"
            android:textSize="15sp" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.metathought.food_order.casheir.ui.widget.CustomNumberKeyBoardView2
        android:id="@+id/viewKeyBoard"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="12dp"
        android:layout_weight="1" />

    <TextView
        android:id="@+id/tvConfirm"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="42dp"
        android:layout_marginBottom="24dp"
        android:background="@drawable/button_login_background"
        android:gravity="center"
        android:text="@string/confirm2"
        android:textColor="@color/white"
        android:textSize="@dimen/_20ssp" />


</LinearLayout>