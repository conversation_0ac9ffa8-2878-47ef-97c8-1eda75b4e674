<?xml version="1.0" encoding="utf-8"?><!--<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"-->
<!--    xmlns:app="http://schemas.android.com/apk/res-auto"-->
<!--    xmlns:tools="http://schemas.android.com/tools"-->
<!--    android:layout_width="wrap_content"-->
<!--    android:layout_height="match_parent"-->
<!--    app:cardCornerRadius="6dp"-->
<!--    app:cardElevation="0dp"-->
<!--    android:layout_marginEnd="4dp"-->
<!--    app:strokeColor="@android:color/transparent">-->

<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/tvValue"
    style="@style/FontLocalization"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:layout_marginEnd="4dp"
    android:background="@drawable/background_white_radius_6dp"
    android:ellipsize="end"
    android:gravity="center"
    android:maxLines="1"
    android:paddingHorizontal="12dp"
    android:textColor="@color/black"
    android:textSize="14sp"
    tools:text="Floor1" />

    <!--</com.google.android.material.card.MaterialCardView>-->