<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_table_fragment"
    tools:context=".ui.table.TableFragment">

    <RadioGroup
        android:id="@+id/radioGroupFilter"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:background="@drawable/round_background_25dp"
        android:checkedButton="@id/radioAll"
        android:orientation="horizontal"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RadioButton
            android:id="@+id/radioOverview"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:checked="true"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="15dp"
            android:text="@string/overview"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />

        <RadioButton
            android:id="@+id/radioMember"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="15dp"
            android:text="@string/customer"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />

        <RadioButton
            android:id="@+id/radioBalance"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="15dp"
            android:text="@string/details"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />

        <RadioButton
            android:id="@+id/radioCredit"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="15dp"
            android:text="@string/credit"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />
    </RadioGroup>

    <!--    <EditText-->
    <!--        android:id="@+id/edtSearch"-->
    <!--        style="@style/commonSearchStyle"-->
    <!--        android:layout_marginEnd="6dp"-->
    <!--        android:hint="@string/search"-->
    <!--        android:maxWidth="300dp"-->
    <!--        android:minWidth="200dp"-->
    <!--        app:layout_constraintEnd_toStartOf="@id/tvCalendar"-->
    <!--        app:layout_constraintTop_toTopOf="@id/radioGroupFilter"-->
    <!--        tools:ignore="Autofill" />-->

    <TextView
        android:id="@+id/tvViewMember"
        style="@style/FontLocalization"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginEnd="8dp"
        android:paddingHorizontal="11dp"
        android:gravity="center_vertical"
        android:text="@string/only_view_member"
        android:textColor="@drawable/selector_view_member_text"
        android:background="@drawable/selector_background_view_member"
        android:textSize="@dimen/_14ssp"
        app:layout_constraintEnd_toStartOf="@id/edtSearch"
        app:layout_constraintTop_toTopOf="@id/radioGroupFilter" />

    <com.metathought.food_order.casheir.ui.widget.CustomSearchView
        android:id="@+id/edtSearch"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="6dp"
        app:layout_constraintEnd_toStartOf="@id/tvCalendar"
        app:layout_constraintTop_toTopOf="@id/radioGroupFilter"
        app:search_hint="@string/search" />

    <com.metathought.food_order.casheir.ui.widget.CalendarTextView
        android:id="@+id/tvCalendar"
        style="@style/commonCalendarTextViewStyle"
        android:layout_marginStart="6dp"
        app:layout_constraintEnd_toStartOf="@id/tvClearFilter"
        app:layout_constraintTop_toTopOf="@id/radioGroupFilter"
        tools:text="01 03, 2024 - 01 03, 2024" />

    <TextView
        android:id="@+id/tvClearFilter"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="6dp"
        android:drawablePadding="10dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="15dp"
        android:singleLine="true"
        android:text="@string/clear_filter"
        android:textColor="@color/primaryColor"
        android:textSize="@dimen/_16ssp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@id/tvAddMember"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tvAddMember"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginStart="6dp"
        android:background="@drawable/background_white_border_radius_100"
        android:drawableStart="@drawable/icon_add_green"
        android:drawablePadding="4dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="27dp"
        android:singleLine="true"
        android:text="@string/add_new"
        android:textColor="@color/primaryColor"
        android:textSize="@dimen/_16ssp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvCalendar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="6dp"
            android:background="@drawable/background_dialog"
            android:orientation="vertical"
            android:paddingHorizontal="16dp">

            <!-- 表头区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:orientation="horizontal">

                <!-- 左侧固定：客户昵称表头 -->
                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="200dp"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:paddingEnd="10dp"
                    android:text="@string/customer_nickname"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />

                <!-- 中间可滑动表头区域 -->
                <HorizontalScrollView
                    android:id="@+id/mainScrollView"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fadeScrollbars="false"
                    android:fillViewport="true"
                    android:overScrollMode="never"
                    android:scrollbarStyle="outsideOverlay"
                    android:scrollbars="none">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="140dp"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:paddingEnd="10dp"
                            android:text="@string/customer_member_number"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="140dp"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:paddingEnd="10dp"
                            android:text="@string/customer_account"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="120dp"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:paddingEnd="10dp"
                            android:text="@string/customer_balance"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="190dp"
                            android:layout_height="match_parent"
                            android:autoSizeMaxTextSize="@dimen/_14ssp"
                            android:autoSizeMinTextSize="@dimen/_10ssp"
                            android:autoSizeTextType="uniform"
                            android:gravity="center"
                            android:maxLines="2"
                            android:paddingEnd="10dp"
                            android:text="@string/register_date"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="190dp"
                            android:layout_height="match_parent"
                            android:autoSizeMaxTextSize="@dimen/_14ssp"
                            android:autoSizeMinTextSize="@dimen/_10ssp"
                            android:autoSizeTextType="uniform"
                            android:gravity="center"
                            android:maxLines="2"
                            android:paddingEnd="10dp"
                            android:text="@string/last_top_up_amount"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="120dp"
                            android:layout_height="match_parent"
                            android:autoSizeMaxTextSize="@dimen/_14ssp"
                            android:autoSizeMinTextSize="@dimen/_10ssp"
                            android:autoSizeTextType="uniform"
                            android:gravity="center"
                            android:maxLines="2"
                            android:paddingEnd="10dp"
                            android:text="@string/recharge_times"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="120dp"
                            android:layout_height="match_parent"
                            android:autoSizeMaxTextSize="@dimen/_14ssp"
                            android:autoSizeMinTextSize="@dimen/_10ssp"
                            android:autoSizeTextType="uniform"
                            android:gravity="center"
                            android:maxLines="2"
                            android:paddingEnd="10dp"
                            android:text="@string/consumption_closed"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp" />

                    </LinearLayout>
                </HorizontalScrollView>

                <!-- 右侧固定操作栏表头 -->
                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="200dp"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:paddingStart="10dp"
                    android:text="@string/operation"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />
            </LinearLayout>

            <View style="@style/commonDividerStyle" />

            <!-- 列表区域 - 包含刷新功能 -->
            <com.scwang.smart.refresh.layout.SmartRefreshLayout
                android:id="@+id/refreshLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/background_dialog">

                <com.scwang.smart.refresh.header.MaterialHeader
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <!-- 内容区域 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <!-- 左侧固定：客户昵称列 -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerviewMemberNames"
                        android:layout_width="200dp"
                        android:layout_height="wrap_content"
                        android:clipToPadding="false"
                        android:overScrollMode="never"
                        android:paddingBottom="16dp"
                        android:scrollbars="none"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                    <!-- 中间可滑动数据列表 -->
                    <com.metathought.food_order.casheir.ui.widget.SyncScrollRecyclerView
                        android:id="@+id/recyclerviewMember"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:clipToPadding="false"
                        android:overScrollMode="never"
                        android:paddingBottom="16dp"
                        android:scrollbars="none"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="1"
                        tools:listitem="@layout/member_list_items" />

                    <!-- 右侧操作按钮列表 -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerviewOperations"
                        android:layout_width="200dp"
                        android:layout_height="wrap_content"
                        android:clipToPadding="false"
                        android:overScrollMode="never"
                        android:paddingBottom="16dp"
                        android:scrollbars="none"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
                </LinearLayout>

                <com.scwang.smart.refresh.footer.ClassicsFooter
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        </LinearLayout>

        <include
            android:id="@+id/layoutEmpty"
            layout="@layout/layout_empty_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone"
            tools:visibility="visible" />
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
