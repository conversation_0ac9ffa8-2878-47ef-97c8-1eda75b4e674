<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingBottom="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="18dp"
            app:dialog_title="@string/select_table" />
        <!--        <LinearLayout-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_gravity="end"-->
        <!--            android:layout_marginBottom="10dp"-->
        <!--            android:gravity="end"-->
        <!--            tools:ignore="UseCompoundDrawables">-->

        <!--            <TextView-->
        <!--                style="@style/FontLocalization"-->
        <!--                android:layout_width="match_parent"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:layout_weight="1"-->
        <!--                android:gravity="start"-->
        <!--                android:text="@string/select_table"-->
        <!--                android:textColor="@color/black"-->
        <!--                android:textSize="24sp"-->
        <!--                android:textStyle="bold"-->
        <!--                tools:text="Select Table" />-->

        <!--            <ImageView-->
        <!--                android:id="@+id/btnClose"-->
        <!--                android:layout_width="40dp"-->
        <!--                android:layout_height="40dp"-->
        <!--                android:padding="5dp"-->
        <!--                android:src="@drawable/ic_cross_closed"-->
        <!--                tools:ignore="ContentDescription"-->
        <!--                android:visibility="gone"/>-->
        <!--        </LinearLayout>-->

        <!--        <View-->
        <!--            style="@style/commonDividerStyle" />-->

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewPage"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_gravity="center"
                    android:background="@drawable/background_language_spiner"
                    android:orientation="horizontal"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="8"
                    tools:listitem="@layout/floor_item" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewTable"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="24dp"
                    android:layout_weight="1"
                    android:clipToPadding="false"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="4"
                    tools:listitem="@layout/available_table_item" />
            </LinearLayout>


            <include
                android:id="@+id/layoutEmptyTable"
                layout="@layout/layout_empty_table"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />

            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />
        </FrameLayout>

        <!--        <View-->
        <!--            style="@style/commonDividerStyle"-->
        <!--            android:background="@color/black12"/>-->

        <!--        <TextView-->
        <!--            android:id="@+id/tvInfo"-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_gravity="center_horizontal"-->
        <!--            android:layout_marginTop="10dp"-->
        <!--            android:drawablePadding="10dp"-->
        <!--            android:text="@string/please_select_table_to_process_next"-->
        <!--            android:textColor="@color/black"-->
        <!--            app:drawableStartCompat="@drawable/ic_info" />-->

        <!--        <LinearLayout-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:gravity="center_horizontal|top"-->
        <!--            android:orientation="horizontal">-->

        <!--            <com.google.android.material.button.MaterialButton-->
        <!--                android:id="@+id/btnCancel"-->
        <!--                style="@style/CustomOutlinedBox"-->
        <!--                android:layout_width="160dp"-->
        <!--                android:layout_height="60dp"-->
        <!--                android:layout_marginTop="10dp"-->
        <!--                android:layout_marginEnd="10dp"-->
        <!--                android:clickable="true"-->
        <!--                android:focusable="true"-->
        <!--                android:gravity="center"-->
        <!--                android:orientation="horizontal"-->
        <!--                android:text="@string/cancel"-->
        <!--                android:textAllCaps="false"-->
        <!--                android:textColor="@color/black"-->
        <!--                android:textSize="@dimen/_18ssp"-->
        <!--                android:textStyle="bold"-->
        <!--                app:backgroundTint="@color/white"-->
        <!--                app:cornerRadius="15dp"-->
        <!--                app:layout_constraintEnd_toEndOf="parent"-->
        <!--                app:strokeColor="@color/black20"-->
        <!--                app:strokeWidth="1dp" />-->


        <!--            <com.google.android.material.button.MaterialButton-->
        <!--                android:id="@+id/btnConfirm"-->
        <!--                style="@style/CustomOutlinedBox"-->
        <!--                android:layout_width="160dp"-->
        <!--                android:layout_height="60dp"-->
        <!--                android:layout_marginTop="10dp"-->
        <!--                android:clickable="true"-->
        <!--                android:focusable="true"-->
        <!--                android:gravity="center"-->
        <!--                android:orientation="horizontal"-->
        <!--                android:text="@string/confirmed"-->
        <!--                android:textAllCaps="false"-->
        <!--                android:textColor="@color/white"-->
        <!--                android:textSize="@dimen/_18ssp"-->
        <!--                android:textStyle="bold"-->
        <!--                app:backgroundTint="@color/primaryColor"-->
        <!--                app:cornerRadius="15dp"-->
        <!--                app:layout_constraintEnd_toEndOf="parent" />-->
        <!--        </LinearLayout>-->
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
