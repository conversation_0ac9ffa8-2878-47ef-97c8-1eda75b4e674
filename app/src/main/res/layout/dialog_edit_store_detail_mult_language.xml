<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_dialog"
    android:orientation="vertical"
    android:padding="25dp">

    <com.metathought.food_order.casheir.ui.widget.DialogTopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="25dp"
        app:dialog_title="@string/mult_language" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_weight="1"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvStoreName"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:gravity="start"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                tools:text="@string/mult_language" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutCompanyNameKm"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/khmer"
                android:textColorHint="@color/black60"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtCompanyNameKm"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawablePadding="10dp"
                    android:inputType="text"
                    android:maxLength="100"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutCompanyNameEn"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/english"
                android:textColorHint="@color/black60"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtCompanyNameEn"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawablePadding="10dp"
                    android:inputType="text"
                    android:maxLength="100"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutCompanyNameZh"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/chinese"
                android:textColorHint="@color/black60"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtCompanyNameZH"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawablePadding="10dp"
                    android:inputType="number"
                    android:maxLength="100"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>

            <View
                android:id="@+id/vDLine"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginVertical="24dp"
                android:background="@drawable/imaginary_line"
                android:layerType="software" />

            <TextView
                android:id="@+id/tvDesc"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:gravity="start"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                tools:text="@string/mult_language" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutDescKm"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/khmer"
                android:textColorHint="@color/black60"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtDescKm"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawablePadding="10dp"
                    android:inputType="text"
                    android:maxLength="1000"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutDescEn"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/english"
                android:textColorHint="@color/black60"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtDescEn"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawablePadding="10dp"
                    android:inputType="text"
                    android:maxLength="1000"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutDescZh"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/chinese"
                android:textColorHint="@color/black60"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtDescZh"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawablePadding="10dp"
                    android:inputType="text"
                    android:maxLength="1000"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>


        </LinearLayout>


    </ScrollView>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/llButtom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="25dp"
        android:orientation="horizontal"
        app:divider="@drawable/shape_option_item_pading_12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:showDividers="middle">

        <TextView
            android:id="@+id/btnDone"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:background="@drawable/button_login_background"
            android:gravity="center"
            android:text="@string/done"
            android:textColor="@color/white"
            android:textSize="@dimen/_18ssp"
            android:textStyle="bold"
            android:visibility="visible" />
    </androidx.appcompat.widget.LinearLayoutCompat>

</LinearLayout>