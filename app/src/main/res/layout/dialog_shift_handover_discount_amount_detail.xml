<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_dialog"
    android:orientation="vertical">

    <com.metathought.food_order.casheir.ui.widget.DialogTopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        app:dialog_title="@string/print_report_discount_amount" />

    <LinearLayout
        android:id="@+id/llLisTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/order_id"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:textStyle="bold" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/print_report_discount_amount"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:textStyle="bold" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvList"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:itemCount="3"
        tools:listitem="@layout/shift_handover_discount_amount_item" />

    <include
        android:id="@+id/layoutEmpty"
        layout="@layout/layout_empty_data"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="24dp"
        android:layout_weight="1"
        android:visibility="gone" />

</LinearLayout>
