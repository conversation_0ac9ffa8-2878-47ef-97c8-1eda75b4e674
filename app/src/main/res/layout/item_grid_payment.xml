<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="100dp"
    android:background="@drawable/background_white_radius_4dp"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingHorizontal="4dp"
    android:paddingVertical="12dp">

    <ImageView
        android:id="@+id/gridIcon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/icon_payment_default" />

    <TextView
        android:id="@+id/gridTitle"
        style="@style/FontLocalization"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/black"
        android:textSize="@dimen/_16ssp"
        tools:text="选项" />

</LinearLayout>
