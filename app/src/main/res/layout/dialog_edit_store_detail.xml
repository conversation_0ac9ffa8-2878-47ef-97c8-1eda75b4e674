<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_dialog"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    android:padding="25dp">

    <com.metathought.food_order.casheir.ui.widget.DialogTopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="25dp"
        app:dialog_title="@string/store_manager" />


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_weight="1"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/llLogo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="@id/tvStoreName"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvStoreLocation">

                <TextView
                    android:id="@+id/tvLogoTitle"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:text="@string/manager_store_logo_required"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16ssp" />

                <androidx.cardview.widget.CardView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="0dp">

                    <FrameLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:id="@+id/ivLogo"
                            style="@style/FontLocalization"
                            android:layout_width="120dp"
                            android:layout_height="120dp"
                            android:scaleType="centerCrop"
                            android:src="@drawable/ic_photo_add" />

                        <ProgressBar
                            android:id="@+id/pbUpload"
                            android:layout_width="25dp"
                            android:layout_height="25dp"
                            android:layout_gravity="center"
                            android:visibility="gone" />

                        <ImageView
                            android:id="@+id/ivImgClose"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end|top"
                            android:layout_margin="6dp"
                            android:src="@drawable/icon_image_close" />
                    </FrameLayout>
                </androidx.cardview.widget.CardView>

                <TextView
                    android:id="@+id/tvLogoCue"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="@string/upload_image_cue"
                    android:textColor="@color/black40"
                    android:textSize="@dimen/_14ssp" />

            </LinearLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutName"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/manager_store_name_required"
                android:textColorHint="@color/black60"
                app:counterEnabled="true"
                app:counterMaxLength="100"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtName"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawablePadding="10dp"
                    android:inputType="text"
                    android:maxLength="100"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutIntro"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/manager_store_intro"
                android:textColorHint="@color/black60"
                app:counterEnabled="true"
                app:counterMaxLength="1000"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtIntro"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawablePadding="10dp"
                    android:inputType="text"
                    android:maxLength="1000"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutPhone"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/manager_store_phone"
                android:textColorHint="@color/black60"
                app:counterEnabled="true"
                app:counterMaxLength="30"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtPhone"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawablePadding="10dp"
                    android:inputType="number"
                    android:maxLength="30"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:id="@+id/tvLocationTitle"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:text="@string/manager_store_location_required"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutLongitude"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:layout_weight="1"
                    android:hint="@string/longitude"
                    android:textColorHint="@color/black60"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edLongitude"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:drawablePadding="10dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutLatitude"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginBottom="16dp"
                    android:layout_weight="1"
                    android:hint="@string/latitude"
                    android:textColorHint="@color/black60"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtLatitude"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:drawablePadding="10dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>

            <TextView
                android:id="@+id/tvLocation"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="12dp"
                android:layout_marginBottom="16dp"
                android:textColor="@color/black40"
                android:textSize="@dimen/_14ssp"
                android:visibility="gone"
                tools:text="地址地址"
                tools:visibility="visible" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutLocation"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/manager_store_location"
                android:textColorHint="@color/black60"
                app:counterEnabled="true"
                app:counterMaxLength="100"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtLocation"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawablePadding="10dp"
                    android:ellipsize="end"
                    android:inputType="text"
                    android:maxLength="100"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.metathought.food_order.casheir.ui.widget.TitleSwitchView
                android:id="@+id/switchDistance"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:switch_title="@string/limit_custom_ding" />

            <LinearLayout
                android:id="@+id/llLocationDistance"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">


                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/textInputLayoutLocationDistance"
                        style="@style/CustomOutlinedBox"
                        android:layout_width="150dp"
                        android:layout_height="wrap_content"
                        android:hint="@string/distance_required"
                        android:textColorHint="@color/black60"
                        app:endIconDrawable="@string/metre"
                        app:errorIconDrawable="@null">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edtLocationDistance"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawablePadding="10dp"
                            android:inputType="number"
                            android:maxLength="4"
                            android:maxLines="1"
                            android:singleLine="true"
                            android:textColor="@color/black"
                            android:textColorHint="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="22dp"
                        android:layout_marginEnd="20dp"
                        android:text="@string/metre"
                        android:textColor="@color/black80"
                        android:textSize="@dimen/_14ssp" />
                </FrameLayout>

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:text="@string/set_location_distance_1"
                    android:textColor="@color/black40"
                    android:textSize="@dimen/_14ssp" />
            </LinearLayout>


        </LinearLayout>


    </ScrollView>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/llButtom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="25dp"
        android:orientation="horizontal"
        app:divider="@drawable/shape_option_item_pading_12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:showDividers="middle">

        <TextView
            android:id="@+id/btnDone"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:background="@drawable/button_login_background"
            android:gravity="center"
            android:text="@string/done"
            android:textColor="@color/white"
            android:textSize="@dimen/_18ssp"
            android:textStyle="bold"
            android:visibility="visible" />
    </androidx.appcompat.widget.LinearLayoutCompat>

</LinearLayout>