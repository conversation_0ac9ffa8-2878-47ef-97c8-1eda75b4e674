<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvStoreName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/black"
                android:textSize="26px"
                android:textStyle="bold"
                tools:text="商店名" />

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/closing_report"
                android:textColor="@color/black"
                android:textSize="36px"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/printTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="start"
                    android:text="@string/print_time"
                    android:textColor="@color/black"
                    android:textSize="22px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvPrintTime"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="22px"
                    android:textStyle="bold"
                    tools:text="时间" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/staff"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="start"
                    android:text="@string/staff"
                    android:textColor="@color/black"
                    android:textSize="22px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvStaffName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="22px"
                    android:textStyle="bold"
                    tools:text="Lynn" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/classStartTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="start"
                    android:text="@string/class_start_time"
                    android:textColor="@color/black"
                    android:textSize="22px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvClassStartTime"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="22px"
                    android:textStyle="bold"
                    tools:text="2024/09/01 00:00:00" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/classCloseTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="start"
                    android:text="@string/class_close_time"
                    android:textColor="@color/black"
                    android:textSize="22px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvClassCloseTime"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="22px"
                    android:textStyle="bold"
                    tools:text="2024/09/01 23:59:59" />

            </LinearLayout>


            <LinearLayout
                android:id="@+id/llItemInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <include
                    layout="@layout/dot_divider"
                    android:layout_width="0dp"
                    android:layout_height="2dp"
                    android:layout_marginTop="15px"
                    android:layout_marginBottom="0dp"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/itemInformation"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/print_product_info"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

                <include
                    layout="@layout/dot_divider"
                    android:layout_width="0dp"
                    android:layout_height="2dp"
                    android:layout_marginTop="15px"
                    android:layout_marginBottom="0dp"
                    android:layout_weight="1" />

            </LinearLayout>


            <LinearLayout
                android:id="@+id/llGoodInfoTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/item"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:gravity="start"
                    android:text="@string/print_title_item_name"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/itemQty"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/print_title_qty"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/itemTotal"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:text="@string/print_title_item_total_with_unit"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

            </LinearLayout>


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5px"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="5"
                tools:listitem="@layout/item_product_report"
                tools:visibility="visible" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/goodsTotalNum"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="start"
                    android:text="@string/printer_title_goods_total_num"
                    android:textColor="@color/black"
                    android:textSize="30px"
                    android:textStyle="bold" />


                <TextView
                    android:id="@+id/tvGoodsTotalNum"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10px"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="30px"
                    android:textStyle="bold"
                    tools:text="800" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llSubtotal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/subtotal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="start"
                    android:text="@string/print_title_subtotal"
                    android:textColor="@color/black"
                    android:textSize="30px"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:text="$"
                    android:textColor="@color/black"
                    android:textSize="30px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvSubtotal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="30px"
                    android:textStyle="bold"
                    tools:text="800" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llDiscount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/discountAmount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="start"
                    android:text="@string/print_report_discount_amount"
                    android:textColor="@color/black"
                    android:textSize="24px"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:text="$"
                    android:textColor="@color/black"
                    android:textSize="24px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvDiscount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="24px"
                    android:textStyle="bold"
                    tools:text="800" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/total"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="start"
                    android:text="@string/print_title_total"
                    android:textColor="@color/black"
                    android:textSize="30px"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:text="$"
                    android:textColor="@color/black"
                    android:textSize="30px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvTotalPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="30px"
                    android:textStyle="bold"
                    tools:text="800" />

            </LinearLayout>

            <include
                layout="@layout/dot_divider"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:layout_marginTop="5px"
                android:layout_marginBottom="0dp" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/onlinePayment"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="start"
                    android:text="@string/online_payment_amount"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:text="$"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvOnlinePaymentAmount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold"
                    tools:text="800" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/offlinePayment"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="start"
                    android:text="@string/offline_payment_amount"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:text="$"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvOfflinePaymentAmount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold"
                    tools:text="800" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvOfflineChannel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="26px"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
            <!--            <LinearLayout-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:orientation="horizontal">-->

            <!--                <TextView-->
            <!--                    android:layout_width="0dp"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_marginStart="26px"-->
            <!--                    android:layout_weight="1"-->
            <!--                    android:gravity="start"-->
            <!--                    android:text="@string/cash_receipts"-->
            <!--                    android:textColor="@color/black"-->
            <!--                    android:textSize="26px"-->
            <!--                    android:textStyle="bold" />-->

            <!--                <TextView-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:gravity="end"-->
            <!--                    android:text="USD"-->
            <!--                    android:textColor="@color/black"-->
            <!--                    android:textSize="26px"-->
            <!--                    android:textStyle="bold" />-->

            <!--                <TextView-->
            <!--                    android:id="@+id/tvCashReceipts"-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_marginStart="10px"-->
            <!--                    android:gravity="end"-->
            <!--                    android:textColor="@color/black"-->
            <!--                    android:textSize="26px"-->
            <!--                    android:textStyle="bold"-->
            <!--                    tools:text="800" />-->

            <!--            </LinearLayout>-->

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/balancePayment"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="start"
                    android:text="@string/balance_receipts"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:text="$"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvBalanceReceipts"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold"
                    tools:text="800" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/creditPayment"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="start"
                    android:text="@string/received_credit"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:text="$"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvCreditReceipts"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold"
                    tools:text="800" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/handoverCash"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="start"
                    android:text="@string/handover_cash"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:gravity="end"-->
<!--                    android:text="$"-->
<!--                    android:textColor="@color/black"-->
<!--                    android:textSize="26px"-->
<!--                    android:textStyle="bold" />-->

                <TextView
                    android:id="@+id/tvHandoverCash"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold"
                    tools:text="800" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

<!--                <TextView-->
<!--                    android:layout_width="0dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_weight="1"-->
<!--                    android:gravity="end"-->
<!--                    android:text="៛"-->
<!--                    android:textColor="@color/black"-->
<!--                    android:textSize="26px"-->
<!--                    android:textStyle="bold" />-->

                <TextView
                    android:id="@+id/tvHandoverCashKHR"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold"
                    tools:text="800" />
            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/openingCash"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="start"
                    android:text="@string/reserve_fund"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:gravity="end"-->
<!--                    android:text="$"-->
<!--                    android:textColor="@color/black"-->
<!--                    android:textSize="26px"-->
<!--                    android:textStyle="bold" />-->

                <TextView
                    android:id="@+id/tvReserveFundUSD"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold"
                    tools:text="800" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

<!--                <TextView-->
<!--                    android:layout_width="0dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_weight="1"-->
<!--                    android:gravity="end"-->
<!--                    android:text="៛"-->
<!--                    android:textColor="@color/black"-->
<!--                    android:textSize="26px"-->
<!--                    android:textStyle="bold" />-->

                <TextView
                    android:id="@+id/tvReserveFundKHR"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold"
                    tools:text="800" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/shiftExpenses"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="start"
                    android:text="@string/shift_expenses"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:gravity="end"-->
<!--                    android:text="$"-->
<!--                    android:textColor="@color/black"-->
<!--                    android:textSize="26px"-->
<!--                    android:textStyle="bold" />-->

                <TextView
                    android:id="@+id/tvShiftExpensesUSD"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold"
                    tools:text="800" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

<!--                <TextView-->
<!--                    android:layout_width="0dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_weight="1"-->
<!--                    android:gravity="end"-->
<!--                    android:text="៛"-->
<!--                    android:textColor="@color/black"-->
<!--                    android:textSize="26px"-->
<!--                    android:textStyle="bold" />-->

                <TextView
                    android:id="@+id/tvShiftExpensesKHR"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold"
                    tools:text="800" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/differenceAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="start"
                    android:text="@string/difference_amount"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvDifferenceAmountUsd"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold"
                    tools:text="800" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/shiftBalance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="start"
                    android:text="@string/shift_balance"
                    android:textColor="@color/black"
                    android:textSize="32px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvShiftBalance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold"
                    tools:text="800" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llRemark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/remark"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="start"
                    android:text="@string/remark"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvRemark"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="今天下午取了店内的现金去买了500个塑料杯/点进紧急拿了50美元现金" />

            </LinearLayout>

            <!--            <include-->
            <!--                layout="@layout/dot_divider"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="2dp"-->
            <!--                android:layout_marginBottom="0dp" />-->

            <include
                android:id="@+id/vFooter"
                layout="@layout/view_printer_footer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
            <!--            <androidx.constraintlayout.widget.ConstraintLayout-->
            <!--                android:id="@+id/llThankYouLayout"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_gravity="center_horizontal"-->
            <!--                android:orientation="horizontal">-->

            <!--                <TextView-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_gravity="center_vertical"-->
            <!--                    android:gravity="start"-->
            <!--                    android:singleLine="true"-->
            <!--                    android:text="****"-->
            <!--                    android:textColor="@color/black60"-->
            <!--                    android:textSize="@dimen/_printer_default_sp"-->
            <!--                    android:textStyle="bold"-->
            <!--                    app:layout_constraintBottom_toBottomOf="parent"-->
            <!--                    app:layout_constraintEnd_toStartOf="@id/llThankYou"-->
            <!--                    app:layout_constraintTop_toTopOf="parent" />-->

            <!--                <LinearLayout-->
            <!--                    android:id="@+id/llThankYou"-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:gravity="center_horizontal"-->
            <!--                    android:orientation="vertical"-->
            <!--                    app:layout_constraintBottom_toBottomOf="parent"-->
            <!--                    app:layout_constraintEnd_toEndOf="parent"-->
            <!--                    app:layout_constraintStart_toStartOf="parent"-->
            <!--                    app:layout_constraintTop_toTopOf="parent">-->

            <!--                    <TextView-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:text="Powered by MPOS Cambodia"-->
            <!--                        android:textColor="@color/black60"-->
            <!--                        android:textSize="@dimen/_printer_default_sp"-->
            <!--                        android:textStyle="bold" />-->

            <!--                    <TextView-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:text="Phone: +855 1122 3328"-->
            <!--                        android:textColor="@color/black60"-->
            <!--                        android:textSize="@dimen/_printer_default_sp"-->
            <!--                        android:textStyle="bold" />-->

            <!--                    <TextView-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:text="https://www.m-pos.cc/"-->
            <!--                        android:textColor="@color/black60"-->
            <!--                        android:textSize="@dimen/_printer_default_sp"-->
            <!--                        android:textStyle="bold" />-->

            <!--                </LinearLayout>-->


            <!--                <TextView-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_gravity="center_vertical"-->
            <!--                    android:gravity="start"-->
            <!--                    android:singleLine="true"-->
            <!--                    android:text="****"-->
            <!--                    android:textColor="@color/black60"-->
            <!--                    android:textSize="@dimen/_printer_default_sp"-->
            <!--                    android:textStyle="bold"-->
            <!--                    app:layout_constraintBottom_toBottomOf="parent"-->
            <!--                    app:layout_constraintStart_toEndOf="@id/llThankYou"-->
            <!--                    app:layout_constraintTop_toTopOf="parent" />-->


            <!--            </androidx.constraintlayout.widget.ConstraintLayout>-->

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>