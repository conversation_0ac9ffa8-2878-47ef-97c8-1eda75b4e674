<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp">

    <LinearLayout
        android:id="@+id/layoutTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvTitleName"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="15dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="2"
            android:textAllCaps="true"
            android:textColor="@color/black"
            android:textSize="@dimen/_12ssp"
            android:textStyle="bold"
            tools:text="@tools:sample/first_names" />

        <TextView
            android:id="@+id/tvValue"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:textColor="@color/black"
            android:textSize="@dimen/_12ssp"
            android:textStyle="bold"
            tools:text="140" />
    </LinearLayout>


    <com.google.android.material.progressindicator.LinearProgressIndicator
        android:id="@+id/pbSaleAmount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/layoutTitle"
        android:layout_gravity="center_vertical"
        android:max="100"
        android:progress="100"
        app:indicatorColor="@color/primaryColor"
        app:trackColor="@color/mainBackground"
        app:trackCornerRadius="8dp"
        app:trackThickness="10dp" />


</RelativeLayout>
