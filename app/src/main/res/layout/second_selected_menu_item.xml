<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="2"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_weight="1.2"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="Lemonade"
            android:textColor="@color/black"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/tvSpecification"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_weight="1.2"
            android:ellipsize="end"
            android:textColor="@color/black60"
            android:textSize="@dimen/_10ssp"
            android:visibility="gone"
            tools:text="Large Normal ice, Normal sugar"
            tools:visibility="visible" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginHorizontal="8dp"
        android:layout_weight="1"
        android:gravity="center">

<!--        <ImageView-->
<!--            android:id="@+id/imgMinus"-->
<!--            android:layout_width="24dp"-->
<!--            android:layout_height="24dp"-->
<!--            android:src="@drawable/ic_minus" />-->

        <TextView
            android:id="@+id/tvQTY"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLength="4"
            android:paddingHorizontal="10dp"
            android:textColor="@color/black"
            android:textSize="12sp"
            tools:text="10" />

<!--        <ImageView-->
<!--            android:id="@+id/imgPlus"-->
<!--            android:layout_width="24dp"-->
<!--            android:layout_height="24dp"-->
<!--            android:src="@drawable/ic_add" />-->
    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1.2"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:text="@string/items"
        android:textColor="@color/black50"
        android:textSize="12sp">

        <TextView
            android:id="@+id/tvPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:textColor="@color/black"
            android:textSize="@dimen/_12ssp"
            tools:text="$9.9" />
        <TextView
            android:id="@+id/tvWeight"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:textColor="@color/black"
            android:textSize="@dimen/_12ssp"
            tools:text="$9.9" />
        <TextView
            android:id="@+id/tvVipPrice"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:layout_marginTop="4dp"
            android:drawablePadding="2dp"
            android:textColor="@color/member_price_color"
            android:textSize="9sp"
            android:textStyle="bold"
            android:drawableStart="@drawable/icon_vip"
            tools:text="$0.00" />

        <TextView
            android:id="@+id/tvOriginalPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:foreground="@drawable/strike_price"
            android:maxLines="3"
            android:textColor="@color/black60"
            android:textSize="@dimen/_10ssp"
            android:visibility="gone"
            tools:text="12.99"
            tools:visibility="visible" />
    </LinearLayout>
</LinearLayout>
