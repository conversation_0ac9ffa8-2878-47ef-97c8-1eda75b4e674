<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/background_popup_dropdown"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:padding="10dp">

    <TextView
        android:id="@+id/tvAllOrder"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:ellipsize="end"
        android:gravity="center"
        android:text="@string/all_order"
        android:textColor="@color/black"
        android:textSize="@dimen/_12ssp" />

    <TextView
        android:id="@+id/tvBeConfirm"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:ellipsize="end"
        android:gravity="center"
        android:text="@string/ordered_confirmed"
        android:textColor="@color/black"
        android:textSize="@dimen/_12ssp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tvUnpaid"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:ellipsize="end"
        android:gravity="center"
        android:text="@string/unpaid"
        android:textColor="@color/black"
        android:textSize="@dimen/_12ssp" />

    <TextView
        android:id="@+id/tvPreOrder"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:ellipsize="end"
        android:gravity="center"
        android:text="@string/pre_order"
        android:textColor="@color/black"
        android:textSize="@dimen/_12ssp" />

    <TextView
        android:id="@+id/tvPaid"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:ellipsize="end"
        android:gravity="center"
        android:text="@string/paid"
        android:textColor="@color/black"
        android:textSize="@dimen/_12ssp" />

    <TextView
        android:id="@+id/tvRefunds"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:ellipsize="end"
        android:gravity="center"
        android:text="@string/refund"
        android:textColor="@color/black"
        android:textSize="@dimen/_12ssp" />

    <TextView
        android:id="@+id/tvCancel"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:ellipsize="end"
        android:gravity="center"
        android:text="@string/order_cancel"
        android:textColor="@color/black"
        android:textSize="@dimen/_12ssp" />
</LinearLayout>