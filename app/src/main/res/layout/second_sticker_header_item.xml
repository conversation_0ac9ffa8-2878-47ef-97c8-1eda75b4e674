<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    app:cardCornerRadius="25dp"
    app:cardElevation="0dp"
    app:cardBackgroundColor="@android:color/transparent"
    app:strokeColor="@android:color/transparent">

    <TextView
        android:backgroundTint="@color/mainWhite"
        android:id="@+id/tvValue"
        android:layout_gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:paddingHorizontal="20dp"
        android:paddingVertical="4dp"
        android:maxLines="1"
        android:ellipsize="end"
        tools:text="Recommended"
        style="@style/FontLocalization"
        android:gravity="center"
        android:textSize="14sp"
        android:textColor="@color/black"
        android:textStyle="bold" />
</androidx.cardview.widget.CardView>