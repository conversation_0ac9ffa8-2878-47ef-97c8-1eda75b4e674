<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="400dp"
    android:layout_height="400dp"
    android:orientation="vertical">

    <com.google.android.material.card.MaterialCardView
        style="@style/CustomCardViewStyle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="3dp"
        android:layout_marginEnd="1dp"
        android:layout_marginBottom="3dp"
        android:clipToPadding="true"
        android:orientation="vertical"
        app:cardBackgroundColor="@color/white"
        app:cardElevation="3dp"
        app:strokeWidth="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:orientation="vertical">

            <com.metathought.food_order.casheir.ui.widget.CustomSearchView
                android:id="@+id/edtNickNameSearch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                app:search_hint="@string/customer_nickname" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1">

                <com.scwang.smart.refresh.layout.SmartRefreshLayout
                    android:id="@+id/nickNameRefreshLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/background_dialog"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.scwang.smart.refresh.header.MaterialHeader
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvNicnameList"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="start"
                        android:layout_marginHorizontal="16dp"
                        android:orientation="vertical"
                        android:overScrollMode="never"
                        android:scrollbars="none"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="8" />

                    <com.scwang.smart.refresh.footer.ClassicsFooter
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </com.scwang.smart.refresh.layout.SmartRefreshLayout>

                <TextView
                    android:id="@+id/tvEmptyText"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    android:layout_marginTop="10dp"
                    android:text="@string/empty_data"
                    android:textColor="@color/black80" />
            </FrameLayout>

            <!--    </LinearLayout>-->

        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

</FrameLayout>