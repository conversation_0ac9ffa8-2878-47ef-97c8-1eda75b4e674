<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="end"
    android:gravity="center_vertical">

    <TextView
        android:id="@+id/tvDialogName"
        style="@style/FontLocalization"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:textColor="@color/black"
        android:layout_marginTop="6dp"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/tvDialogIcon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="sure_to_anti_settlemen" />

    <ImageView
        android:id="@+id/tvDialogIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:visibility="gone"
        tools:visibility="visible"
        tools:src="@drawable/ic_trash"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="30dp"
        app:layout_constraintStart_toEndOf="@id/tvDialogName"
        app:layout_constraintEnd_toStartOf="@id/btnClose"
        app:layout_constraintTop_toTopOf="@id/tvDialogName"
        />

<!--    <View-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="0dp"-->
<!--        android:layout_weight="1"-->
<!--        android:minWidth="10dp" />-->

    <ImageView
        android:id="@+id/btnClose"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:paddingHorizontal="5dp"
        android:src="@drawable/ic_cross_closed"
        tools:ignore="ContentDescription"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>