<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvTitle"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="start"
            android:text="@string/pay_by_cash"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_weight="1"
                android:text="@string/order_amount"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/tvOrderPrice"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:textColor="@color/black"
                android:textStyle="bold"
                tools:text="$999.99" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_weight="1"
                android:text="@string/conversion_ratio"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/tvConversionRatio"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:textColor="@color/black"
                android:textStyle="bold"
                tools:text="$1 = ៛4000" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_weight="1"
                android:text="@string/amount_receivable"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/tvAmountReceivable"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:textColor="@color/black"
                android:textStyle="bold"
                tools:text="$999.99 + ៛4000" />

        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:layout_marginTop="10dp"
            android:text="@string/cash_collection"
            android:textColor="@color/black" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp">

            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="45dp"
                android:layout_weight="1">

                <!--                                <com.google.android.material.textfield.TextInputLayout-->
                <!--                                    android:id="@+id/textInputLayoutUsdAmount"-->
                <!--                                    style="@style/CustomOutlinedBox"-->
                <!--                                    android:layout_width="match_parent"-->
                <!--                                    android:layout_height="wrap_content"-->
                <!--                                    android:hint="@string/amount"-->
                <!--                                    android:textColorHint="@color/bg_progress"-->
                <!--                                    app:startIconDrawable="@drawable/ic_rectangle">-->

                <EditText
                    android:id="@+id/edtUsdAmount"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/background_white_border_black12_radius_12"
                    android:hint="0"
                    android:inputType="numberDecimal"
                    android:maxLength="15"
                    android:maxLines="1"
                    android:paddingStart="60dp"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black40" />

                <!--                </com.google.android.material.textfield.TextInputLayout>-->

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="16dp"
                    android:text="$"
                    android:textSize="@dimen/_16ssp"
                    android:textStyle="bold" />

            </FrameLayout>


            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="45dp"
                android:layout_marginStart="10dp"
                android:layout_weight="1">

                <!--                <com.google.android.material.textfield.TextInputLayout-->
                <!--                    android:id="@+id/textInputLayoutKhrAmount"-->
                <!--                    style="@style/CustomOutlinedBox"-->
                <!--                    android:layout_width="match_parent"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:hint="@string/amount"-->
                <!--                    android:textColorHint="@color/bg_progress"-->
                <!--                    app:startIconDrawable="@drawable/ic_rectangle">-->

                <EditText
                    android:id="@+id/edtKhrAmount"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/background_white_border_black12_radius_12"
                    android:hint="0"
                    android:inputType="number"
                    android:maxLength="15"
                    android:maxLines="1"
                    android:paddingStart="60dp"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black40" />

                <!--                </com.google.android.material.textfield.TextInputLayout>-->

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="16dp"
                    android:text="KHR"
                    android:textSize="@dimen/_16ssp"
                    android:textStyle="bold" />

            </FrameLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_weight="1"
                android:text="@string/back_your_change_amount"
                android:textColor="@color/black" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvChangeAmount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16ssp"
                    android:textStyle="bold"
                    tools:text="$999.99 + ៛4000" />

                <TextView
                    android:id="@+id/tvChangeAmountTip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:text="@string/insufficient_cash_received"
                    android:textColor="@color/main_red"
                    android:textSize="@dimen/_16ssp"
                    android:visibility="gone" />
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnCancel"
                style="@style/CustomOutlinedBox"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:text="@string/cancel"
                android:textAllCaps="false"
                android:textColor="@color/black"
                android:textSize="@dimen/_18ssp"
                app:backgroundTint="@color/white"
                app:cornerRadius="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:strokeColor="@color/black20"
                app:strokeWidth="1dp" />


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnConfirm"
                style="@style/CustomOutlinedBox"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:text="@string/confirm"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_18ssp"
                app:backgroundTint="@color/primaryColor"
                app:cornerRadius="8dp"
                app:layout_constraintEnd_toEndOf="parent" />
        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>