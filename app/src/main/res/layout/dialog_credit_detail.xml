<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/mainLayout"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.metathought.food_order.casheir.ui.widget.OrderDetailsView
            android:id="@+id/layoutDetail"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/background_dialog"
            android:padding="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </LinearLayout>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/mainLayout"
        app:layout_constraintEnd_toEndOf="@+id/mainLayout"
        app:layout_constraintStart_toStartOf="@+id/mainLayout"
        app:layout_constraintTop_toTopOf="@+id/mainLayout" />

</androidx.constraintlayout.widget.ConstraintLayout>
