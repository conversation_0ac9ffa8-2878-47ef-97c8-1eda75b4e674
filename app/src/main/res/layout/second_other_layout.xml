<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/second_other_arc_background">
    <ImageView
        android:id="@+id/ivOtherIcon"
        android:layout_width="286dp"
        android:layout_height="286dp"
        android:layout_marginTop="37dp"
        android:layout_marginEnd="26dp"
        android:background="@mipmap/ic_second_other_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="65dp"
        android:layout_marginTop="83dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toStartOf="@id/ivOtherIcon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/imgLogo"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:src="@drawable/ic_logo" />

        <TextView
            android:id="@+id/tvStoreName"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/khmer_os"
            android:maxLines="2"
            android:ellipsize="end"
            android:textColor="@color/white"
            android:textSize="35sp"
            android:textStyle="bold"
            tools:text="山河茶餐厅" />

        <TextView
            android:id="@+id/welcome"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/khmer_os"
            android:maxLines="2"
            android:text="@string/welcome"
            android:textColor="@color/white"
            android:textSize="35sp"
            android:textStyle="bold" />
    </LinearLayout>




    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/ivOtherLogo"
            android:layout_width="133dp"
            android:layout_height="46dp"
            android:layout_gravity="center_horizontal"
            android:src="@drawable/ic_second_other_logo" />

        <TextView
            android:id="@+id/businessSlogan"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="40dp"
            android:text="@string/your_best_business_partner"
            android:textColor="@color/black"
            android:textSize="22sp"
            android:textStyle="bold" />
    </LinearLayout>



</androidx.constraintlayout.widget.ConstraintLayout>