<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_white_top_radius_20dp"
    android:orientation="vertical">

    <com.metathought.food_order.casheir.ui.widget.DialogTopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="32dp"
        android:paddingVertical="24dp"
        app:dialog_title="@string/resume_order" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/mainBackground"
        android:focusable="true"
        android:focusableInTouchMode="true">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutFirst"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="6dp"
            android:paddingStart="10dp"
            android:paddingTop="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.65">

            <com.metathought.food_order.casheir.ui.widget.CustomSearchView
                android:id="@+id/edtSearch"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:layout_constraintBottom_toTopOf="@id/layoutPendingList"
                app:layout_constraintEnd_toEndOf="@id/layoutPendingList"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:search_hint="@string/hint_phone_number" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutPendingList"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginTop="12dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/edtSearch"
                app:layout_constraintWidth_percent="0.45"
                tools:visibility="visible">

                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginBottom="10dp"
                    app:cardElevation="0dp"
                    app:cardCornerRadius="12dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.scwang.smart.refresh.layout.SmartRefreshLayout
                        android:id="@+id/refreshLayout"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <com.scwang.smart.refresh.header.MaterialHeader
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recyclerPendingOrder"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:clipToPadding="false"
                            android:overScrollMode="never"
                            android:scrollbars="none"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            tools:itemCount="10"
                            tools:listitem="@layout/pending_order_item" />

                        <com.scwang.smart.refresh.footer.ClassicsFooter
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />
                    </com.scwang.smart.refresh.layout.SmartRefreshLayout>
                </androidx.cardview.widget.CardView>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutMenu"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:paddingStart="10dp"
                android:paddingBottom="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@+id/layoutPendingList"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.55"
                tools:visibility="visible">

                <!--                <TextView-->
                <!--                    android:id="@+id/tvTableID"-->
                <!--                    style="@style/FontLocalization"-->
                <!--                    android:layout_width="0dp"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:ellipsize="end"-->
                <!--                    android:gravity="center_vertical"-->
                <!--                    android:lines="1"-->
                <!--                    android:textColor="@color/black"-->
                <!--                    android:textSize="24sp"-->
                <!--                    android:textStyle="bold"-->
                <!--                    app:layout_constraintEnd_toEndOf="parent"-->
                <!--                    app:layout_constraintHorizontal_bias="0"-->
                <!--                    app:layout_constraintStart_toStartOf="parent"-->
                <!--                    app:layout_constraintTop_toTopOf="parent"-->
                <!--                    tools:text="@string/login_time" />-->

                <LinearLayout
                    android:id="@+id/llProduct"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:background="@drawable/background_white_radius_12dp"
                    android:orientation="vertical"
                    android:padding="8dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <!--                <LinearLayout-->
                    <!--                    android:layout_width="match_parent"-->
                    <!--                    android:layout_height="wrap_content"-->
                    <!--                    android:layout_marginTop="10dp"-->
                    <!--                    android:orientation="horizontal">-->

                    <!--                    <TextView-->
                    <!--                        android:layout_width="0dp"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_gravity="end"-->
                    <!--                        android:layout_marginTop="10dp"-->
                    <!--                        android:layout_marginEnd="10dp"-->
                    <!--                        android:layout_weight="3"-->
                    <!--                        android:text="@string/items"-->
                    <!--                        android:textColor="@color/black60"-->
                    <!--                        android:textSize="14sp" />-->

                    <!--                    <TextView-->
                    <!--                        android:layout_width="0dp"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_gravity="end"-->
                    <!--                        android:layout_marginTop="10dp"-->
                    <!--                        android:layout_marginEnd="10dp"-->
                    <!--                        android:layout_weight="1"-->
                    <!--                        android:gravity="center_horizontal"-->
                    <!--                        android:text="@string/quantity"-->
                    <!--                        android:textColor="@color/black60"-->
                    <!--                        android:textSize="14sp" />-->

                    <!--                    <TextView-->
                    <!--                        android:layout_width="0dp"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_gravity="end"-->
                    <!--                        android:layout_marginTop="10dp"-->
                    <!--                        android:layout_marginEnd="10dp"-->
                    <!--                        android:layout_weight="2"-->
                    <!--                        android:gravity="center_horizontal"-->
                    <!--                        android:text="@string/amount"-->
                    <!--                        android:textColor="@color/black60"-->
                    <!--                        android:textSize="14sp" />-->
                    <!--                </LinearLayout>-->

                    <!--                <View style="@style/commonDividerStyle" />-->

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/orderedInfoRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:overScrollMode="never"
                        android:scrollbars="none"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="5"
                        tools:listitem="@layout/pending_order_menu_item" />

                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <include
                android:id="@+id/layoutEmptyList"
                layout="@layout/layout_empty_list"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutResumeOrder"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:padding="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/layoutFirst"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:id="@+id/layoutDetailInfo"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@drawable/background_white_radius_12dp"
                android:orientation="vertical"
                android:paddingHorizontal="10dp"
                android:paddingBottom="10dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible">

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:scrollbars="none">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:id="@+id/llCustomerInfo"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="24dp"
                            android:orientation="vertical"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/tvCustomerTitle"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/customer_info"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_18ssp"
                                android:textStyle="bold" />

                            <LinearLayout
                                android:id="@+id/llCustomerName"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="16dp">

                                <TextView
                                    style="@style/FontLocalization"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="15dp"
                                    android:text="@string/customer_name"
                                    android:textColor="@color/black60"
                                    android:textSize="@dimen/_12ssp" />

                                <TextView
                                    android:id="@+id/tvCustomerName"
                                    style="@style/FontLocalization"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="end"
                                    android:maxLines="2"
                                    android:textColor="@color/black"
                                    android:textSize="@dimen/_14ssp"
                                    android:textStyle="bold"
                                    tools:text="@tools:sample/lorem/random" />
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/llCustomerPhone"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="12dp">

                                <TextView
                                    style="@style/FontLocalization"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="15dp"
                                    android:text="@string/phone_number"
                                    android:textColor="@color/black60"
                                    android:textSize="@dimen/_12ssp" />

                                <TextView
                                    android:id="@+id/tvPhoneNumber"
                                    style="@style/FontLocalization"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="end"
                                    android:maxLines="2"
                                    android:textColor="@color/black"
                                    android:textSize="@dimen/_14ssp"
                                    android:textStyle="bold"
                                    tools:text="15006099788" />
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/llPeople"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="12dp">

                                <TextView
                                    style="@style/FontLocalization"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="15dp"
                                    android:text="@string/people"
                                    android:textColor="@color/black60"
                                    android:textSize="@dimen/_12ssp" />

                                <TextView
                                    android:id="@+id/tvPeople"
                                    style="@style/FontLocalization"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="end"
                                    android:maxLines="2"
                                    android:textColor="@color/black"
                                    android:textSize="@dimen/_14ssp"
                                    android:textStyle="bold"
                                    tools:text="5" />
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/llDiningTime"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="12dp">

                                <TextView
                                    style="@style/FontLocalization"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="15dp"
                                    android:text="@string/dining_time"
                                    android:textColor="@color/black60"
                                    android:textSize="@dimen/_12ssp" />

                                <TextView
                                    android:id="@+id/tvDiningTime"
                                    style="@style/FontLocalization"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="end"
                                    android:maxLines="2"
                                    android:textColor="@color/black"
                                    android:textSize="@dimen/_14ssp"
                                    android:textStyle="bold"
                                    tools:text="2024/04/02 12:00:00" />
                            </LinearLayout>
                        </LinearLayout>


                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="24dp"
                            android:text="@string/pending_order_info"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_18ssp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/pending_order_id"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvPendingOrderID"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="20241234567890" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/date"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvPendingOrderTime"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="2024/04/02 12:00:00" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/order_type"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvOrderType"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="2024/04/02 12:00:00" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_weight="1">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/remark"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvRemark"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:ellipsize="end"
                                android:gravity="end"
                                android:text="@string/none"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="@string/none" />
                        </LinearLayout>
                    </LinearLayout>
                </ScrollView>

                <View
                    style="@style/commonDividerStyle"
                    android:layout_marginVertical="17dp" />

                <LinearLayout
                    android:id="@+id/layoutTotal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="9dp"
                        android:visibility="gone"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/subtotal"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            android:id="@+id/tvSubtotal"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llDiscountActivity"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="9dp"
                        android:gravity="center_vertical"
                        android:visibility="gone">

                        <TextView
                            android:id="@+id/tvDiscountActivityTitle"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="4dp"
                            android:gravity="center_vertical"
                            android:text="@string/discount_activity"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_14ssp" />

                        <ImageView
                            android:id="@+id/btnDiscountActivityPriceCue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="15dp"
                            android:src="@drawable/icon_circle_warn"
                            android:visibility="gone"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tvDiscountActivityAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llPackPrice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="9dp"
                        android:visibility="gone"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="4dp"
                            android:gravity="center_vertical"
                            android:text="@string/packing_price"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_14ssp" />

                        <ImageView
                            android:id="@+id/btnPackPriceCue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="15dp"
                            android:src="@drawable/icon_circle_warn" />


                        <TextView
                            android:id="@+id/tvPackingAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llServiceFee"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="9dp"
                        android:visibility="gone"
                        android:gravity="top">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="4dp"
                            android:gravity="center_vertical"
                            android:text="@string/service_fee"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_14ssp" />

                        <ImageView
                            android:id="@+id/btnServiceFeeCue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="15dp"
                            android:src="@drawable/icon_circle_warn" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvServiceFee"
                                style="@style/FontLocalization"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="$99.99" />

                            <!--                        <TextView-->
                            <!--                            android:id="@+id/tvVipServiceFee"-->
                            <!--                            style="@style/FontLocalization"-->
                            <!--                            android:layout_width="wrap_content"-->
                            <!--                            android:layout_height="wrap_content"-->
                            <!--                            android:layout_marginStart="2dp"-->
                            <!--                            android:layout_marginTop="4dp"-->
                            <!--                            android:drawablePadding="2dp"-->
                            <!--                            android:textColor="@color/member_price_color"-->
                            <!--                            android:textSize="@dimen/_14ssp"-->
                            <!--                            android:textStyle="bold"-->
                            <!--                            android:visibility="gone"-->
                            <!--                            app:drawableStartCompat="@drawable/icon_vip"-->
                            <!--                            tools:text="$0.00"-->
                            <!--                            tools:visibility="visible" />-->
                        </LinearLayout>

                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="9dp"
                        android:gravity="center_vertical"
                        android:visibility="gone">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/discounted"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            tools:text="-$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llVat"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="9dp"
                        android:visibility="gone"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/vat"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_14ssp" />

                        <TextView
                            android:id="@+id/tvVAT"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="15dp"
                        android:gravity="center_vertical"
                        android:text="@string/total"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvTotal"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            app:layout_constrainedWidth="true"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$0" />

                        <TextView
                            android:id="@+id/tvVipPrice"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="2dp"
                            android:layout_marginTop="4dp"
                            android:drawablePadding="2dp"
                            android:textColor="@color/member_price_color"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            app:drawableStartCompat="@drawable/icon_vip"
                            tools:text="$0.00"
                            tools:visibility="visible" />

                        <!--                        <TextView-->
                        <!--                            android:id="@+id/tvOriginalPrice"-->
                        <!--                            style="@style/FontLocalization"-->
                        <!--                            android:layout_width="wrap_content"-->
                        <!--                            android:layout_height="wrap_content"-->
                        <!--                            android:layout_marginStart="2dp"-->
                        <!--                            android:layout_marginTop="4dp"-->
                        <!--                            android:drawablePadding="2dp"-->
                        <!--                            android:foreground="@drawable/strike_price"-->
                        <!--                            android:textColor="@color/black60"-->
                        <!--                            android:textSize="16sp"-->
                        <!--                            android:textStyle="bold"-->
                        <!--                            android:visibility="gone"-->
                        <!--                            tools:text="$0.00"-->
                        <!--                            tools:visibility="visible" />-->

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/btnDelete"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:minWidth="100dp"
                        app:cardBackgroundColor="@color/transparent"
                        app:strokeColor="@color/color_ff3141">

                        <TextView
                            android:layout_width="100dp"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="10dp"
                            android:gravity="center"
                            android:text="@string/remove"
                            android:textColor="@color/color_ff3141"
                            android:textSize="20sp"
                            android:textStyle="bold" />
                    </com.google.android.material.card.MaterialCardView>


                    <LinearLayout
                        android:id="@+id/btnResumeOrder"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:background="@drawable/button_login_background"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="horizontal"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/resume_order"
                            android:textColor="@color/mainWhite"
                            android:textSize="20sp"
                            android:textStyle="bold" />

                        <ProgressBar
                            android:id="@+id/pbConfirm"
                            android:layout_width="25dp"
                            android:layout_height="25dp"
                            android:layout_marginStart="10dp"
                            android:indeterminate="true"
                            android:indeterminateTint="@color/mainWhite"
                            android:indeterminateTintMode="src_atop"
                            android:progressTint="@color/mainWhite"
                            android:visibility="gone" />
                    </LinearLayout>
                </LinearLayout>


            </LinearLayout>

            <include
                android:id="@+id/layoutEmptyDetail"
                layout="@layout/layout_empty_data"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <ProgressBar
            android:id="@+id/pbLoad"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_gravity="center"
            android:layout_marginStart="10dp"
            android:indeterminate="true"
            android:indeterminateTint="@color/primaryColor"
            android:indeterminateTintMode="src_atop"
            android:progressTint="@color/primaryColor"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>
