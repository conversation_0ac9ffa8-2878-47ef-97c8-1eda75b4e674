<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/background_dialog"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="24dp"
            app:dialog_title="@string/product_report" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="32dp">

                <com.metathought.food_order.casheir.ui.widget.CalendarTextView
                    android:id="@+id/tvCalendar"
                    style="@style/commonCalendarTextViewStyle"
                    android:layout_width="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:background="@drawable/background_white_border_black12_radius_100"
                    app:layout_constraintStart_toEndOf="@id/dropdownFilter"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="01 03, 2024 - 01 03, 2024" />

                <com.metathought.food_order.casheir.ui.widget.CustomSearchView
                    android:id="@+id/edtSearch"
                    android:layout_width="220dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:layout_marginEnd="10dp"
                    app:search_hint="@string/hint_sale_report_search" />

                <LinearLayout
                    android:id="@+id/dropdownFilter"
                    android:layout_width="170dp"
                    android:layout_height="match_parent"
                    android:background="@drawable/background_white_border_black12_radius_100"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingHorizontal="10dp"
                    android:visibility="visible"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/tvType"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:gravity="start"
                        android:maxLines="1"
                        android:text="@string/product_category"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <ImageView
                        android:id="@+id/arrow"
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:src="@drawable/ic_dropdown"
                        android:visibility="visible"
                        tools:ignore="ContentDescription" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvClearFilter"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="10dp"
                    android:drawablePadding="10dp"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="15dp"
                    android:singleLine="true"
                    android:text="@string/clear_filter"
                    android:textColor="@color/primaryColor"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toEndOf="@id/tvCalendar"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="10" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnPrint"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="25dp"
                    android:layout_marginEnd="10dp"
                    android:insetTop="0dp"

                    android:insetBottom="0dp"
                    android:minWidth="60dp"
                    android:paddingHorizontal="0dp"
                    android:paddingVertical="0dp"
                    android:text="@string/printer"
                    android:textColor="@color/primaryColor"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    app:backgroundTint="@android:color/transparent"
                    app:cornerRadius="20dp"
                    app:strokeColor="@color/primaryColor"
                    app:strokeWidth="1dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnExport"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:insetTop="0dp"

                    android:insetBottom="0dp"
                    android:minWidth="60dp"
                    android:paddingHorizontal="0dp"
                    android:paddingVertical="0dp"
                    android:text="@string/export"
                    android:textColor="@color/primaryColor"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    app:backgroundTint="@android:color/transparent"
                    app:cornerRadius="20dp"
                    app:strokeColor="@color/primaryColor"
                    app:strokeWidth="1dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="32dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="10dp"
                android:gravity="top"
                android:orientation="horizontal">

                <TextView
                    android:gravity="center"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1.5"
                    android:text="@string/item_category"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="2"
                    android:orientation="vertical">

                    <TextView
                        android:layout_gravity="center"
                        android:id="@+id/itemName"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableEnd="@drawable/icon_sort_normal"
                        android:drawablePadding="4dp"
                        android:gravity="center_vertical"
                        android:text="@string/product_name"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />
                </LinearLayout>


                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/itemQty"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableEnd="@drawable/icon_sort_normal"
                        android:drawablePadding="4dp"
                        android:gravity="center_vertical"
                        android:layout_gravity="center"
                        android:text="@string/quantity"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                </LinearLayout>


                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_gravity="center"
                        android:id="@+id/itemTotal"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableEnd="@drawable/icon_sort_normal"
                        android:drawablePadding="4dp"
                        android:gravity="center_vertical"
                        android:text="@string/print_title_total"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <!--                    <TextView-->
                    <!--                        style="@style/FontLocalization"-->
                    <!--                        android:layout_width="wrap_content"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:gravity="center_vertical"-->
                    <!--                        android:text="@string/not_include_vat"-->
                    <!--                        android:textColor="@color/black40"-->
                    <!--                        android:textSize="@dimen/_10ssp" />-->
                </LinearLayout>

                <TextView
                    android:gravity="center"
                    android:id="@+id/tvServiceFee"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1"
                    android:text="@string/service_fee"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:gravity="center"
                    android:id="@+id/tvPackingFee"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1"
                    android:text="@string/pac"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:gravity="center"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1"
                    android:text="@string/order_numbers"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:gravity="center"
                    android:id="@+id/itemTotalAmount"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/proportion_of_amount"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />
            </LinearLayout>

            <View
                style="@style/commonDividerStyle"
                android:layout_marginHorizontal="32dp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvList"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginHorizontal="32dp"
                android:layout_weight="1"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            <include
                android:id="@+id/layoutEmpty"
                layout="@layout/layout_empty_data"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>

        <View style="@style/commonDividerStyle" />

        <LinearLayout
            android:id="@+id/llBottom"
            android:layout_width="match_parent"
            android:layout_height="72dp"
            android:layout_marginHorizontal="32dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginEnd="20dp"
                android:layout_weight="3.5" />


            <TextView
                android:id="@+id/tvTotalNum"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:gravity="center"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                tools:text="222" />


            <TextView
                android:id="@+id/tvTotal"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/print_title_total"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold" />

            <TextView
                android:gravity="center"
                android:id="@+id/tvTotalServiceFee"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:text="@string/service_fee"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold" />

            <TextView
                android:gravity="center"
                android:id="@+id/tvTotalPac"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:text="@string/pac"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold" />

            <TextView
                android:gravity="center"
                android:id="@+id/tvOrderNum"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:layout_weight="1"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                tools:text="99" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

        </LinearLayout>

    </LinearLayout>

    <ProgressBar
        android:id="@+id/pbLoading"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_gravity="center"
        android:indeterminate="true"
        android:indeterminateTint="@color/primaryColor"
        android:indeterminateTintMode="src_atop"
        android:progressTint="@color/primaryColor"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>
