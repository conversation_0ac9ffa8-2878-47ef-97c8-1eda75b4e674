<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@android:color/transparent"
    app:cardCornerRadius="0dp"
    app:cardElevation="0dp"
    app:strokeColor="@android:color/transparent">

    <TextView
        android:id="@+id/tvValue"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:backgroundTint="@color/mainWhite"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:paddingBottom="12dp"
        android:textColor="@color/black"
        android:textSize="20sp"
        android:textStyle="bold"
        tools:text="Recommended" />
</androidx.cardview.widget.CardView>