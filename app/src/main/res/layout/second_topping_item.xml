<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="5dp">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/selector_rounded_rectangle_border"
        android:gravity="center_vertical"
        android:padding="8dp">

        <TextView
            android:id="@+id/tvFeedName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLength="50"
            android:maxLines="1"
            android:text="Lemonade"
            android:textColor="@color/black"
            android:textSize="@dimen/_12ssp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="15dp"
            android:ellipsize="end"
            android:maxLines="3"
            android:paddingHorizontal="5dp"
            android:text="$12.99"
            android:textColor="@color/primaryColor"
            android:textSize="@dimen/_12ssp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvQTY"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLength="4"
            android:paddingHorizontal="5dp"
            android:text="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp" />
    </LinearLayout>

</FrameLayout>