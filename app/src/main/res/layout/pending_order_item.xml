<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!--    <com.google.android.material.card.MaterialCardView-->
    <!--        android:id="@+id/cardViewMain"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:clipToPadding="false"-->
    <!--        android:elevation="0dp"-->
    <!--        app:cardBackgroundColor="@color/white"-->
    <!--        app:cardCornerRadius="0dp"-->
    <!--        app:cardElevation="0dp"-->
    <!--        app:strokeWidth="0dp">-->

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clContent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingHorizontal="12dp"
        android:paddingVertical="16dp">

        <TextView
            android:id="@+id/tvTableID"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:autoSizeMaxTextSize="@dimen/_18ssp"
            android:autoSizeMinTextSize="@dimen/_12ssp"
            android:autoSizeTextType="uniform"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:lines="1"
            android:text="@string/login_time"
            android:textColor="@color/black"
            android:textSize="@dimen/_18ssp"
            android:textStyle="bold"
            android:visibility="visible"
            app:layout_constraintEnd_toStartOf="@id/tvPrice"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/imgDelete"
            style="@style/FontLocalization"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:gravity="center_vertical|end"
            android:maxLines="1"
            android:src="@drawable/ic_trash"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintStart_toEndOf="@id/tvTableID"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvItems"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:gravity="start"
            android:maxLines="1"
            android:textColor="@color/black80"
            android:textSize="@dimen/_14ssp"
            app:layout_constraintEnd_toStartOf="@id/tvPrice"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTableID"
            app:layout_constraintVertical_bias="1"
            tools:text="11111" />

        <TextView
            android:id="@+id/tvTime"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:gravity="start"
            android:maxLines="1"
            android:textColor="@color/black80"
            android:textSize="@dimen/_14ssp"
            app:layout_constraintEnd_toStartOf="@id/tvPrice"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvItems"
            tools:text="11111" />

        <TextView
            android:id="@+id/tvRemark"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="10dp"
            android:gravity="start"
            android:textColor="@color/black80"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTime"
            tools:text="11111" />


        <TextView
            android:id="@+id/tvPrice"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="0dp"
            android:gravity="bottom|end"
            android:maxLines="1"
            android:textColor="@color/black"
            android:textSize="@dimen/_18ssp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="$99.99" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        style="@style/commonDividerStyle"
        android:layout_gravity="bottom"
        android:layout_marginHorizontal="12dp" />
    <!--    </com.google.android.material.card.MaterialCardView>-->
</FrameLayout>
