<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/mainWhite"
        android:backgroundTint="@color/mainWhite"
        android:gravity="center_vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/imgLogo"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:src="@drawable/ic_logo" />

        <TextView
            android:id="@+id/tvStoreName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:textColor="@color/black"
            tools:text="@string/brand_name" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingHorizontal="15dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="15dp"
            android:background="@drawable/background_menu_fragment"
            android:paddingHorizontal="16dp"
            android:paddingTop="16dp"
            app:layout_constraintEnd_toStartOf="@id/layoutOrder"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:id="@+id/layoutMenuCategories"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginBottom="16dp"
                android:background="@drawable/background_dialog"
                android:orientation="vertical"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.15">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewCategories"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    tools:listitem="5" />
            </LinearLayout>


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewMenu"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="10dp"
                android:clipToPadding="true"
                android:paddingEnd="-5dp"
                android:overScrollMode="never"
                android:scrollbars="none"
                android:paddingBottom="11dp"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@id/layoutMenuCategories"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0"
                app:spanCount="3"
                tools:listitem="@layout/second_menu_item" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutOrder"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@drawable/background_menu_fragment"
            android:paddingHorizontal="10dp"
            android:paddingTop="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.34">

            <FrameLayout
                android:id="@+id/layoutTableID"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                app:layout_constraintEnd_toStartOf="@id/dingingLayout"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tvSelectTable"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/select_table"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16ssp"
                    tools:text="A001" />
            </FrameLayout>

            <androidx.cardview.widget.CardView
                android:id="@+id/dingingLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:elevation="0dp"
                app:cardBackgroundColor="@color/paid_backgroud_color"
                app:cardCornerRadius="5dp"
                app:cardElevation="0dp"
                app:layout_constraintTop_toTopOf="@id/layoutTableID"
                app:layout_constraintBottom_toBottomOf="@id/layoutTableID"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintStart_toEndOf="@id/layoutTableID">

                <TextView
                    android:id="@+id/tvDiningStyle"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical|end"
                    android:maxLines="1"
                    android:paddingHorizontal="10dp"
                    android:paddingVertical="5dp"
                    android:textColor="@color/paid_text_color"
                    android:textSize="@dimen/_12ssp"
                    android:textStyle="bold"
                    tools:text="堂食" />
            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:orientation="vertical"
                android:layout_marginTop="5dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/dingingLayout">

               <androidx.cardview.widget.CardView
                   app:cardCornerRadius="16dp"
                   android:layout_width="match_parent"
                   android:layout_weight="1"
                   android:elevation="0dp"
                   app:cardBackgroundColor="@color/white"
                   android:layout_height="0dp">
                   <LinearLayout
                       android:id="@+id/layoutMainOrdered"
                       android:layout_width="match_parent"
                       android:layout_height="match_parent"
                       android:orientation="vertical"
                       android:paddingHorizontal="5dp"
                       android:visibility="visible">

                       <LinearLayout
                           android:id="@+id/layoutHeader"
                           android:layout_width="match_parent"
                           android:layout_height="wrap_content"
                           android:layout_marginVertical="8dp"
                           android:orientation="horizontal">

                           <TextView
                               android:id="@+id/tvDeleteAll"
                               android:layout_width="0dp"
                               android:layout_height="wrap_content"
                               android:layout_gravity="center_vertical"
                               android:layout_marginEnd="10dp"
                               android:layout_weight="2"
                               android:drawablePadding="3dp"
                               android:text="@string/items"
                               android:textColor="@color/black60"
                               android:textSize="12sp" />

                           <TextView
                               android:id="@+id/quantity"
                               android:layout_width="0dp"
                               android:layout_height="wrap_content"
                               android:layout_gravity="center_vertical"
                               android:layout_marginEnd="10dp"
                               android:layout_weight="1"
                               android:gravity="center_horizontal"
                               android:text="@string/quantity"
                               android:textColor="@color/black60"
                               android:textSize="12sp" />

                           <TextView
                               android:id="@+id/amount"
                               android:layout_width="0dp"
                               android:layout_height="wrap_content"
                               android:layout_gravity="center_vertical"
                               android:layout_marginEnd="10dp"
                               android:layout_weight="1"
                               android:gravity="center_horizontal"
                               android:text="@string/amount"
                               android:textColor="@color/black60"
                               android:textSize="12sp" />
                       </LinearLayout>

                       <View
                           android:id="@+id/vTopLine"
                           style="@style/commonDividerStyle" />
                       <!--            <include-->
                       <!--                android:id="@+id/menuTest"-->
                       <!--                layout="@layout/selected_menu_item"/>-->
                       <!--            <include-->
                       <!--                android:id="@+id/menuTest2"-->
                       <!--                layout="@layout/selected_menu_item"/>-->

                       <androidx.cardview.widget.CardView
                           android:id="@+id/layoutNewOrderTitle"
                           android:layout_width="match_parent"
                           android:layout_height="wrap_content"
                           android:layout_marginVertical="5dp"
                           android:visibility="gone"
                           app:cardBackgroundColor="@color/background_session"
                           app:cardCornerRadius="8dp"
                           app:layout_constraintTop_toBottomOf="@+id/tvCustomerInfo"
                           app:strokeWidth="0dp"
                           tools:visibility="visible">

                           <LinearLayout
                               android:layout_width="match_parent"
                               android:layout_height="wrap_content">

                               <TextView
                                   android:id="@+id/tvOrderMoreCount"
                                   android:layout_width="wrap_content"
                                   android:layout_height="wrap_content"
                                   android:paddingHorizontal="5dp"
                                   android:paddingVertical="5dp"
                                   android:text="@string/new_order"
                                   android:textColor="@color/primaryColor"
                                   android:textSize="10sp"
                                   android:textStyle="bold" />

                               <TextView
                                   android:id="@+id/tvNewOrderTotalPrice"
                                   android:layout_width="wrap_content"
                                   android:layout_height="wrap_content"
                                   android:layout_weight="1"
                                   android:gravity="end"
                                   android:paddingHorizontal="5dp"
                                   android:paddingVertical="5dp"
                                   android:text="$0.00"
                                   android:textColor="@color/black"
                                   android:textSize="10sp"
                                   android:textStyle="bold" />

                               <ImageView
                                   android:id="@+id/arrowNewOrder"
                                   android:layout_width="12dp"
                                   android:layout_height="12dp"
                                   android:layout_gravity="center_vertical"
                                   android:rotation="180"
                                   android:src="@drawable/ic_dropdown"
                                   android:visibility="visible"
                                   app:tint="@color/primaryColor"
                                   tools:ignore="ContentDescription" />
                           </LinearLayout>
                       </androidx.cardview.widget.CardView>

                       <androidx.recyclerview.widget.RecyclerView
                           android:id="@+id/recyclerOrderedFood"
                           android:layout_width="match_parent"
                           android:layout_height="0dp"
                           android:layout_weight="1"
                           android:clipToPadding="false"
                           android:overScrollMode="never"
                           android:scrollbars="none"
                           android:visibility="visible"
                           app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                           tools:itemCount="2"
                           tools:listitem="@layout/second_selected_menu_item" />

                       <androidx.cardview.widget.CardView
                           android:id="@+id/layoutOrderMore"
                           android:layout_width="match_parent"
                           android:layout_height="wrap_content"
                           android:layout_marginVertical="5dp"
                           android:visibility="gone"
                           app:cardBackgroundColor="@color/background_session"
                           app:cardCornerRadius="8dp"
                           app:strokeWidth="0dp"
                           tools:visibility="visible">

                           <LinearLayout
                               android:layout_width="match_parent"
                               android:layout_height="wrap_content">

                               <TextView
                                   android:id="@+id/tvPreviousOrderCount"
                                   android:layout_width="wrap_content"
                                   android:layout_height="wrap_content"
                                   android:paddingHorizontal="5dp"
                                   android:paddingVertical="5dp"
                                   android:text="@string/old_order_items"
                                   android:textColor="@color/primaryColor"
                                   android:textSize="10sp"
                                   android:textStyle="bold" />

                               <TextView
                                   android:id="@+id/tvPricePreviousOrder"
                                   android:layout_width="wrap_content"
                                   android:layout_height="wrap_content"
                                   android:layout_weight="1"
                                   android:gravity="end"
                                   android:paddingHorizontal="5dp"
                                   android:paddingVertical="5dp"
                                   android:text="$0.00"
                                   android:textColor="@color/black"
                                   android:textSize="10sp"
                                   android:textStyle="bold" />

                               <ImageView
                                   android:id="@+id/arrowOldOrder"
                                   android:layout_width="12dp"
                                   android:layout_height="12dp"
                                   android:layout_gravity="center_vertical"
                                   android:src="@drawable/ic_dropdown"
                                   android:visibility="visible"
                                   app:tint="@color/primaryColor"
                                   tools:ignore="ContentDescription" />
                           </LinearLayout>
                       </androidx.cardview.widget.CardView>

                       <androidx.recyclerview.widget.RecyclerView
                           android:id="@+id/recyclerPreviousOrderedFood"
                           android:layout_width="match_parent"
                           android:layout_height="0dp"
                           android:layout_marginBottom="10dp"
                           android:layout_weight="1"
                           android:clipToPadding="false"
                           android:overScrollMode="never"
                           android:scrollbars="none"
                           android:visibility="gone"
                           app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                           tools:itemCount="2"
                           tools:listitem="@layout/selected_menu_item"
                           tools:visibility="gone" />


                   </LinearLayout>
               </androidx.cardview.widget.CardView>


                <LinearLayout
                    android:id="@+id/layoutTotal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingTop="17dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent">


                    <LinearLayout
                        android:id="@+id/llPackPrice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/packingPrice"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="4dp"
                            android:gravity="center_vertical"
                            android:text="@string/packing_price"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />


                        <TextView
                            android:id="@+id/tvPackingAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/subtotal"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/subtotal"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <TextView
                            android:id="@+id/tvSubtotal"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/vat"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/vat"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <TextView
                            android:id="@+id/tvVat"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/discounted"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/discounted"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="-$99.99" />
                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/totalPrice"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/total_price"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:orientation="vertical"
                            android:gravity="end"
                            android:layout_height="wrap_content"
                            android:layout_weight="1">


                            <TextView
                                android:id="@+id/tvTotalPrice"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/primaryColor"
                                android:textSize="24sp"
                                android:textStyle="bold"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                tools:text="$999999.99" />

                            <TextView
                                android:id="@+id/tvVipPrice"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="2dp"
                                android:drawablePadding="2dp"
                                android:textColor="@color/member_price_color"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:visibility="gone"
                                android:drawableStart="@drawable/icon_vip"
                                tools:text="$0.00"
                                tools:visibility="visible" />

                        </LinearLayout>

                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <include
                android:id="@+id/layoutEmpty"
                layout="@layout/layout_empty_data"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="50dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>