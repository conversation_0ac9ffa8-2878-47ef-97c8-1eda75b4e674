<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <!-- 顶部DialogTopBar -->
    <com.metathought.food_order.casheir.ui.widget.DialogTopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="24dp"
        android:paddingVertical="10dp"
        app:dialog_title="@string/pay_now"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 主要内容区域 - 左右平分 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/color_efefef"
        android:orientation="horizontal"
        android:padding="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topBar">

        <!-- 左侧内容区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 左侧第一块 - 100dp -->
            <LinearLayout
                android:id="@+id/leftBlock1"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:layout_marginBottom="6dp"
                android:background="@drawable/background_white_radius_4dp"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="16dp">

                <com.google.android.material.imageview.ShapeableImageView
                    android:id="@+id/ivAvatar"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_marginEnd="12dp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/icon_photo_default"
                    app:shapeAppearanceOverlay="@style/AvatarShape" />

                <ProgressBar
                    android:id="@+id/pbInfo"
                    android:layout_width="25dp"
                    android:layout_height="25dp" />

                <LinearLayout
                    android:id="@+id/llGustInfo"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/tourists"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvAddMember"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:background="@drawable/background_primary_color_radius_100dp"
                        android:paddingHorizontal="16dp"
                        android:paddingVertical="6dp"
                        android:text="@string/add_member"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold" />

                </LinearLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clMemberInfo"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1">

                    <!-- 会员昵称 -->
                    <TextView
                        android:id="@+id/tvMemberName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="4dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_18ssp"
                        android:textStyle="bold"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintEnd_toStartOf="@+id/tvMemberToggle"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="张三" />

                    <TextView
                        android:id="@+id/tvMemberToggle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="8dp"
                        android:paddingHorizontal="10dp"
                        android:paddingVertical="2dp"
                        android:text="@string/toggle_info"
                        android:background="@drawable/background_primary_color_radius_100dp"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_12ssp"
                        app:layout_constraintBottom_toBottomOf="@id/tvMemberName"
                        app:layout_constraintEnd_toStartOf="@+id/tvMemberBalance"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintStart_toEndOf="@id/tvMemberName"
                        app:layout_constraintTop_toTopOf="@id/tvMemberName" />


                    <!-- 余额信息 -->
                    <TextView
                        android:id="@+id/tvMemberBalance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="@id/tvMemberName"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="1"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="余额: ¥1,234.56" />

                    <!-- 会员号 -->
                    <TextView
                        android:id="@+id/tvMemberNumber"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="8dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_14ssp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/tvMemberPhone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvMemberName"
                        tools:text="会员号:138****8888" />

                    <!-- 手机号 -->
                    <TextView
                        android:id="@+id/tvMemberPhone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_14ssp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/tvMemberNumber"
                        app:layout_constraintTop_toTopOf="@id/tvMemberNumber"
                        tools:text="账户:138****8888" />


                </androidx.constraintlayout.widget.ConstraintLayout>


            </LinearLayout>

            <!-- 左侧第二块 - 100dp -->
            <LinearLayout
                android:id="@+id/leftBlock2"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:layout_marginBottom="6dp"
                android:background="@drawable/background_white_radius_4dp"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="16dp">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:text="应收"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_18ssp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="end|center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvReceivableAmount"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="$100 = $ 1000"
                        android:textColor="@color/primaryColor"
                        android:textSize="@dimen/_24ssp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvConversionRatio"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black60"
                        android:textSize="@dimen/_12ssp"
                        tools:text="换算比例" />

                </LinearLayout>

            </LinearLayout>

            <!-- 左侧第三块 - 剩余空间 -->
            <LinearLayout
                android:id="@+id/leftBlock3"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@drawable/background_white_radius_4dp"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingHorizontal="16dp"
                android:paddingTop="16dp">

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1">

                    <LinearLayout
                        android:id="@+id/paymentLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">


                    </LinearLayout>
                </ScrollView>


                <View
                    style="@style/commonDividerStyle"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/black20" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="80dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvChange"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="12dp"
                        android:text=""
                        android:textColor="@color/black"
                        android:textSize="@dimen/_18ssp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="end|center_vertical"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvChangeAmount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/primaryColor"
                            android:textSize="@dimen/_20ssp"
                            android:textStyle="bold"
                            tools:text="$0 = ៛1000" />

                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>

        </LinearLayout>

        <!-- 右侧内容区域 -->
        <LinearLayout
            android:id="@+id/rightBlock1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingStart="6dp">

            <!-- 右侧上半部分 - RecyclerView布局 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/normalPaymentRv"
                android:layout_width="match_parent"
                android:layout_height="312dp"
                android:nestedScrollingEnabled="false"
                android:overScrollMode="never"
                android:scrollbars="none"
                tools:itemCount="9"
                tools:layout_height="312dp"
                tools:listitem="@layout/item_grid_payment" />


            <!-- 右侧下半部分 - 自定义键盘 -->
            <com.metathought.food_order.casheir.ui.widget.KeypadView
                android:id="@+id/rightBlockBottom"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="6dp"
                android:layout_weight="1" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/rightBlock2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingStart="6dp"
            android:visibility="gone">

            <TextView
                android:id="@+id/tvBack"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:drawableStart="@drawable/icon_left_arrow_black"
                android:gravity="center_vertical"
                android:text="@string/go_back"
                android:textColor="@color/black"
                android:textSize="@dimen/_18ssp" />
            <!-- 右侧上半部分 - RecyclerView布局 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/morePaymentRv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="6dp"
                android:nestedScrollingEnabled="false"
                android:overScrollMode="never"
                android:scrollbars="none"
                tools:itemCount="9"
                tools:layout_height="400dp"
                tools:listitem="@layout/item_grid_payment" />

        </LinearLayout>

    </LinearLayout>

    <ProgressBar
        android:id="@+id/pbPaymentSlide"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
