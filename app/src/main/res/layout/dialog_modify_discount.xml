<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/background_white_left_radius_20"
        android:orientation="vertical"
        android:paddingHorizontal="24dp"
        android:paddingVertical="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/viewKeyBoard"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginBottom="20dp"
            android:gravity="center_vertical"
            tools:ignore="UseCompoundDrawables">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/discount_whole_order"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/llContent"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="4dp"
            android:layout_weight="1"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <RadioGroup
                android:id="@+id/radioGroupPaymentMethod"
                android:layout_width="wrap_content"
                android:layout_height="45dp"
                android:layout_gravity="center"
                android:layout_marginBottom="24dp"
                android:background="@drawable/background_e5e5e5_radius_100"
                android:checkedButton="@id/radioAll"
                android:orientation="horizontal"
                android:visibility="visible">

                <RadioButton
                    android:id="@+id/radioNormal"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:background="@drawable/radiobutton_discount_background"
                    android:button="@null"
                    android:checked="true"
                    android:gravity="center"
                    android:paddingHorizontal="25dp"
                    android:paddingVertical="10dp"
                    android:text="@string/selling_price"
                    android:textColor="@drawable/radio_discount_text_selector"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:buttonCompat="@null" />

                <RadioButton
                    android:id="@+id/radioVip"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:background="@drawable/radiobutton_discount_background"
                    android:button="@null"
                    android:gravity="center"
                    android:minHeight="0dp"
                    android:paddingHorizontal="25dp"
                    android:paddingVertical="10dp"
                    android:text="@string/vip_price"
                    android:textColor="@drawable/radio_discount_text_selector"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:buttonCompat="@null" />

            </RadioGroup>

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/deductible_amount"
                            android:textColor="@color/black80"
                            android:textSize="@dimen/_14ssp" />

                        <ImageView
                            android:id="@+id/ivWarn"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="2dp"
                            android:src="@drawable/icon_warn_green"
                            android:visibility="gone"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/totalPrice"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:textColor="@color/black80"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold"
                            tools:text="7200.00" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/totalPriceKhr"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:paddingHorizontal="8dp"
                        android:textColor="@color/black40"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold"
                        tools:text="($1=KHR4100)= KHR 295,200" />

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="16dp">

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/textInputLayoutPercent"
                            style="@style/CustomOutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/reduction_percentage"
                            android:orientation="horizontal"
                            android:textColorHint="@color/black60">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/edtPercent"
                                style="@style/FontLocalization"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawablePadding="10dp"
                                android:inputType="numberDecimal"
                                android:maxLength="60"
                                android:maxLines="1"
                                android:singleLine="true"
                                android:textColor="@color/black"
                                android:textColorHint="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <TextView
                            android:id="@+id/tvUnit"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="12dp"
                            android:text="%"
                            android:textColor="@color/black80"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold" />
                    </FrameLayout>

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp">

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/textInputLayoutUsd"
                            style="@style/CustomOutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:hint="@string/amount_of_reduction_usd"
                            android:orientation="horizontal">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/edtUsd"
                                style="@style/FontLocalization"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:inputType="numberDecimal"
                                android:maxLines="1"
                                android:paddingStart="30dp"
                                android:singleLine="true"
                                android:textColor="@color/black"
                                android:textColorHint="@color/black40"
                                android:textSize="15sp" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:layout_marginTop="20dp"
                            android:text="$"
                            android:textColor="@color/black80"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold" />
                    </FrameLayout>

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp">

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/textInputLayoutKhr"
                            style="@style/CustomOutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:hint="@string/amount_of_reduction_khr">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/edtKhr"
                                style="@style/FontLocalization"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:inputType="number"
                                android:maxLines="1"
                                android:paddingStart="30dp"
                                android:singleLine="true"
                                android:textColor="@color/black"
                                android:textColorHint="@color/black40"
                                android:textSize="15sp" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:layout_marginTop="24dp"
                            android:src="@drawable/icon_km_unit"
                            android:textColor="@color/black80"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold" />
                    </FrameLayout>

                    <LinearLayout
                        android:id="@+id/llResult"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        android:background="@drawable/background_f5f5f5_radius_10"
                        android:orientation="vertical"
                        android:paddingHorizontal="10dp"
                        android:paddingVertical="18dp"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/tvDiscountPercent"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/discounts_percent"
                                android:textColor="@color/black80"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvDiscountValue"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="12dp"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:text="-"
                                android:textColor="@color/black80"
                                android:textSize="@dimen/_16ssp"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp">

                            <TextView
                                android:id="@+id/tvReducePriceTitle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/amount_of_reduction_usd"
                                android:textColor="@color/black80"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvReducePrice"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="12dp"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:text="-"
                                android:textColor="@color/black80"
                                android:textSize="@dimen/_16ssp"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/amount_after_discount"
                                android:textColor="@color/black80"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvReduceRealPrice"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="12dp"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:textColor="@color/black"
                                android:textSize="24sp"
                                android:textStyle="bold"
                                tools:text="7200.00" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvReduceRealPriceKhr"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:text="($1=៛4100)= ៛ 295,200"
                            android:textColor="@color/black"
                            android:textSize="24sp"
                            android:textStyle="bold" />


                    </LinearLayout>

                </LinearLayout>
            </ScrollView>

<!--            <LinearLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:orientation="horizontal">-->

<!--                <com.google.android.material.button.MaterialButton-->
<!--                    android:id="@+id/btnConfirm"-->
<!--                    style="@style/FontLocalization"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="60dp"-->
<!--                    android:layout_marginTop="24dp"-->
<!--                    android:alpha="0.5"-->
<!--                    android:clickable="false"-->
<!--                    android:enabled="false"-->
<!--                    android:gravity="center"-->
<!--                    android:orientation="horizontal"-->
<!--                    android:text="@string/confirm"-->
<!--                    android:textAllCaps="false"-->
<!--                    android:textColor="@color/white"-->
<!--                    android:textSize="@dimen/_18ssp"-->
<!--                    android:textStyle="bold"-->
<!--                    app:backgroundTint="@color/primaryColor"-->
<!--                    app:cornerRadius="15dp"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    app:strokeColor="@color/primaryColor"-->
<!--                    app:strokeWidth="0dp" />-->
<!--            </LinearLayout>-->

        </LinearLayout>
    </LinearLayout>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/layoutMain"
        app:layout_constraintEnd_toEndOf="@id/layoutMain"
        app:layout_constraintStart_toStartOf="@id/layoutMain"
        app:layout_constraintTop_toTopOf="@id/layoutMain"
        tools:visibility="visible" />

    <com.metathought.food_order.casheir.ui.widget.CustomNumberKeyBoardView
        android:id="@+id/viewKeyBoard"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@drawable/background_e7e7e7_right_radius_20dp"
        android:padding="2dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/layoutMain"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>