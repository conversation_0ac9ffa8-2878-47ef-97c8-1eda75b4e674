<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:padding="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:gravity="center">

            <TextView
                android:id="@+id/tvTitle"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:text="@string/sure_to_cancel_credit"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />
        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:text="@string/hint_reason"
            android:layout_marginTop="40dp"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:layout_height="wrap_content"/>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/textInputLayoutReason"
            style="@style/CustomOutlinedBox"
            android:layout_width="match_parent"
            android:layout_marginTop="10dp"
            android:layout_height="wrap_content"
            android:hint="@string/please_enter_the_reason"
            android:textColorHint="@color/black60">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edtReason"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawablePadding="10dp"
                android:gravity="top"
                android:lineSpacingExtra="5dp"
                android:maxLength="200"
                android:textColor="@color/black"
                android:textColorHint="@color/black" />
        </com.google.android.material.textfield.TextInputLayout>

        <LinearLayout
            android:id="@+id/llAutoInStore"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="21dp"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="start"
                android:text="@string/automatically_store_returned_goods"
                android:textColor="@color/black"
                android:textSize="@dimen/_16ssp" />

            <ImageView
                android:id="@+id/ivSwitch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/icon_switch_close" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnNo"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:text="@string/no"
                android:textAllCaps="false"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                app:backgroundTint="@color/white"
                app:cornerRadius="15dp"
                app:elevation="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:strokeColor="@color/black20"
                app:strokeWidth="1dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnYes"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:text="@string/confirm2"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                app:backgroundTint="@color/primaryColor"
                app:cornerRadius="15dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:strokeColor="@color/primaryColor"
                app:strokeWidth="0dp" />

        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
