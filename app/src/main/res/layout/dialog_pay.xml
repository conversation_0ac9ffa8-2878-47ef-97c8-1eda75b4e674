<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_dialog"
    android:orientation="vertical">

    <com.metathought.food_order.casheir.ui.widget.DialogTopBar
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="24dp"
        android:paddingTop="25dp"
        app:dialog_title="@string/pay_now" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="40dp"
        android:layout_weight="1"
        android:focusable="true"
        android:focusableInTouchMode="true">

        <LinearLayout
            android:id="@+id/llContent"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_constraintBottom_toTopOf="@id/llBottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/topBar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="@string/total"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16ssp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvPrice"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="- -"
                        android:textColor="@color/primaryColor"
                        android:textSize="@dimen/_32ssp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvKhrPrice"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:text="- -"
                        android:textColor="@color/primaryColor"
                        android:textSize="@dimen/_32ssp"
                        android:textStyle="bold" />

                </LinearLayout>
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvPaymentChannel"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="10dp"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/item_payment_channel" />

            <View style="@style/commonDividerStyle" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginVertical="10dp"
                android:layout_weight="1">

                <!-- 现金支付 -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clCash"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvConversionRatioTitle"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/conversion_ratio"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tvConversionRatio"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="24dp"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        app:layout_constraintStart_toEndOf="@id/tvConversionRatioTitle"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="$1 = ៛4100" />

                    <TextView
                        android:id="@+id/tvChangeAmountTitle"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="24dp"
                        android:text="@string/back_your_change_amount"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        app:layout_constraintEnd_toStartOf="@id/tvChangeAmount"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tvChangeAmount"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="$9 + ៛41" />

                    <TextView
                        android:id="@+id/tvChangeAmountTip"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingStart="24dp"
                        android:text="@string/insufficient_cash_received"
                        android:textColor="@color/main_red"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvChangeAmount"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tvReceiveTitle"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:text="@string/cash_collection"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        android:visibility="visible"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvConversionRatioTitle" />

                    <FrameLayout
                        android:id="@+id/flUsdAmount"
                        android:layout_width="0dp"
                        android:layout_height="45dp"
                        android:layout_marginTop="4dp"
                        app:layout_constraintEnd_toStartOf="@id/flKhrAmount"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvReceiveTitle">

                        <EditText
                            android:id="@+id/edtUsdAmount"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/selector_editview_bg"
                            android:hint="0"
                            android:inputType="numberDecimal"
                            android:maxLength="15"
                            android:maxLines="1"
                            android:paddingStart="40dp"
                            android:singleLine="true"
                            android:textColor="@color/black"
                            android:textColorHint="@color/black40" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="16dp"
                            android:src="@drawable/icon_dollor_black" />

                    </FrameLayout>


                    <FrameLayout
                        android:id="@+id/flKhrAmount"
                        android:layout_width="0dp"
                        android:layout_height="45dp"
                        android:layout_marginStart="24dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/flUsdAmount"
                        app:layout_constraintTop_toTopOf="@id/flUsdAmount">

                        <EditText
                            android:id="@+id/edtKhrAmount"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/selector_editview_bg"
                            android:hint="0"
                            android:inputType="number"
                            android:maxLength="15"
                            android:maxLines="1"
                            android:paddingStart="40dp"
                            android:singleLine="true"
                            android:textColor="@color/black"
                            android:textColorHint="@color/black40" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="16dp"
                            android:src="@drawable/icon_km_unit" />

                    </FrameLayout>


                    <com.metathought.food_order.casheir.ui.widget.CustomNumberKeyBoardView2
                        android:id="@+id/viewKeyBoard"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_marginTop="10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/viewCustomInput"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/flKhrAmount" />

                    <!--                    <androidx.recyclerview.widget.RecyclerView-->
                    <!--                        android:id="@+id/tvCustomInput"-->
                    <!--                        android:layout_width="0dp"-->
                    <!--                        android:layout_height="0dp"-->
                    <!--                        android:layout_marginStart="24dp"-->
                    <!--                        android:background="@color/black"-->
                    <!--                        app:layout_constraintBottom_toBottomOf="@id/viewKeyBoard"-->
                    <!--                        app:layout_constraintEnd_toEndOf="parent"-->
                    <!--                        app:layout_constraintStart_toEndOf="@id/viewKeyBoard"-->
                    <!--                        app:layout_constraintTop_toTopOf="@id/viewKeyBoard" />-->

                    <com.metathought.food_order.casheir.ui.widget.CustomerQuickInputView
                        android:id="@+id/viewCustomInput"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_marginStart="24dp"
                        app:layout_constraintBottom_toBottomOf="@id/viewKeyBoard"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/viewKeyBoard"
                        app:layout_constraintTop_toTopOf="@id/viewKeyBoard" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clQrCode"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/white"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/layoutQR"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:background="@drawable/rectangle"
                        android:clipToPadding="false"
                        android:orientation="vertical"
                        android:padding="1dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintWidth_percent="0.4">

                        <ImageView
                            android:layout_width="match_parent"
                            android:layout_height="50dp"
                            android:background="@drawable/top_khqr"
                            android:src="@drawable/ic_khqr_logo" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/info_khqr"
                            android:gravity="center_vertical"
                            android:orientation="vertical"
                            android:paddingLeft="30dp"
                            android:paddingTop="8dp"
                            android:paddingBottom="8dp">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tvScanQRName"
                                style="@style/FontLocalization"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginEnd="60dp"
                                android:ellipsize="end"
                                android:textColor="@color/black"
                                android:textSize="14sp"
                                app:autoSizeTextType="uniform"
                                app:layout_constrainedWidth="true"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                tools:text="$KUNTHEA SOT" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tvScanQRAmount"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:lines="1"
                                android:textColor="@color/black"
                                android:textSize="25sp"
                                android:textStyle="bold"
                                app:autoSizeTextType="uniform"
                                app:layout_constrainedWidth="true"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                tools:text="$673.57" />
                        </LinearLayout>

                        <ImageView
                            android:id="@+id/imgQR"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:adjustViewBounds="true"
                            android:padding="30dp" />
                    </LinearLayout>

                    <ProgressBar
                        android:id="@+id/pbQrLoading"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_gravity="center"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clBalance"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/white"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <FrameLayout
                        android:id="@+id/flPhone"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="10dp"
                        app:layout_constraintEnd_toStartOf="@id/btnSearch"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <EditText
                            android:id="@+id/edtPhoneNumber"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="50dp"
                            android:background="@drawable/selector_editview_bg"
                            android:hint="@string/input_phone_number_required"
                            android:inputType="number"
                            android:maxLength="15"
                            android:maxLines="1"
                            android:paddingStart="70dp"
                            android:singleLine="true"
                            android:textColor="@color/black"
                            android:textColorHint="@color/black40" />

                        <!--                        <com.google.android.material.textfield.TextInputLayout-->
                        <!--                            android:id="@+id/textInputLayoutPhoneNumber"-->
                        <!--                            style="@style/CustomOutlinedBox"-->
                        <!--                            android:layout_width="match_parent"-->
                        <!--                            android:layout_height="wrap_content"-->
                        <!--                            android:hint="@string/hint_phone_number_required"-->
                        <!--                            android:textColorHint="@color/bg_progress"-->
                        <!--                            app:startIconDrawable="@drawable/ic_rectangle">-->

                        <!--                            <com.google.android.material.textfield.TextInputEditText-->
                        <!--                                android:id="@+id/edtPhoneNumber"-->
                        <!--                                style="@style/FontLocalization"-->
                        <!--                                android:layout_width="match_parent"-->
                        <!--                                android:layout_height="match_parent"-->
                        <!--                                android:inputType="number"-->
                        <!--                                android:maxLength="14"-->
                        <!--                                android:maxLines="1"-->
                        <!--                                android:singleLine="true"-->
                        <!--                                android:textColor="@color/black"-->
                        <!--                                android:textColorHint="@color/black" />-->

                        <!--                        </com.google.android.material.textfield.TextInputLayout>-->

                        <com.hbb20.CountryCodePicker
                            android:id="@+id/countryCodeHolder"
                            style="@style/FontLocalization"
                            android:layout_width="70dp"
                            android:layout_height="50dp"
                            android:layout_gravity="center_vertical"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            app:ccpDialog_keyboardAutoPopup="false"
                            app:ccp_defaultPhoneCode="855"
                            app:ccp_showFlag="false"
                            app:ccp_showNameCode="false"
                            app:ccp_textSize="15sp"
                            app:layout_constraintStart_toStartOf="parent" />
                    </FrameLayout>

                    <LinearLayout
                        android:id="@+id/btnSearch"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_marginEnd="10dp"
                        android:background="@drawable/button_login_background"
                        android:clickable="false"
                        android:enabled="false"
                        android:focusable="false"
                        android:gravity="center"
                        android:orientation="horizontal"
                        app:layout_constraintBottom_toBottomOf="@id/flPhone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/flPhone">

                        <ImageView
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:indeterminate="true"
                            android:indeterminateTint="@color/mainWhite"
                            android:indeterminateTintMode="src_atop"
                            android:progressTint="@color/mainWhite"
                            android:src="@drawable/ic_search"
                            app:tint="@color/white" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvCustomerNameTitle"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="@string/customer_nickname"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/flPhone" />

                    <TextView
                        android:id="@+id/tvCustomerName"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="— —"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvCustomerNameTitle" />

                    <TextView
                        android:id="@+id/tvCustomerBalanceTitle"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="@string/customer_balance"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/flPhone" />

                    <TextView
                        android:id="@+id/tvCustomerBalance"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="— —"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvCustomerBalanceTitle" />

                    <TextView
                        android:id="@+id/tvInsufficientBalance"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:text="@string/insufficient_balance"
                        android:textColor="@color/main_red"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvCustomerBalance"
                        tools:Visibility="visible" />

                    <TextView
                        android:id="@+id/tvNotFound"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:layout_marginTop="26dp"
                        android:text="@string/user_not_found"
                        android:textColor="@color/black80"
                        android:textSize="@dimen/_18ssp"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/flPhone"
                        tools:visibility="visible" />

                    <!--                    <ImageView-->
                    <!--                        android:id="@+id/pbLoadMember"-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="match_parent"-->
                    <!--                        android:adjustViewBounds="true"-->
                    <!--                        android:padding="30dp"-->
                    <!--                        app:layout_constraintBottom_toBottomOf="parent"-->
                    <!--                        app:layout_constraintEnd_toEndOf="parent"-->
                    <!--                        app:layout_constraintStart_toStartOf="parent"-->
                    <!--                        app:layout_constraintTop_toTopOf="parent" />-->
                    <ProgressBar
                        android:id="@+id/pbLoadMember"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:layout_marginStart="10dp"
                        android:indeterminate="true"
                        android:indeterminateTint="@color/primaryColor"
                        android:indeterminateTintMode="src_atop"
                        android:progressTint="@color/mainWhite"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:visibility="visible" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <LinearLayout
                    android:id="@+id/llOther"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginVertical="30dp"
                    android:background="@color/white"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/tvOtherPayment"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:maxWidth="250dp"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_20ssp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="ABA支付金额ABA支付金额ABA支付金额ABA支付金额" />

                    <TextView
                        android:id="@+id/tvOtherPaymentAmount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_20ssp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="$19.09" />
                </LinearLayout>


            </FrameLayout>

        </LinearLayout>

        <ProgressBar
            android:id="@+id/pbLoading"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_gravity="center"
            app:layout_constraintBottom_toTopOf="@id/llBottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/topBar" />

    </FrameLayout>

    <View style="@style/commonDividerStyle" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/llBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="25dp"
        android:orientation="horizontal"
        android:visibility="visible"
        app:divider="@drawable/shape_option_item_pading_12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:showDividers="middle">

        <TextView
            android:id="@+id/btnMixedPayment"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginEnd="20dp"
            android:layout_weight="1"
            android:background="@drawable/background_white_border_primary_radius_12"
            android:gravity="center"
            android:minWidth="200dp"
            android:paddingHorizontal="10dp"
            android:text="@string/mixed_payment"
            android:textColor="@color/primaryColor"
            android:textSize="@dimen/_18ssp"
            android:textStyle="bold"
            android:visibility="visible" />

        <TextView
            android:id="@+id/btnDone"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="3"
            android:background="@drawable/button_login_background"
            android:gravity="center"
            android:text="@string/confirm2"
            android:textColor="@color/white"
            android:textSize="@dimen/_18ssp"
            android:textStyle="bold"
            android:visibility="visible" />
    </androidx.appcompat.widget.LinearLayoutCompat>

</LinearLayout>