<?xml version="1.0" encoding="utf-8"?>
<cn.bingoogolapple.badgeview.BGABadgeFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:layout_width="200dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardViewMain"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="6dp"
        android:clipToPadding="false"
        android:elevation="0dp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp"
        app:strokeColor="@color/black08"
        app:strokeWidth="1dp">

        <FrameLayout
            android:id="@+id/constraintLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/imgImage"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:background="@color/color_f5f5f5"
                        android:scaleType="center"
                        android:visibility="gone"
                        app:layout_constraintDimensionRatio="1:1"
                        app:layout_constraintEnd_toStartOf="@id/tvGoodName"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tvCouponActivity"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="22dp"
                        android:layout_marginTop="6dp"
                        android:background="@color/main_red"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:paddingHorizontal="6dp"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_12ssp"
                        android:visibility="gone"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintEnd_toEndOf="@id/imgImage"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="第二份半价第二份半价第二份半价第二份半价"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tvGoodName"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="10dp"
                        android:layout_marginTop="6dp"
                        android:layout_marginBottom="6dp"
                        android:ellipsize="end"
                        android:gravity="start|top"
                        android:maxLines="3"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:visibility="visible"
                        app:layout_constraintBottom_toTopOf="@id/tvPrice"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/imgImage"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="蛋糕蛋糕蛋糕蛋糕蛋糕蛋糕蛋糕"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tvPrice"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="6dp"
                        android:autoSizeMinTextSize="@dimen/_12ssp"
                        android:autoSizeStepGranularity="1sp"
                        android:autoSizeTextType="uniform"
                        android:gravity="start"
                        android:maxLines="1"
                        android:textColor="@color/primaryColorDark"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/llCouponActivityWithNoImage"
                        app:layout_constraintStart_toStartOf="@id/tvGoodName"
                        app:layout_constraintTop_toBottomOf="@id/tvGoodName"
                        tools:text="$0.00" />

                    <LinearLayout
                        android:id="@+id/llCouponActivityWithNoImage"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/tvPrice"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="1"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toEndOf="@id/tvPrice"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tvCouponActivityWithNoImage"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="6dp"
                            android:background="@color/main_red"
                            android:ellipsize="end"
                            android:gravity="center_vertical"
                            android:maxLines="1"
                            android:paddingHorizontal="6dp"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_12ssp"
                            tools:text="第二份半价第二份半价第二份半价第二份半价" />
                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>

            <!--无图售罄-->
            <TextView
                android:id="@+id/tvSoldOut"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/black40"
                android:gravity="center"
                android:text="@string/sold_out"
                android:textColor="@color/white"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/llContent"
                app:layout_constraintEnd_toEndOf="@id/llContent"
                app:layout_constraintStart_toStartOf="@id/llContent"
                app:layout_constraintTop_toTopOf="@id/llContent"
                tools:visibility="gone" />

        </FrameLayout>
    </androidx.cardview.widget.CardView>


</cn.bingoogolapple.badgeview.BGABadgeFrameLayout>
