<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/llPrintAgain"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvPrintAgain0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="left"
                    android:gravity="center"
                    android:text="@string/print_again"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvPrintAgain1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="left"
                    android:gravity="center"
                    android:text="@string/print_again"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:visibility="visible" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llStoreName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvFirstStoreName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginHorizontal="20px"
                    android:gravity="center"
                    android:textColor="@color/black"
                    android:textSize="32px"
                    android:textStyle="bold"
                    tools:text="JM美味海鲜餐厅(体验店)" />

                <TextView
                    android:id="@+id/tvSecondStoreName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginHorizontal="20px"
                    android:gravity="center"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:text="JM美味海鲜餐厅(体验店)"
                    tools:visibility="visible" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/llDiningStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvDiningStyle0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        tools:text="Dining-In" />

                    <TextView
                        android:id="@+id/tvDiningStyle1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="ការប្រើប្រាស់ផ្ទៃក្នុង"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llReceipt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvReceipt0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Receipt"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvReceipt1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="បង្កាន់ដៃ"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <!--                <TextView-->
                <!--                    android:id="@+id/tvSeriesNo"-->
                <!--                    android:layout_width="wrap_content"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:textSize="@dimen/_printer_default_sp"-->
                <!--                    android:textStyle="bold"-->
                <!--                    android:visibility="gone"-->
                <!--                    tools:text="NO.0003"-->
                <!--                    tools:visibility="visible" />-->
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15px">

                <LinearLayout
                    android:id="@+id/llTableName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1">

                    <LinearLayout
                        android:id="@+id/llTableNameTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_table_name"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_table_name"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvTableName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10px"
                        android:layout_weight="1"
                        android:gravity="end|top"
                        android:textColor="@color/black"
                        android:textSize="32px"
                        android:textStyle="bold"
                        tools:text="A01" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llPickUpNo"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/llPickUpNoTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_pick_up_no"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:text="@string/print_title_pick_up_no"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvPickUpNo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15px"
                        android:gravity="end|top"
                        android:textColor="@color/black"
                        android:textSize="32px"
                        android:textStyle="bold"
                        tools:text="001" />

                </LinearLayout>


            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15px">

                <LinearLayout
                    android:id="@+id/llDateTime"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvDateTime0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_datetime"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvDateTime1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_datetime"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>


                <TextView
                    android:id="@+id/tvOrderTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="2023-10-10 10:10:10" />
            </LinearLayout>

            <!--            <include-->
            <!--                android:id="@+id/llOrderInfoTitleDivider"-->
            <!--                layout="@layout/dot_divider"-->
            <!--                android:visibility="gone"-->
            <!--                tools:visibility="visible" />-->
            <include
                android:id="@+id/llOrderInfoTitleDivider"
                layout="@layout/dot_divider"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="0dp"
                android:visibility="gone"
                tools:visibility="visible" />


            <LinearLayout
                android:id="@+id/llItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/llItemName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.2"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:text="Item"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:text="Item"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llQty"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:gravity="center_horizontal"
                        android:text="Qty"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:gravity="center_horizontal"
                        android:text="Qty"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llUnitPrice"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.4"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvUnitPrice0"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:text="Price"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvUnitPrice1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:text="Price"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llItemTotal"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.4"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvItemTotal0"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:text="Price"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvItemTotal1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:text="Price"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="2"
                tools:listitem="@layout/item_ticket_order_menu"
                tools:visibility="visible" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvZplist"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="2"
                tools:listitem="@layout/item_ticket_order_menu"
                tools:visibility="visible" />

            <LinearLayout
                android:id="@+id/llCancelGoods"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="10px"
                android:gravity="center"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:text="— — — — —"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp" />

                <LinearLayout
                    android:id="@+id/llCancelGoodsTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10px"
                    android:layout_marginEnd="10px"
                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/rightStar"
                    app:layout_constraintStart_toEndOf="@id/leftStar"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/cancel_dish"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/cancel_dish"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />

                </LinearLayout>


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="start"
                    android:text="— — — — —"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp" />


            </LinearLayout>


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvCancelGoodList"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="2"
                tools:listitem="@layout/item_ticket_order_menu"
                android:visibility="gone"
                tools:visibility="visible"/>

            <include layout="@layout/dot_divider" />

            <LinearLayout
                android:id="@+id/llRemark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llRemarkTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_remark"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_remark"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>


                <TextView
                    android:id="@+id/tvRemark"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    tools:text="No Spicy! No chili, ginger, onion, garlic" />

                <include layout="@layout/dot_divider" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llPriceLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llSubtotal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/llSubtotalTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvSubtotal0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_subtotal"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvSubtotal1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_subtotal"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvSubtotalUsdPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            tools:text="$10" />

                        <TextView
                            android:id="@+id/tvVipSubtotalPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:text="-៛40731"
                            tools:visibility="visible" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llCouponActivity"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10px"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/llCouponActivityTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        >

                        <TextView
                            android:id="@+id/couponActivityTitle1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/discount_activity"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/couponActivityTitle2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/discount_activity"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvDiscountActivityAmount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            tools:text="$10" />

                        <TextView
                            android:id="@+id/tvVipDiscountActivityAmount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:text="-៛40731" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llPackPrice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10px">

                    <LinearLayout
                        android:id="@+id/llPackPriceTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_pack_fee"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_pack_fee"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvPackUsdPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            tools:text="$10" />

                        <TextView
                            android:id="@+id/tvPackKhrPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:text="-៛40731" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llServiceFee"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10px">

                    <LinearLayout
                        android:id="@+id/llServiceFeeTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvServiceFee0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_service_fee"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvServiceFee1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_service_fee"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvServiceFeePrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            tools:text="$10" />

                        <TextView
                            android:id="@+id/tvVipServiceFeePrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:text="-៛40731"
                            tools:visibility="visible" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llVat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10px">

                    <LinearLayout
                        android:id="@+id/llVatTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvVat0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_vat"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvVat1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_vat"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvVatUsdPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            tools:text="$10" />

                        <TextView
                            android:id="@+id/tvVipVatPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:text="-៛40731"
                            tools:visibility="visible" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llCoupon"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10px">

                    <LinearLayout
                        android:id="@+id/llCouponTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvCoupon0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_coupon"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvCoupon1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_coupon"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvCouponPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            tools:text="$10" />

                        <TextView
                            android:id="@+id/tvVipCouponPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            tools:text="$10" />

                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llDiscount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10px">

                    <LinearLayout
                        android:id="@+id/llDiscountTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvDiscount0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_discount_per"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvDiscount1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_discount_per"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvDiscountPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            tools:text="$10" />

                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llDiscountAmount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10px">

                    <LinearLayout
                        android:id="@+id/llDiscountAmountTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvDiscountAmount0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_discount_per"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvDiscountAmount1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_discount_per"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvDiscountAmountPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            tools:text="$10" />

                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:id="@+id/llTotalTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvTotal0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_total"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_total"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvTotalUsdPrice"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="40px"
                        android:textStyle="bold"
                        tools:text="$10" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvTotalVipPrice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:text="VIP $40731"
                        tools:visibility="visible" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llConversionRatio"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tvConversionRatio"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="visible"
                        tools:text="($1=KHR4123)=៛40731"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tvTotalKhrPrice"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="visible"
                        tools:text="($1=KHR4123)=៛40731"
                        tools:visibility="visible" />

                </LinearLayout>

            </LinearLayout>

            <!--            <LinearLayout-->
            <!--                android:id="@+id/llBalancePayment"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginTop="10px"-->
            <!--                android:orientation="horizontal"-->
            <!--                android:visibility="gone"-->
            <!--                tools:visibility="visible">-->

            <!--                <LinearLayout-->
            <!--                    android:id="@+id/llBalancePaymentTitle"-->
            <!--                    android:layout_width="0dp"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_weight="1"-->
            <!--                    android:orientation="vertical">-->

            <!--                    <TextView-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:text="@string/print_title_paid_by_balance"-->
            <!--                        android:textColor="@color/black"-->
            <!--                        android:textSize="@dimen/_printer_default_sp"-->
            <!--                        android:textStyle="bold" />-->

            <!--                    <TextView-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:text="@string/print_title_paid_by_balance"-->
            <!--                        android:textColor="@color/black"-->
            <!--                        android:textSize="@dimen/_printer_default_sp"-->
            <!--                        android:textStyle="bold"-->
            <!--                        android:visibility="gone"-->
            <!--                        tools:visibility="visible" />-->
            <!--                </LinearLayout>-->

            <!--                <LinearLayout-->
            <!--                    android:layout_width="wrap_content"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:gravity="end"-->
            <!--                    android:orientation="vertical">-->

            <!--                    <TextView-->
            <!--                        android:id="@+id/tvBalanceUsdPrice"-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:textColor="@color/black"-->
            <!--                        android:textSize="@dimen/_printer_default_sp"-->
            <!--                        android:textStyle="bold"-->
            <!--                        tools:text="$10" />-->

            <!--                    <TextView-->
            <!--                        android:id="@+id/tvBalanceKhrPrice"-->
            <!--                        android:layout_width="wrap_content"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:textColor="@color/black"-->
            <!--                        android:textSize="@dimen/_printer_default_sp"-->
            <!--                        android:textStyle="bold"-->
            <!--                        android:visibility="gone"-->
            <!--                        tools:text="៛40731" />-->
            <!--                </LinearLayout>-->
            <!--            </LinearLayout>-->


            <LinearLayout
                android:id="@+id/llConfirmed"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/llConfirmedTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_confirmed"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/print_title_confirmed"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>


            </LinearLayout>

            <LinearLayout
                android:id="@+id/llPaidLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10px"
                android:orientation="vertical">


                <include layout="@layout/dot_divider" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10px"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/llPaidTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_paid"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_paid"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvPaymentTypeEn"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        tools:text="User BalanceUser" />

                    <TextView
                        android:id="@+id/tvPaymentTypeKh"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:text="" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llReceived"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10px"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/llReceivedTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_received"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_received"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvReceivedAmount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            tools:text="$10" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:text="៛40731" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llChange"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10px"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/llChangeTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_change"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_change"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10px"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvChangeAmount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            tools:text="$10" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:text="៛40731" />
                    </LinearLayout>
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llOrderInfoLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10px"
                android:orientation="vertical">


                <include layout="@layout/dot_divider" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15px">

                    <LinearLayout
                        android:id="@+id/llOrderNoTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_order_no"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_order_no"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>


                    <TextView
                        android:id="@+id/tvOrderNo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        tools:text="9876543321234567899" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llCustomerName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5px">

                    <LinearLayout
                        android:id="@+id/llCustomerNameTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_customer_name"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_customer_name"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>


                    <TextView
                        android:id="@+id/tvCustomerName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15px"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        tools:text="JiaJiaJia" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llAccountBalance"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5px"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/llAccountBalanceTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/account_balance"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/account_balance"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>


                    <TextView
                        android:id="@+id/tvAccountBalance"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15px"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        tools:text="JiaJiaJia" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llPaymentTime"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5px">

                    <LinearLayout
                        android:id="@+id/llPaymentTimeTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_payment_time"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_payment_time"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>


                    <TextView
                        android:id="@+id/tvPaymentTime"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        tools:text="2023-10-10 10:10:10" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llServiceLine"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5px"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/llServiceLineTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_service_line"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/print_title_service_line"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvServiceLine"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        tools:text="855 *********" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llCompanyInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <include layout="@layout/dot_divider" />

                    <LinearLayout
                        android:id="@+id/llCompanyName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5px"
                        android:orientation="vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <LinearLayout
                            android:id="@+id/llCompanyNameTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/companyNameTitle0"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/print_title_company_name"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_printer_default_sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/companyNameTitle1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10px"
                                android:text="@string/print_title_company_name"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_printer_default_sp"
                                android:textStyle="bold"
                                android:visibility="gone"
                                tools:visibility="visible" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvCompanyName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="start"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            tools:text="855 *********" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llInvoiceNumber"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5px"
                        android:orientation="vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <LinearLayout
                            android:id="@+id/llInvoiceNumberTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tvInvoiceNumber0"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/print_title_invoiceNumber"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_printer_default_sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tvInvoiceNumber1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10px"
                                android:text="@string/print_title_invoiceNumber"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_printer_default_sp"
                                android:textStyle="bold"
                                android:visibility="gone"
                                tools:visibility="visible" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvInvoiceNumber"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="start"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            tools:text="855 *********" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llVatTin"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5px"
                        android:orientation="vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <LinearLayout
                            android:id="@+id/llVatTinTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tvVatTin0"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/print_title_vat_tin"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_printer_default_sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tvVatTin1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10px"
                                android:text="@string/print_title_vat_tin"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_printer_default_sp"
                                android:textStyle="bold"
                                android:visibility="gone"
                                tools:visibility="visible" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvVatTin"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="start"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            tools:text="855 *********" />
                    </LinearLayout>


                    <LinearLayout
                        android:id="@+id/llCompanyAddress"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5px"
                        android:orientation="vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <LinearLayout
                            android:id="@+id/llCompanyAddressTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/print_title_company_address"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_printer_default_sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10px"
                                android:text="@string/print_title_company_address"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_printer_default_sp"
                                android:textStyle="bold"
                                android:visibility="gone"
                                tools:visibility="visible" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvCompanyAddress"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="start"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            tools:text="855 *********" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llContactPhone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5px"
                        android:orientation="vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <LinearLayout
                            android:id="@+id/llContactPhoneTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/print_title_contact_number"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_printer_default_sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10px"
                                android:text="@string/print_title_contact_number"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_printer_default_sp"
                                android:textStyle="bold"
                                android:visibility="gone"
                                tools:visibility="visible" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvContactPhone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="start"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            tools:text="855 *********" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llContactEmail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5px"
                        android:orientation="vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <LinearLayout
                            android:id="@+id/llContactEmailTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/print_title_contact_email"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_printer_default_sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10px"
                                android:text="@string/print_title_contact_email"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_printer_default_sp"
                                android:textStyle="bold"
                                android:visibility="gone"
                                tools:visibility="visible" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvContactEmail"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="start"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_printer_default_sp"
                            android:textStyle="bold"
                            tools:text="855 *********" />
                    </LinearLayout>
                </LinearLayout>

                <include layout="@layout/dot_divider" />

                <TextView
                    android:id="@+id/tvThankYouWords"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="10px"
                    android:gravity="center"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:text="哈哈"
                    tools:visibility="visible" />
            </LinearLayout>


            <LinearLayout
                android:id="@+id/llPaymentQrCode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10px"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <include layout="@layout/dot_divider" />

                <LinearLayout
                    android:id="@+id/llScanCue"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10px"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvScanCueEn"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:gravity="center"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:text="@string/print_scan_to_pay"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tvScanCueKm"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:gravity="center"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:text="@string/print_scan_to_pay"
                        tools:visibility="visible" />

                </LinearLayout>


                <ImageView
                    android:id="@+id/ivOrderQr"
                    android:layout_width="250px"
                    android:layout_height="250px"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginVertical="40px" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llPassAppCoupon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5px"
                android:gravity="center_vertical"
                android:visibility="visible"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/llPassAppCouponTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/pass_app_promo_code"
                        android:textColor="@color/black"
                        android:textSize="26px"
                        android:textStyle="bold" />


                </LinearLayout>

                <TextView
                    android:id="@+id/tvPassAppCoupon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="PASSMPOS"
                    android:textColor="@color/black"
                    android:textSize="26px"
                    android:textStyle="bold" />
            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/llThankYouLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="10px"
                android:gravity="center">

                <TextView
                    android:id="@+id/leftStar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:singleLine="true"
                    android:text="*************************************"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="@id/llThankYou"
                    app:layout_constraintEnd_toStartOf="@id/llThankYou"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/llThankYou" />

                <LinearLayout
                    android:id="@+id/llThankYou"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10px"
                    android:layout_marginEnd="10px"
                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/rightStar"
                    app:layout_constraintStart_toEndOf="@id/leftStar"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Powered by MPOS Cambodia"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Phone: +855 1122 3328"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="https://www.m-pos.cc/"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_printer_default_sp"
                        android:textStyle="bold" />

                </LinearLayout>


                <TextView
                    android:id="@+id/rightStar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:singleLine="true"
                    android:text="*************************************"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="@id/llThankYou"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/llThankYou"
                    app:layout_constraintTop_toTopOf="@id/llThankYou" />


            </androidx.constraintlayout.widget.ConstraintLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="80px" />
        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>