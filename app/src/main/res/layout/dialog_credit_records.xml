<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/mainLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:gravity="end"
            android:orientation="horizontal"
            tools:ignore="UseCompoundDrawables">

            <TextView
                android:id="@+id/tvDialogName"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="start"
                android:text="@string/credit_records"
                android:textColor="@color/black"
                android:textSize="22sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="16dp">


            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnPrint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:paddingStart="24dp"
                android:paddingTop="4dp"
                android:paddingEnd="24dp"
                android:paddingBottom="4dp"
                android:text="@string/printer"
                android:textColor="@color/white"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                app:backgroundTint="@color/primaryColor"
                app:cornerRadius="8dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <LinearLayout
                android:id="@+id/recordsLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/background_efefef_radius_12"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constrainedHeight="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/btnPrint">

                <LinearLayout
                    android:id="@+id/titleCreditRecords"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingVertical="15dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1.2"
                        android:paddingHorizontal="16dp"
                        android:text="@string/order_id"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1.2"
                        android:paddingHorizontal="16dp"
                        android:text="@string/credit_amount"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1.2"
                        android:paddingHorizontal="16dp"
                        android:text="@string/time"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingHorizontal="16dp"
                        android:text="@string/status"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingHorizontal="16dp"
                        android:text="@string/operate"
                        android:textColor="@color/black"
                        android:textSize="16sp" />
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/titlePaymentRecords"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingVertical="15dp"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingHorizontal="16dp"
                        android:text="@string/amount"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingHorizontal="16dp"
                        android:text="@string/payment_method"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingHorizontal="16dp"
                        android:text="@string/time"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                </LinearLayout>

                <View style="@style/commonDividerStyle" />

                <com.scwang.smart.refresh.layout.SmartRefreshLayout
                    android:id="@+id/refreshLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <com.scwang.smart.refresh.header.MaterialHeader
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvRecords"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/labelCreditRecords"
                        tools:itemCount="1"
                        tools:listitem="@layout/item_credit_records" />

                    <com.scwang.smart.refresh.footer.ClassicsFooter
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </com.scwang.smart.refresh.layout.SmartRefreshLayout>


            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/mainLayout"
        app:layout_constraintEnd_toEndOf="@+id/mainLayout"
        app:layout_constraintStart_toStartOf="@+id/mainLayout"
        app:layout_constraintTop_toTopOf="@+id/mainLayout" />

</androidx.constraintlayout.widget.ConstraintLayout>
