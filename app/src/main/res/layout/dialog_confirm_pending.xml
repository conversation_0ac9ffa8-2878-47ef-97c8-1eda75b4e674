<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <RelativeLayout
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/layoutTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:ignore="UseCompoundDrawables">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:text="@string/pending_order"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/layoutTitle"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">


                <LinearLayout
                    android:id="@+id/layoutInfor"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/tvTitle"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="30dp"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/series_number"
                            android:textColor="@color/black50"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvSeriesNumber"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            tools:text="001" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="15dp"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/items"
                            android:textColor="@color/black50"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvItemsCount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            tools:text="09" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="10dp"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/total"
                            android:textColor="@color/black50"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvTotal"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>
                </LinearLayout>


                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutRemark"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:hint="@string/remark"
                    android:textColorHint="@color/bg_progress"
                    android:visibility="visible">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtRemark"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="top"
                        android:inputType="text|textMultiLine"
                        android:maxLength="100"
                        android:maxLines="4"
                        android:textColor="@color/black" />

                </com.google.android.material.textfield.TextInputLayout>


                <LinearLayout
                    android:id="@+id/btnPending"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="16dp"
                    android:alpha="0.5"
                    android:background="@drawable/button_login_background"
                    android:clickable="false"
                    android:enabled="false"
                    android:focusable="false"
                    android:gravity="center"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/pending"
                        android:textColor="@color/mainWhite"
                        android:textSize="18sp" />

                </LinearLayout>

            </LinearLayout>
        </ScrollView>

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_centerInParent="true"
            android:visibility="gone" />
    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
