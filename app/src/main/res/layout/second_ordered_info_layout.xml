<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/mainWhite"
        android:backgroundTint="@color/mainWhite"
        android:gravity="center_vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/imgLogo"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:src="@drawable/ic_logo" />

        <TextView
            android:id="@+id/tvStoreName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:textColor="@color/black"
            tools:text="@string/brand_name" />

    </LinearLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingHorizontal="15dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutMenu"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@drawable/background_menu_fragment"
            android:paddingHorizontal="15dp"
            android:paddingVertical="10dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.49">

            <TextView
                android:id="@+id/tvTableID"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:text=""
                android:textColor="@color/black"
                android:textSize="25sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toStartOf="@id/cardStatus"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="A001" />

            <androidx.cardview.widget.CardView
                android:id="@+id/cardStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:elevation="0dp"
                app:cardBackgroundColor="@color/paid_backgroud_color"
                app:cardCornerRadius="5dp"
                app:cardElevation="0dp"
                app:layout_constraintBottom_toBottomOf="@id/tvTableID"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvTableID"
                app:layout_constraintTop_toTopOf="@id/tvTableID">

                <TextView
                    android:id="@+id/tvDiningStyle"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical|end"
                    android:maxLines="1"
                    android:paddingHorizontal="10dp"
                    android:paddingVertical="5dp"
                    android:textColor="@color/paid_text_color"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="堂食" />
            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/background_dialog"
                android:orientation="vertical"
                android:padding="10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTableID">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/items"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="5dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="2"
                        android:text="@string/items"
                        android:textColor="@color/black60"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/quantity"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="5dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1.2"
                        android:gravity="center_horizontal"
                        android:text="@string/quantity"
                        android:textColor="@color/black60"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/amount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="5dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        android:gravity="center_horizontal"
                        android:text="@string/amount"
                        android:textColor="@color/black60"
                        android:textSize="14sp" />
                </LinearLayout>

                <View style="@style/commonDividerStyle" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/orderedInfoRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="5"
                    tools:listitem="@layout/item_second_ordered_food" />

            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutResumeOrder"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@drawable/background_menu_fragment"
            android:paddingHorizontal="15dp"
            android:paddingVertical="10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.49">

            <LinearLayout
                android:id="@+id/layoutDetailInfo"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible">


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/background_dialog"
                    android:orientation="vertical"
                    android:paddingVertical="10dp"
                    android:paddingStart="8dp"
                    android:paddingEnd="12dp">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:id="@+id/llCustomerTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content">

                            <ImageView
                                android:layout_width="3dp"
                                android:layout_height="14dp"
                                android:layout_gravity="center_vertical"
                                android:src="@drawable/shape_second_ordered_arc" />

                            <TextView
                                android:id="@+id/tvCustomerTitle"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="5dp"
                                android:text="@string/customer_info"
                                android:textColor="@color/paid_text_color"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:visibility="visible" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llCustomerName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/customerName"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/customer_name"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvCustomerName"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="林超正" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llCustomerPhone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/phoneNumber"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/phone_number"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvCustomerPhone"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="15006099788" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp">

                            <ImageView
                                android:layout_width="3dp"
                                android:layout_height="14dp"
                                android:layout_gravity="center_vertical"
                                android:src="@drawable/shape_second_ordered_arc" />

                            <TextView
                                android:id="@+id/orderInfo"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp"
                                android:text="@string/order_info"
                                android:textColor="@color/paid_text_color"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp">

                            <TextView
                                android:id="@+id/orderId"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/order_id"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvOrderNo"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="20241234567890" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp">

                            <TextView
                                android:id="@+id/orderedTime"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/ordered_time"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvOrderTime"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="2024/04/02 12:00:00" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp">

                            <TextView
                                android:id="@+id/orderType"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/order_type"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvOrderType"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="Dine In" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llPeople"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp">

                            <TextView
                                android:id="@+id/people"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginEnd="15dp"
                                android:text="@string/people"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvCustomerNum"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="5" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp">

                            <TextView
                                android:id="@+id/orderBy"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/order_by"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvOrderBy"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="Kiosk" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llPaymentMethod"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:visibility="gone">

                            <TextView
                                android:id="@+id/paymentMethod"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/payment_method"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvPaymentMethod"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="U-Pay" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llDiningTime"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp">

                            <TextView
                                android:id="@+id/diningTime"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/ordered_dining_time"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvDiningTime"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="2024/04/02 12:00:00" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llCancelTime"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:visibility="gone">

                            <TextView
                                android:id="@+id/cancelTime"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/cancel_time"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvCancelTime"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="2024/04/02 12:00:00" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llPaymentTime"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:visibility="gone">

                            <TextView
                                android:id="@+id/paymentTime"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/payment_time"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvPaymentTime"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="2024/04/02 12:00:00" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llRefundTime"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:visibility="gone">

                            <TextView
                                android:id="@+id/refundTime"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/refund_time"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvRefundTime"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="2024/04/02 12:00:00" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1">

                            <TextView
                                android:id="@+id/remark"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/remark"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_12ssp" />

                            <TextView
                                android:id="@+id/tvOrderRemark"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="多加点醋，不吃辣，葱多放点可以吗？备注信息比较多的时候自动换行" />
                        </LinearLayout>
                    </LinearLayout>


                </LinearLayout>


                <LinearLayout
                    android:id="@+id/layoutTotal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="5dp"
                    android:layout_marginTop="5dp"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent">

                    <LinearLayout
                        android:id="@+id/llPackPrice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/packingPrice"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="4dp"
                            android:gravity="center_vertical"
                            android:text="@string/packing_price"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />


                        <TextView
                            android:id="@+id/tvPackingAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/subtotal"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/subtotal"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <TextView
                            android:id="@+id/tvSubtotal"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llVat"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/vat"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/vat"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <TextView
                            android:id="@+id/tvVat"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llTotalPrice2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/total2"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/total_price"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <TextView
                            android:id="@+id/tvTotalPrice2"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llPartialRefundAmount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/partialRefundAmount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/partial_refund_amount"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <TextView
                            android:id="@+id/tvPartialRefundAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/main_red"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="-$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llPartialRefundPackFee"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/refund_pack_fee"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <TextView
                            android:id="@+id/tvPartialRefundPackFee"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/main_red"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="-$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llVatRefundAmount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/vatRefund"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/vat_refund"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <TextView
                            android:id="@+id/tvVatRefundAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/main_red"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="-$99.99" />
                    </LinearLayout>


                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llTotalPrice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="5dp"
                    android:layout_marginBottom="5dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/totalPrice"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="15dp"
                        android:gravity="center_vertical"
                        android:text="@string/total_price"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:orientation="vertical">


                        <TextView
                            android:id="@+id/tvTotalPrice"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/primaryColor"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            app:layout_constrainedWidth="true"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="$0" />

                        <TextView
                            android:id="@+id/tvVipPrice"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="2dp"
                            android:layout_marginTop="4dp"
                            android:drawableStart="@drawable/icon_vip"
                            android:drawablePadding="2dp"
                            android:textColor="@color/member_price_color"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:text="$0.00"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <!--                    <TextView-->
                    <!--                        android:id="@+id/tvTotalPrice"-->
                    <!--                        style="@style/FontLocalization"-->
                    <!--                        android:layout_width="0dp"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_weight="1"-->
                    <!--                        android:gravity="end"-->
                    <!--                        android:maxLines="1"-->
                    <!--                        android:textColor="@color/black"-->
                    <!--                        android:textSize="18sp"-->
                    <!--                        android:textStyle="bold"-->
                    <!--                        tools:text="$99.99" />-->
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llActualReceiveAmount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="5dp"
                    android:layout_marginBottom="5dp"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/actualReceivedAmount"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="15dp"
                        android:gravity="center_vertical"
                        android:text="@string/actual_received_amount"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <TextView
                        android:id="@+id/tvActualReceiveAmount"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:maxLines="1"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        tools:text="$99.99" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llRefundAmount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="5dp"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/refundAmount"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="15dp"
                        android:gravity="center_vertical"
                        android:text="@string/refund_amount"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <TextView
                        android:id="@+id/tvRefundAmount"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:maxLines="1"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        tools:text="$99.99" />
                </LinearLayout>

            </LinearLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>