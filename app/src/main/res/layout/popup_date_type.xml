<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/background_popup_dropdown"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:padding="10dp">

    <TextView
        android:id="@+id/tvToday"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center"
        android:text="@string/today"
        android:textColor="@color/black" />

    <TextView
        android:id="@+id/tvWeek"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center"
        android:text="@string/this_week"
        android:textColor="@color/black" />

    <TextView
        android:id="@+id/tvMonth"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center"
        android:text="@string/this_month"
        android:textColor="@color/black" />

    <TextView
        android:id="@+id/tvQuarter"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center"
        android:text="@string/this_quarter"
        android:textColor="@color/black" />

    <TextView
        android:id="@+id/tvYear"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center"
        android:text="@string/this_year"
        android:textColor="@color/black" />

</LinearLayout>