<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/background_efefef_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginBottom="10dp"
            android:gravity="end"
            android:orientation="horizontal"
            tools:ignore="UseCompoundDrawables">

            <TextView
                android:id="@+id/tvDialogName"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="start"
                android:text="@string/add_goods_info_detail"
                android:textColor="@color/black"
                android:textSize="22sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvList"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:minHeight="240dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="3"
            tools:listitem="@layout/item_receive_order_info_type" />


    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>