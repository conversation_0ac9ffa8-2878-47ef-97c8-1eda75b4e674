<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1.4"
        android:background="@drawable/background_white_left_radius_20"
        android:orientation="vertical">

        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="24dp"
            android:paddingVertical="25dp"
            app:dialog_title="@string/mixed_payment" />

        <ScrollView
            android:id="@+id/scrollView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:paddingHorizontal="16dp"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/background_efefef_radius_12"
                    android:gravity="center_vertical"
                    android:minHeight="90dp"
                    android:paddingVertical="12dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="6dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvTotalPrice"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:textColor="@color/primaryColor"
                            android:textSize="@dimen/_20ssp"
                            android:textStyle="bold"
                            tools:text="$199.99" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:gravity="center"
                            android:text="@string/total_price"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="4dp"
                        android:text=" = "
                        android:textColor="@color/black"
                        android:textSize="@dimen/_20ssp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="6dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvBalancePrice"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="$ - -"
                            android:textColor="@color/primaryColor"
                            android:textSize="@dimen/_20ssp"
                            android:textStyle="bold" />

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:gravity="center"
                            android:text="@string/pay_by_balance"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=" + "
                        android:textColor="@color/black"
                        android:textSize="@dimen/_20ssp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="6dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvCashUsd"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="$ - -"
                            android:textColor="@color/primaryColor"
                            android:textSize="@dimen/_20ssp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvCashKhr"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="៛ - -"
                            android:textColor="@color/primaryColor"
                            android:textSize="@dimen/_20ssp"
                            android:textStyle="bold"
                            android:visibility="gone" />

                        <TextView
                            android:id="@+id/tvCashUsdTitle"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:ellipsize="end"
                            android:gravity="center"
                            android:maxLines="1"
                            android:text="@string/pay_by_cash"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold" />

                    </LinearLayout>


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvBalancePay"
                        style="@style/FontLocalization"
                        android:layout_width="135dp"
                        android:layout_height="wrap_content"
                        android:background="@drawable/button_login_background"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:maxLines="1"
                        android:minHeight="50dp"
                        android:paddingHorizontal="5dp"
                        android:text="@string/pay_by_balance"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_14ssp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:orientation="vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/amount"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold" />

                        <FrameLayout
                            android:id="@+id/flBalanceAmount"
                            android:layout_width="match_parent"
                            android:layout_height="45dp"
                            android:layout_marginTop="4dp"
                            app:layout_constraintEnd_toStartOf="@id/flKhrAmount"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tvReceiveTitle">

                            <EditText
                                android:id="@+id/edtBalanceAmount"
                                style="@style/FontLocalization"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@drawable/selector_editview_bg"
                                android:inputType="numberDecimal"
                                android:maxLength="15"
                                android:maxLines="1"
                                android:paddingStart="40dp"
                                android:singleLine="true"
                                android:textColor="@color/black"
                                android:textColorHint="@color/black40" />

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="16dp"
                                android:src="@drawable/icon_dollor_black" />

                        </FrameLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/clBalance"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:background="@drawable/background_efefef_radius_12"
                            android:padding="10dp">

                            <FrameLayout
                                android:id="@+id/flPhone"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginEnd="10dp"
                                app:layout_constraintEnd_toStartOf="@id/btnSearch"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent">

                                <EditText
                                    android:id="@+id/edtPhoneNumber"
                                    style="@style/FontLocalization"
                                    android:layout_width="match_parent"
                                    android:layout_height="50dp"
                                    android:background="@drawable/selector_editview_bg"
                                    android:hint="@string/input_phone_number_required"
                                    android:inputType="number"
                                    android:maxLength="15"
                                    android:maxLines="1"
                                    android:paddingStart="70dp"
                                    android:singleLine="true"
                                    android:textColor="@color/black"
                                    android:textColorHint="@color/black40" />

                                <!--                                -->
                                <!--                                <com.google.android.material.textfield.TextInputLayout-->
                                <!--                                    android:id="@+id/textInputLayoutPhoneNumber"-->
                                <!--                                    style="@style/CustomOutlinedBox"-->
                                <!--                                    android:background="@color/white"-->
                                <!--                                    android:layout_width="match_parent"-->
                                <!--                                    android:layout_height="wrap_content"-->
                                <!--                                    android:hint="@string/hint_phone_number_required"-->
                                <!--                                    android:textColorHint="@color/bg_progress"-->
                                <!--                                    app:startIconDrawable="@drawable/ic_rectangle">-->

                                <!--                                    <com.google.android.material.textfield.TextInputEditText-->
                                <!--                                        android:id="@+id/edtPhoneNumber"-->
                                <!--                                        style="@style/FontLocalization"-->
                                <!--                                        android:layout_width="match_parent"-->
                                <!--                                        android:layout_height="match_parent"-->
                                <!--                                        android:inputType="phone"-->
                                <!--                                        android:maxLength="14"-->
                                <!--                                        android:maxLines="1"-->
                                <!--                                        android:singleLine="true"-->
                                <!--                                        android:textColor="@color/black"-->
                                <!--                                        android:textColorHint="@color/black" />-->

                                <!--                                </com.google.android.material.textfield.TextInputLayout>-->

                                <com.hbb20.CountryCodePicker
                                    android:id="@+id/countryCodeHolder"
                                    style="@style/FontLocalization"
                                    android:layout_width="70dp"
                                    android:layout_height="50dp"
                                    android:layout_gravity="center_vertical"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal"
                                    app:ccpDialog_keyboardAutoPopup="false"
                                    app:ccp_defaultPhoneCode="855"
                                    app:ccp_showFlag="false"
                                    app:ccp_showNameCode="false"
                                    app:ccp_textSize="15sp"
                                    app:layout_constraintStart_toStartOf="parent" />
                            </FrameLayout>

                            <LinearLayout
                                android:id="@+id/btnSearch"
                                android:layout_width="50dp"
                                android:layout_height="50dp"
                                android:alpha="0.5"
                                android:background="@drawable/button_login_background"
                                android:clickable="false"
                                android:enabled="false"
                                android:focusable="false"
                                android:gravity="center"
                                android:orientation="horizontal"
                                app:layout_constraintBottom_toBottomOf="@id/flPhone"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="@id/flPhone">

                                <ImageView
                                    android:layout_width="30dp"
                                    android:layout_height="30dp"
                                    android:indeterminate="true"
                                    android:indeterminateTint="@color/mainWhite"
                                    android:indeterminateTintMode="src_atop"
                                    android:progressTint="@color/mainWhite"
                                    android:src="@drawable/ic_search"
                                    app:tint="@color/white" />
                            </LinearLayout>

                            <TextView
                                android:id="@+id/tvCustomerNameTitle"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:layout_marginTop="16dp"
                                android:text="@string/customer_nickname"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/flPhone" />

                            <TextView
                                android:id="@+id/tvCustomerName"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="16dp"
                                android:text="- -"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                app:layout_constraintStart_toStartOf="@id/tvCustomerNameTitle"
                                app:layout_constraintTop_toBottomOf="@+id/tvCustomerNameTitle" />

                            <TextView
                                android:id="@+id/tvCustomerBalanceTitle"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="16dp"
                                android:layout_marginEnd="10dp"
                                android:text="@string/customer_balance"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/flPhone" />

                            <TextView
                                android:id="@+id/tvCustomerBalance"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="16dp"
                                android:text="- -"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                app:layout_constraintEnd_toEndOf="@id/tvCustomerBalanceTitle"
                                app:layout_constraintTop_toBottomOf="@id/tvCustomerBalanceTitle" />

                            <TextView
                                android:id="@+id/tvNotFound"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:layout_gravity="center"
                                android:layout_marginTop="16dp"
                                android:text="@string/user_not_found"
                                android:textColor="@color/black80"
                                android:textSize="@dimen/_14ssp"
                                android:visibility="gone"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/flPhone"
                                tools:visibility="visible" />

                            <!--                            <ImageView-->
                            <!--                                android:id="@+id/pbLoadMember"-->
                            <!--                                android:layout_width="match_parent"-->
                            <!--                                android:layout_height="match_parent"-->
                            <!--                                android:adjustViewBounds="true"-->
                            <!--                                app:layout_constraintBottom_toBottomOf="parent"-->
                            <!--                                app:layout_constraintEnd_toEndOf="parent"-->
                            <!--                                app:layout_constraintStart_toStartOf="parent"-->
                            <!--                                app:layout_constraintTop_toBottomOf="@+id/flPhone" />-->
                            <ProgressBar
                                android:id="@+id/pbLoadMember"
                                android:layout_width="25dp"
                                android:layout_height="25dp"
                                android:layout_centerInParent="true"
                                android:layout_gravity="center"
                                android:layout_marginStart="10dp"
                                android:indeterminate="true"
                                android:indeterminateTint="@color/primaryColor"
                                android:indeterminateTintMode="src_atop"
                                android:progressTint="@color/mainWhite"
                                android:visibility="gone"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/flPhone"
                                tools:visibility="visible" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/llCustomizePay"
                        android:layout_width="135dp"
                        android:layout_height="wrap_content"
                        android:background="@drawable/button_login_background"
                        android:gravity="center"
                        android:minHeight="50dp"
                        android:paddingHorizontal="5dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvCustomizePay"
                                style="@style/FontLocalization"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:drawablePadding="0dp"
                                android:ellipsize="end"
                                android:gravity="center"
                                android:maxLines="1"
                                android:text="@string/pay_by_cash"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvOffline"
                                style="@style/FontLocalization"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:drawablePadding="0dp"
                                android:ellipsize="end"
                                android:gravity="center"
                                android:maxLines="1"
                                android:text="@string/offline_payments"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_12ssp" />
                        </LinearLayout>

                        <ImageView
                            android:id="@+id/ivArrow"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="3dp"
                            android:layout_marginEnd="5dp"
                            android:src="@drawable/icon_arrow_down_white" />


                    </LinearLayout>


                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/clCash"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/tvReceiveTitle"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/cash_collection"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            android:visibility="visible"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <FrameLayout
                            android:id="@+id/flUsdAmount"
                            android:layout_width="0dp"
                            android:layout_height="45dp"
                            android:layout_marginTop="4dp"
                            app:layout_constraintEnd_toStartOf="@id/flKhrAmount"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tvReceiveTitle">

                            <EditText
                                android:id="@+id/edtUsdAmount"
                                style="@style/FontLocalization"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@drawable/selector_editview_bg"
                                android:inputType="numberDecimal"
                                android:maxLength="15"
                                android:maxLines="1"
                                android:paddingStart="40dp"
                                android:singleLine="true"
                                android:textColor="@color/black"
                                android:textColorHint="@color/black40" />

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="16dp"
                                android:src="@drawable/icon_dollor_black" />

                        </FrameLayout>

                        <FrameLayout
                            android:id="@+id/flKhrAmount"
                            android:layout_width="0dp"
                            android:layout_height="45dp"
                            android:layout_marginStart="10dp"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/flUsdAmount"
                            app:layout_constraintTop_toTopOf="@id/flUsdAmount">

                            <EditText
                                android:id="@+id/edtKhrAmount"
                                style="@style/FontLocalization"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@drawable/selector_editview_bg"
                                android:inputType="number"
                                android:maxLength="15"
                                android:maxLines="1"
                                android:paddingStart="40dp"
                                android:singleLine="true"
                                android:textColor="@color/black"
                                android:textColorHint="@color/black40" />

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="16dp"
                                android:src="@drawable/icon_km_unit" />

                        </FrameLayout>

                        <TextView
                            android:id="@+id/tvConversionRatioTitle"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:text="@string/conversion_ratio"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/flUsdAmount" />

                        <TextView
                            android:id="@+id/tvConversionRatio"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="24dp"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            app:layout_constraintEnd_toEndOf="@id/flUsdAmount"
                            app:layout_constraintTop_toTopOf="@id/tvConversionRatioTitle"
                            tools:text="$1 = ៛4100" />

                        <TextView
                            android:id="@+id/tvChangeAmountTitle"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:layout_marginEnd="24dp"
                            android:text="@string/back_your_change_amount"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            app:layout_constraintStart_toStartOf="@id/tvConversionRatioTitle"
                            app:layout_constraintTop_toBottomOf="@id/tvConversionRatioTitle" />

                        <TextView
                            android:id="@+id/tvChangeAmount"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:text="$- -"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            app:layout_constraintEnd_toEndOf="@id/flUsdAmount"
                            app:layout_constraintTop_toTopOf="@id/tvChangeAmountTitle" />

                        <TextView
                            android:id="@+id/tvChangeAmountTip"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:paddingStart="24dp"
                            android:text="@string/insufficient_cash_received"
                            android:textColor="@color/main_red"
                            android:textSize="@dimen/_14ssp"
                            android:visibility="gone"
                            app:layout_constraintEnd_toEndOf="@id/flUsdAmount"
                            app:layout_constraintTop_toBottomOf="@id/tvChangeAmount"
                            tools:visibility="visible" />

                        <com.metathought.food_order.casheir.ui.widget.CustomerQuickInputView
                            android:id="@+id/viewCustomInput"
                            android:layout_width="0dp"
                            android:layout_height="250dp"
                            android:layout_marginTop="16dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="@id/flKhrAmount"
                            app:layout_constraintTop_toBottomOf="@id/flKhrAmount" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <LinearLayout
                        android:id="@+id/llOther"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:background="@drawable/background_efefef_radius_12"
                        android:gravity="center_vertical"
                        android:padding="10dp"
                        android:visibility="gone"
                        tools:visibility="gone">

                        <TextView
                            android:id="@+id/tvOtherPayment"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:maxWidth="200dp"
                            android:text="@string/amount"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_14ssp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tvOtherPaymentAmount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:gravity="end"
                            android:text="$ - -"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_20ssp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                    </LinearLayout>

                </LinearLayout>


            </LinearLayout>

        </ScrollView>

    </LinearLayout>


    <com.metathought.food_order.casheir.ui.widget.CustomNumberKeyBoardView
        android:id="@+id/viewKeyBoard"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@drawable/background_e7e7e7_right_radius_20dp" />


</LinearLayout>