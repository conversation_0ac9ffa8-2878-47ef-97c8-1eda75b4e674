<?xml version="1.0" encoding="utf-8"?>
<cn.bingoogolapple.badgeview.BGABadgeFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="6dp"
    android:paddingEnd="6dp"
    tools:layout_width="200dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardViewMain"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipToPadding="false"
        android:elevation="0dp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp"
        app:strokeColor="@color/black08"
        app:strokeWidth="1dp">

        <FrameLayout
            android:id="@+id/constraintLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"

                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/llNoImage"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/tvName"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="10dp"
                            android:ellipsize="end"
                            android:lines="3"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            android:visibility="visible"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="占位占位占位占位占位占位占位占位占位占位占位占位占位占位占位占位占位占位占位占位占位占位占位"
                            tools:visibility="visible" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="10dp"
                        android:gravity="top"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvPrice"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="start|bottom"
                            android:textColor="@color/primaryColorDark"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold"
                            tools:text="$0.00" />

                        <ImageView
                            android:id="@+id/ivEdit"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingTop="4dp"
                            android:paddingStart="10dp"
                            android:paddingEnd="13dp"
                            android:paddingBottom="10dp"
                            android:src="@drawable/icon_weight_edit" />

                    </LinearLayout>


                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!--无图售罄-->
            <TextView
                android:id="@+id/tvSoldOut"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/black40"
                android:gravity="center"
                android:text="@string/sold_out"
                android:textColor="@color/white"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/llContent"
                app:layout_constraintEnd_toEndOf="@id/llContent"
                app:layout_constraintStart_toStartOf="@id/llContent"
                app:layout_constraintTop_toTopOf="@id/llContent"
                tools:visibility="gone" />

        </FrameLayout>
    </androidx.cardview.widget.CardView>


</cn.bingoogolapple.badgeview.BGABadgeFrameLayout>
