<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:orientation="horizontal">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/background_white_left_radius_20"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical"
        android:paddingHorizontal="24dp"
        android:paddingVertical="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/rightLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="24dp"
            app:dialog_title="@string/discount_single_good" />

        <LinearLayout
            android:id="@+id/llContent"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="4dp"
            android:layout_weight="1"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <RadioGroup
                android:id="@+id/radioGroupType"
                android:layout_width="wrap_content"
                android:layout_height="45dp"
                android:layout_gravity="center"
                android:layout_marginBottom="24dp"
                android:background="@drawable/background_e5e5e5_radius_100"
                android:checkedButton="@id/radioAll"
                android:orientation="horizontal"
                android:visibility="visible">

                <RadioButton
                    android:id="@+id/radioModify"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:background="@drawable/radiobutton_discount_background"
                    android:button="@null"
                    android:checked="true"
                    android:gravity="center"
                    android:paddingHorizontal="25dp"
                    android:paddingVertical="10dp"
                    android:text="@string/modify_price"
                    android:textColor="@drawable/radio_discount_text_selector"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:buttonCompat="@null" />

                <RadioButton
                    android:id="@+id/radioFixedAmount"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:background="@drawable/radiobutton_discount_background"
                    android:button="@null"
                    android:gravity="center"
                    android:minHeight="0dp"
                    android:paddingHorizontal="25dp"
                    android:paddingVertical="10dp"
                    android:text="@string/discount2"
                    android:textColor="@drawable/radio_discount_text_selector"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:buttonCompat="@null" />

                <RadioButton
                    android:id="@+id/radioPercentage"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:background="@drawable/radiobutton_discount_background"
                    android:button="@null"
                    android:gravity="center"
                    android:minHeight="0dp"
                    android:paddingHorizontal="25dp"
                    android:paddingVertical="10dp"
                    android:text="@string/discount3"
                    android:textColor="@drawable/radio_discount_text_selector"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:buttonCompat="@null" />

            </RadioGroup>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/background_efefef_40_radius_20dp"
                android:orientation="vertical"
                android:paddingHorizontal="18dp"
                android:paddingVertical="10dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvName"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:maxLines="2"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold"
                        tools:text="山河招牌顶级蟹籽虾皇" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:gravity="center">

                        <ImageView
                            android:id="@+id/imgMinus"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_minus_disable"
                            android:visibility="visible" />

                        <TextView
                            android:id="@+id/tvCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:maxLength="4"
                            android:paddingHorizontal="10dp"
                            android:text="0"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_18ssp"
                            android:textStyle="bold"
                            tools:text="0" />

                        <ImageView
                            android:id="@+id/imgPlus"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_add" />
                    </LinearLayout>

                    <!--                <TextView-->
                    <!--                    android:id="@+id/tvCount"-->
                    <!--                    android:layout_width="wrap_content"-->
                    <!--                    android:layout_height="wrap_content"-->
                    <!--                    android:layout_marginEnd="18dp"-->
                    <!--                    android:textColor="@color/black60"-->
                    <!--                    android:textSize="@dimen/_12ssp"-->
                    <!--                    android:textStyle="bold"-->
                    <!--                    app:layout_constraintBottom_toBottomOf="@+id/tvName"-->
                    <!--                    app:layout_constraintEnd_toStartOf="@+id/ivIcon"-->
                    <!--                    app:layout_constraintTop_toTopOf="@+id/tvName"-->
                    <!--                    tools:text="x 1" />-->
                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/original_price"
                        android:textColor="@color/black80"
                        android:textSize="@dimen/_14ssp" />

                    <TextView
                        android:id="@+id/tvPrice"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:textColor="@color/black80"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold"
                        tools:text="$20" />

                    <TextView
                        android:id="@+id/tvVipPrice"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:drawablePadding="2dp"
                        android:textColor="@color/color_ff7f00"
                        android:textSize="@dimen/_12ssp"
                        android:textStyle="bold"
                        app:drawableStartCompat="@drawable/icon_vip"
                        tools:text="$20" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/new_price"
                        android:textColor="@color/black80"
                        android:textSize="@dimen/_14ssp" />

                    <TextView
                        android:id="@+id/tvNewPrice"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:textColor="@color/black80"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold"
                        tools:text="$20" />

                    <TextView
                        android:id="@+id/tvNewVipPrice"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:drawablePadding="2dp"
                        android:textColor="@color/color_ff7f00"
                        android:textSize="@dimen/_12ssp"
                        android:textStyle="bold"
                        app:drawableStartCompat="@drawable/icon_vip"
                        tools:text="$20" />

                </LinearLayout>
            </LinearLayout>

            <FrameLayout
                android:id="@+id/flPercent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:visibility="gone"
                tools:visibility="visible">


                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutPercent"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/discounts2"
                    android:orientation="horizontal"
                    android:textColorHint="@color/black60">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtPercent"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:cursorVisible="false"
                        android:drawablePadding="10dp"
                        android:gravity="center_vertical"
                        android:inputType="number"
                        android:maxLength="60"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="12dp"
                    android:text="%"
                    android:textColor="@color/black80"
                    android:textSize="@dimen/_16ssp"
                    android:textStyle="bold" />
            </FrameLayout>


            <LinearLayout
                android:id="@+id/llDeductInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:visibility="gone"
                tools:visibility="visible">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutUsd"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:hint="@string/sale_price_discount"
                    android:orientation="horizontal"
                    app:startIconDrawable="@drawable/icon_dollor_black"
                    app:startIconTint="@color/black">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtUsd"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:cursorVisible="false"
                        android:gravity="center_vertical"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingVertical="8dp"
                        android:paddingStart="35dp"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black40"
                        android:textSize="15sp" />

                </com.google.android.material.textfield.TextInputLayout>


                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutVipUsd"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="20dp"
                    android:layout_weight="1"
                    android:hint="@string/vip_price_discount"
                    app:startIconDrawable="@drawable/icon_dollor_black"
                    app:startIconTint="@color/black">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtVipUsd"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:cursorVisible="false"
                        android:gravity="center_vertical"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingVertical="8dp"
                        android:paddingStart="35dp"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black40"
                        android:textSize="15sp" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>


            <LinearLayout
                android:id="@+id/llModifyInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:visibility="gone"
                tools:visibility="visible">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutNewSalePrice"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:hint="@string/modify_sale_price"
                    android:orientation="horizontal"
                    app:startIconDrawable="@drawable/icon_dollor_black"
                    app:startIconTint="@color/black">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtNewSalePrice"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:cursorVisible="false"
                        android:gravity="center_vertical"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingVertical="8dp"
                        android:paddingStart="35dp"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black40"
                        android:textSize="15sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutNewVipPrice"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="20dp"
                    android:layout_weight="1"
                    android:hint="@string/modify_vip_price"
                    android:orientation="horizontal"
                    app:startIconDrawable="@drawable/icon_dollor_black"
                    app:startIconTint="@color/black">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtNewVipPrice"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:cursorVisible="false"
                        android:gravity="center_vertical"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingVertical="8dp"
                        android:paddingStart="35dp"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black40"
                        android:textSize="15sp" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llOriginalSinglePrice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/original_single_price"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_12ssp" />

                <TextView
                    android:id="@+id/tvOriginalSinglePrice"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_12ssp"
                    android:textStyle="bold"
                    tools:text="$20" />

                <TextView
                    android:id="@+id/tvOriginalSingleVipPrice"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:drawablePadding="2dp"
                    android:textColor="@color/color_ff7f00"
                    android:textSize="@dimen/_12ssp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    app:drawableStartCompat="@drawable/icon_vip"
                    tools:text="$20"
                    tools:visibility="visible" />
            </LinearLayout>


            <LinearLayout
                android:id="@+id/llReasonType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="visible"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:drawablePadding="2dp"
                    android:text="@string/reason_type_require"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16ssp"
                    android:textStyle="bold" />

                <RadioGroup
                    android:id="@+id/radioDiscountGroupType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="horizontal">

                    <RadioButton
                        android:id="@+id/radioDiscount"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="10dp"
                        android:background="@drawable/radiobutton_only_stroke_background"
                        android:button="@null"
                        android:checked="true"
                        android:drawableStart="@drawable/radiobutton_payment_icon"
                        android:drawablePadding="8dp"
                        android:gravity="center"
                        android:paddingHorizontal="10dp"
                        android:text="Discount"
                        android:textColor="@drawable/radio_payment_text_selector"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        app:buttonCompat="@null" />

                    <RadioButton
                        android:id="@+id/radioVoid"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="10dp"
                        android:background="@drawable/radiobutton_only_stroke_background"
                        android:button="@null"
                        android:checked="false"
                        android:drawableStart="@drawable/radiobutton_payment_icon"
                        android:drawablePadding="8dp"
                        android:gravity="center"
                        android:paddingHorizontal="10dp"
                        android:text="Void"
                        android:textColor="@drawable/radio_payment_text_selector"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        app:buttonCompat="@null" />

                </RadioGroup>
            </LinearLayout>


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutRemark"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="16dp"
                android:layout_weight="1"
                android:hint="@string/please_enter_the_reason"
                android:textColorHint="@color/black60"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtRemark"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawablePadding="10dp"
                    android:gravity="top|start"
                    android:inputType="textMultiLine"
                    android:maxLength="200"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />
            </com.google.android.material.textfield.TextInputLayout>

            <LinearLayout
                android:id="@+id/llBottom"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="24dp">

                <TextView
                    android:id="@+id/tvCustomize"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/button_outline_background_enable"
                    android:gravity="center"
                    android:minWidth="100dp"
                    android:paddingHorizontal="10dp"
                    android:text="@string/customize"
                    android:textColor="@color/primaryColor"
                    android:textSize="@dimen/_20ssp" />

                <TextView
                    android:id="@+id/tvConfirm"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/button_login_background"
                    android:gravity="center"
                    android:text="@string/confirm2"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_20ssp" />

            </LinearLayout>
        </LinearLayout>

    </LinearLayout>

    <!--    <com.metathought.food_order.casheir.ui.widget.CustomNumberKeyBoardView-->
    <!--        android:id="@+id/viewKeyBoard"-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="match_parent"-->
    <!--        android:layout_weight="1"-->
    <!--        android:background="@drawable/background_e7e7e7_right_radius_20dp"-->
    <!--        android:padding="2dp" />-->

    <FrameLayout
        android:id="@+id/rightLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/layoutMain"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/viewChooseList"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/background_e7e7e7_right_radius_20dp"
            android:paddingHorizontal="10dp"
            android:paddingTop="16dp">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/choose_discount_require"
                android:textSize="@dimen/_16ssp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvList"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="10dp"
                android:visibility="gone"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTitle"
                tools:itemCount="3"
                tools:listitem="@layout/item_preconfiguration_discount_list"
                tools:visibility="visible" />

            <include
                android:id="@+id/layoutEmpty"
                layout="@layout/layout_empty_data"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.metathought.food_order.casheir.ui.widget.CustomNumberKeyBoardView
            android:id="@+id/viewKeyBoard"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/background_e7e7e7_right_radius_20dp"
            android:padding="2dp"
            android:visibility="gone" />

    </FrameLayout>


    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_gravity="center"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/layoutMain"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/layoutMain"
        tools:visibility="visible" />


</androidx.constraintlayout.widget.ConstraintLayout>
