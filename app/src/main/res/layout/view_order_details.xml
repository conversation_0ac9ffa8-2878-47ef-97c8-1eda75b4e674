<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <LinearLayout
        android:id="@+id/layoutDetailTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:paddingBottom="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:id="@+id/btnBack"
            android:layout_width="40dp"
            android:layout_height="30dp"
            android:padding="5dp"
            android:src="@drawable/ic_back"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/tvOrderNoTitle"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/btnCloseDetail"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:padding="5dp"
            android:src="@drawable/ic_cross_closed"
            tools:ignore="ContentDescription" />
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutMenu"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@drawable/background_round_fragment"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layoutDetailTitle"
        app:layout_constraintVertical_bias="0.0"
        app:layout_constraintWidth_percent="0.5">

        <TextView
            android:id="@+id/tvTableID"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:text=""
            android:textColor="@color/black"
            android:textSize="22sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/cardStatus"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@tools:sample/lorem/random" />

        <androidx.cardview.widget.CardView
            android:id="@+id/cardStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:elevation="0dp"
            app:cardBackgroundColor="@color/confirm_backgroud_color"
            app:cardCornerRadius="5dp"
            app:cardElevation="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintStart_toEndOf="@id/tvTableID"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/imgDelete"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:paddingHorizontal="10dp"
                android:paddingVertical="5dp"
                android:text=""
                android:textColor="@color/confirm_text_color"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold" />
        </androidx.cardview.widget.CardView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/background_dialog"
            android:orientation="vertical"
            android:padding="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTableID">

            <!--                <LinearLayout-->
            <!--                    android:layout_width="match_parent"-->
            <!--                    android:layout_height="wrap_content"-->
            <!--                    android:layout_marginTop="10dp"-->
            <!--                    android:orientation="horizontal">-->

            <!--                    <TextView-->
            <!--                        android:layout_width="0dp"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:layout_gravity="end"-->
            <!--                        android:layout_marginTop="10dp"-->
            <!--                        android:layout_marginEnd="10dp"-->
            <!--                        android:layout_weight="3"-->
            <!--                        android:text="@string/items"-->
            <!--                        android:textColor="@color/black50"-->
            <!--                        android:textSize="14sp" />-->

            <!--                    <TextView-->
            <!--                        android:layout_width="0dp"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:layout_gravity="end"-->
            <!--                        android:layout_marginTop="10dp"-->
            <!--                        android:layout_marginEnd="10dp"-->
            <!--                        android:layout_weight="1"-->
            <!--                        android:gravity="center_horizontal"-->
            <!--                        android:text="@string/quantity"-->
            <!--                        android:textColor="@color/black50"-->
            <!--                        android:textSize="14sp" />-->

            <!--                    <TextView-->
            <!--                        android:layout_width="0dp"-->
            <!--                        android:layout_height="wrap_content"-->
            <!--                        android:layout_gravity="end"-->
            <!--                        android:layout_marginTop="10dp"-->
            <!--                        android:layout_weight="2"-->
            <!--                        android:gravity="center_horizontal"-->
            <!--                        android:text="@string/amount"-->
            <!--                        android:textColor="@color/black50"-->
            <!--                        android:textSize="14sp" />-->
            <!--                </LinearLayout>-->

            <!--                <View-->
            <!--                    android:layout_width="match_parent"-->
            <!--                    android:layout_height="@dimen/height_bar"-->
            <!--                    android:background="@color/black08" />-->

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/orderedInfoRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="5"
                tools:listitem="@layout/pending_order_menu_item" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutResumeOrder"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@drawable/background_round_fragment"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layoutDetailTitle"
        app:layout_constraintWidth_percent="0.49">

        <LinearLayout
            android:id="@+id/layoutDetailInfo"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@drawable/background_dialog"
                android:orientation="vertical"
                android:padding="10dp">

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:scrollbars="none">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvCustomerTitle"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/customer_info"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            tools:visibility="visible" />

                        <LinearLayout
                            android:id="@+id/llPeople"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="13dp">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/print_title_number_of_pax"
                                android:textColor="@color/order_info_detail_color"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvCustomerNum"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="5" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llCustomerName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/customer_name"
                                android:textColor="@color/order_info_detail_color"
                                android:textSize="@dimen/_14ssp" />

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1">

                                <TextView
                                    android:id="@+id/tvCustomerName"
                                    style="@style/FontLocalization"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/black"
                                    android:textSize="@dimen/_14ssp"
                                    android:textStyle="bold"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintTop_toTopOf="parent"
                                    tools:text="林超正" />
                            </androidx.constraintlayout.widget.ConstraintLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llCustomerPhone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/phone_number"
                                android:textColor="@color/order_info_detail_color"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvCustomerPhone"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="15006099788" />
                        </LinearLayout>


                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:text="@string/order_info"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:id="@+id/llTakeOutPlatform"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/take_out_platform"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvTakeOutPlatform"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="20241234567890" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llTakeOutId"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/take_out_order_id"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvTakeOutId"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="20241234567890" />

                            <!--                                <ImageView-->
                            <!--                                    android:id="@+id/ivEditTakeOutId"-->
                            <!--                                    android:layout_width="wrap_content"-->
                            <!--                                    android:layout_height="match_parent"-->
                            <!--                                    android:paddingStart="10dp"-->
                            <!--                                    android:src="@drawable/icon_edit"-->
                            <!--                                    android:visibility="gone"-->
                            <!--                                    tools:visibility="visible" />-->
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llPickUpNo"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/print_title_pick_up_no"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvPickUpNo"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="20241234567890" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/order_id"
                                android:textColor="@color/order_info_detail_color"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvOrderNo"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="20241234567890" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@id/llInvoiceNumber"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/print_title_invoiceNumber"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvInvoiceNumber"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="20241234567890" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llCashier"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/cashier"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/print_title_Cashier"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvCashier"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="U-Pay" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/ordered_time"
                                android:textColor="@color/order_info_detail_color"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvOrderTime"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="2024/04/02 12:00:00" />
                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/order_type"
                                android:textColor="@color/order_info_detail_color"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvOrderType"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="Dine In" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/order_by"
                                android:textColor="@color/order_info_detail_color"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvOrderBy"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="Kiosk" />
                        </LinearLayout>

                        <!--                            <LinearLayout-->
                        <!--                                android:id="@+id/llPaymentMethod"-->
                        <!--                                android:layout_width="match_parent"-->
                        <!--                                android:layout_height="wrap_content"-->
                        <!--                                android:layout_marginTop="8dp"-->
                        <!--                                android:visibility="gone">-->

                        <!--                                <TextView-->
                        <!--                                    style="@style/FontLocalization"-->
                        <!--                                    android:layout_width="wrap_content"-->
                        <!--                                    android:layout_height="wrap_content"-->
                        <!--                                    android:layout_marginEnd="15dp"-->
                        <!--                                    android:text="@string/payment_method"-->
                        <!--                                    android:textColor="@color/order_info_detail_color"-->
                        <!--                                    android:textSize="@dimen/_14ssp" />-->

                        <!--                                <TextView-->
                        <!--                                    android:id="@+id/tvPaymentMethod"-->
                        <!--                                    style="@style/FontLocalization"-->
                        <!--                                    android:layout_width="0dp"-->
                        <!--                                    android:layout_height="wrap_content"-->
                        <!--                                    android:layout_weight="1"-->
                        <!--                                    android:gravity="end"-->
                        <!--                                    android:maxLines="2"-->
                        <!--                                    android:textColor="@color/black"-->
                        <!--                                    android:textSize="@dimen/_14ssp"-->
                        <!--                                    android:textStyle="bold"-->
                        <!--                                    tools:text="U-Pay" />-->
                        <!--                            </LinearLayout>-->

                        <LinearLayout
                            android:id="@+id/llPaymentMethod1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/paymentMethod1"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/payment_method"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvPaymentMethod1"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="U-Pay" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llPaymentMethodPayAmount1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/paymentMethodPayAmount1"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/pay_amount"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvPaymentMethodPayAmount1"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="U-Pay" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llPaymentMethod2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/paymentMethod2"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/payment_method"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvPaymentMethod2"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="U-Pay" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llPaymentMethodPayAmount2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/paymentMethodPayAmount2"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/pay_amount"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvPaymentMethodPayAmount2"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="U-Pay" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llChange"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:id="@+id/change"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/back_your_change_amount"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvChange"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="U-Pay" />
                        </LinearLayout>


                        <LinearLayout
                            android:id="@+id/llDiningTime"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/ordered_dining_time"
                                android:textColor="@color/order_info_detail_color"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvDiningTime"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="2024/04/02 12:00:00" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llCancelTime"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:visibility="gone">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/cancel_time"
                                android:textColor="@color/order_info_detail_color"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvCancelTime"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="2024/04/02 12:00:00" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llCancelReason"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:visibility="gone">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/cancel_reason"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <com.metathought.food_order.casheir.ui.widget.ExpandTextView
                                android:id="@+id/tvCancelReason"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                android:visibility="visible" />
                        </LinearLayout>


                        <LinearLayout
                            android:id="@+id/llPaymentTime"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:visibility="gone">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/payment_time"
                                android:textColor="@color/order_info_detail_color"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvPaymentTime"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="2024/04/02 12:00:00" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llRefundTime"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/refund_time"
                                android:textColor="@color/order_info_detail_color"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvRefundTime"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="2024/04/02 12:00:00" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llRefundPayType"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/refund_pay_type"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvRefundPayType"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:lineSpacingExtra="8dp"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="2024/04/02 12:00:00" />
                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/remark"
                                android:textColor="@color/order_info_detail_color"
                                android:textSize="@dimen/_14ssp" />

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1">

                                <TextView
                                    android:id="@+id/tvOrderRemark"
                                    style="@style/FontLocalization"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/black"
                                    android:textSize="@dimen/_14ssp"
                                    android:textStyle="bold"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintTop_toTopOf="parent"
                                    tools:text="多加点醋，不吃辣，葱多放点可以吗？动换行" />
                            </androidx.constraintlayout.widget.ConstraintLayout>

                        </LinearLayout>


                        <LinearLayout
                            android:id="@+id/llAntiSettlementReason"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/anti_settlement_reason"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1">

                                <TextView
                                    android:id="@+id/tvOrderAntiSettlement"
                                    style="@style/FontLocalization"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="end"
                                    android:textColor="@color/black"
                                    android:textSize="@dimen/_14ssp"
                                    android:textStyle="bold"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintTop_toTopOf="parent"
                                    tools:text="多加点醋，不吃辣，葱多放点可以吗？动换行" />
                            </androidx.constraintlayout.widget.ConstraintLayout>

                        </LinearLayout>


                        <LinearLayout
                            android:id="@+id/llUserAccount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/customer_account"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvUserAccount"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:lineSpacingExtra="10dp"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="2024/04/02 12:00:00" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llUserName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/customer_nickname"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1">

                                <TextView
                                    android:id="@+id/tvUserName"
                                    style="@style/FontLocalization"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/black"
                                    android:textSize="@dimen/_14ssp"
                                    android:textStyle="bold"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintTop_toTopOf="parent"
                                    tools:text="2024/04/02 12:00:00" />
                            </androidx.constraintlayout.widget.ConstraintLayout>
                        </LinearLayout>


                        <LinearLayout
                            android:id="@+id/llCreditReason"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/credit_reason"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1">

                                <TextView
                                    android:id="@+id/tvCreditReason"
                                    style="@style/FontLocalization"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/black"
                                    android:textSize="@dimen/_14ssp"
                                    android:textStyle="bold"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintTop_toTopOf="parent"
                                    tools:text="2024/04/02 12:00:00" />
                            </androidx.constraintlayout.widget.ConstraintLayout>
                        </LinearLayout>


                        <LinearLayout
                            android:id="@+id/llCancelCreditReason"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/cancel_credit"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1">

                                <TextView
                                    android:id="@+id/tvCancelCreditReason"
                                    style="@style/FontLocalization"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/black"
                                    android:textSize="@dimen/_14ssp"
                                    android:textStyle="bold"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintTop_toTopOf="parent"
                                    tools:text="2024/04/02 12:00:00" />
                            </androidx.constraintlayout.widget.ConstraintLayout>
                        </LinearLayout>


                        <LinearLayout
                            android:id="@+id/llRepaymentTime"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="15dp"
                                android:text="@string/repayment_time"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_14ssp" />

                            <TextView
                                android:id="@+id/tvRepaymentTime"
                                style="@style/FontLocalization"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:lineSpacingExtra="10dp"
                                android:maxLines="2"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_14ssp"
                                android:textStyle="bold"
                                tools:text="2024/04/02 12:00:00" />
                        </LinearLayout>
                    </LinearLayout>

                </ScrollView>

                <View style="@style/commonDividerStyle" />

                <LinearLayout
                    android:id="@+id/layoutTotal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/subtotal"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <TextView
                            android:id="@+id/tvSubtotal"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llDiscountActivity"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tvDiscountActivityTitle"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="4dp"
                            android:gravity="center_vertical"
                            android:text="@string/discount_activity"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <ImageView
                            android:id="@+id/btnDiscountActivityPriceCue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="15dp"
                            android:src="@drawable/icon_circle_warn"
                            android:visibility="gone"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tvDiscountActivityAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llPackPrice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="4dp"
                            android:gravity="center_vertical"
                            android:text="@string/packing_price"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <ImageView
                            android:id="@+id/btnPackPriceCue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="15dp"
                            android:src="@drawable/icon_circle_warn" />

                        <TextView
                            android:id="@+id/tvPackingAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llServiceFee"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:gravity="top"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="4dp"
                            android:gravity="center_vertical"
                            android:text="@string/service_fee"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <ImageView
                            android:id="@+id/btnServiceFeeCue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="15dp"
                            android:src="@drawable/icon_circle_warn" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvServiceFee"
                                style="@style/FontLocalization"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="$99.99" />
                            <!--                                <TextView-->
                            <!--                                    android:id="@+id/tvVipServiceFee"-->
                            <!--                                    style="@style/FontLocalization"-->
                            <!--                                    android:layout_width="wrap_content"-->
                            <!--                                    android:layout_height="wrap_content"-->
                            <!--                                    android:layout_marginTop="4dp"-->
                            <!--                                    android:drawablePadding="2dp"-->
                            <!--                                    android:textColor="@color/member_price_color"-->
                            <!--                                    android:textSize="@dimen/_12ssp"-->
                            <!--                                    android:textStyle="bold"-->
                            <!--                                    android:visibility="gone"-->
                            <!--                                    app:drawableStartCompat="@drawable/icon_vip"-->
                            <!--                                    tools:text="$0.00"-->
                            <!--                                    tools:visibility="visible" />-->
                        </LinearLayout>

                    </LinearLayout>


                    <LinearLayout
                        android:id="@+id/llCoupon"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:gravity="center_vertical">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/coupon"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <LinearLayout
                            android:id="@+id/llCouponContent"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end">

                            <TextView
                                android:id="@+id/tvViewCouponGiftGood"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:gravity="end"
                                android:maxLines="1"
                                android:text="@string/view_give_away_goods"
                                android:textColor="@color/primaryColor"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                android:visibility="gone"
                                tools:visibility="visible" />

                            <TextView
                                android:id="@+id/tvCoupon"
                                style="@style/FontLocalization"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                android:visibility="visible"
                                tools:text="-$99.99"
                                tools:visibility="visible" />

                            <!--                                <ImageView-->
                            <!--                                    android:id="@+id/iconCouponArrow"-->
                            <!--                                    android:layout_width="wrap_content"-->
                            <!--                                    android:layout_height="match_parent"-->
                            <!--                                    android:paddingHorizontal="5dp"-->
                            <!--                                    android:layout_marginStart="5dp"-->
                            <!--                                    android:src="@drawable/icon_coupon_arrow_right" />-->
                        </LinearLayout>


                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llDiscount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tvDiscountTitle"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="4dp"
                            android:gravity="center_vertical"
                            android:text="@string/discounts"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <ImageView
                            android:id="@+id/btnDiscountCue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="15dp"
                            android:src="@drawable/icon_circle_warn" />

                        <TextView
                            android:id="@+id/tvDiscount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:drawablePadding="4dp"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llDiscountAmount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/tvDiscountAmountTitle"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="4dp"
                            android:gravity="center_vertical"
                            android:text="@string/amount_of_reduction_usd"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <ImageView
                            android:id="@+id/btnDiscountAmountCue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="15dp"
                            android:src="@drawable/icon_circle_warn" />

                        <TextView
                            android:id="@+id/tvDiscountAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:drawablePadding="4dp"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llVat"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/titleVat"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/vat"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <TextView
                            android:id="@+id/tvVat"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llCommission"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="4dp"
                            android:gravity="center_vertical"
                            android:text="@string/commission"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />


                        <TextView
                            android:id="@+id/tvCommissionPrice"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="$99.99" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llTotalPrice2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="top"
                            android:text="@string/total"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvTotalPrice2"
                                style="@style/FontLocalization"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="$99.99" />

                            <TextView
                                android:id="@+id/tvTotalKhrPrice2"
                                style="@style/FontLocalization"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_12ssp"
                                android:textStyle="bold"
                                tools:text="៛0" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llPartialRefundAmount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/partial_refund_amount"
                            android:textColor="@color/order_info_detail_color"
                            android:textSize="@dimen/_12ssp" />

                        <TextView
                            android:id="@+id/tvPartialRefundAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/main_red"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="-$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llPartialRefundPackFee"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/refund_pack_fee"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <TextView
                            android:id="@+id/tvPartialRefundPackFee"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/main_red"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="-$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llPartialRefundServiceFee"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/refund_service_fee"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp" />

                        <TextView
                            android:id="@+id/tvPartialRefundServiceFee"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/main_red"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="-$99.99" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llVatRefundAmount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="15dp"
                            android:gravity="center_vertical"
                            android:text="@string/vat_refund"
                            android:textColor="@color/order_info_detail_color"
                            android:textSize="@dimen/_12ssp" />

                        <TextView
                            android:id="@+id/tvVatRefundAmount"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/main_red"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            tools:text="-$99.99" />
                    </LinearLayout>

                    <!--                    <LinearLayout-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_marginTop="5dp"-->
                    <!--                        android:gravity="center_vertical">-->


                    <!--                    <LinearLayout-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:layout_marginTop="5dp"-->
                    <!--                        android:gravity="center_vertical">-->

                    <!--                        <TextView-->
                    <!--                            style="@style/FontLocalization"-->
                    <!--                            android:layout_width="wrap_content"-->
                    <!--                            android:layout_height="match_parent"-->
                    <!--                            android:layout_marginEnd="15dp"-->
                    <!--                            android:gravity="center_vertical"-->
                    <!--                            android:text="@string/discounted"-->
                    <!--                            android:textColor="@color/black80"-->
                    <!--                            android:textSize="@dimen/_12ssp" />-->

                    <!--                        <TextView-->
                    <!--                            style="@style/FontLocalization"-->
                    <!--                            android:layout_width="0dp"-->
                    <!--                            android:layout_height="wrap_content"-->
                    <!--                            android:layout_weight="1"-->
                    <!--                            android:gravity="end"-->
                    <!--                            android:maxLines="1"-->
                    <!--                            android:textColor="@color/black80"-->
                    <!--                            android:textSize="@dimen/_12ssp"-->
                    <!--                            android:textStyle="bold"-->
                    <!--                            tools:text="-$99.99" />-->
                    <!--                    </LinearLayout>-->


                </LinearLayout>


                <LinearLayout
                    android:id="@+id/llTotalPrice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="top">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="4dp"
                        android:gravity="top"
                        android:text="@string/total_price"
                        android:textColor="@color/black80"
                        android:textSize="@dimen/_16ssp" />

                    <ImageView
                        android:id="@+id/btnTotalPriceDetail"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="3dp"
                        android:layout_marginEnd="15dp"
                        android:src="@drawable/icon_circle_warn" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tvTotalPrice"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            tools:text="$99.99" />

                        <TextView
                            android:id="@+id/tvTotalKhrPrice"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:maxLines="1"
                            android:textColor="@color/black"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            tools:text="៛0" />

                        <TextView
                            android:id="@+id/tvVipPrice"
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:drawablePadding="2dp"
                            android:textColor="@color/member_price_color"
                            android:textSize="@dimen/_12ssp"
                            android:textStyle="bold"
                            android:visibility="gone"
                            app:drawableStartCompat="@drawable/icon_vip"
                            tools:text="$0.00"
                            tools:visibility="visible" />

                        <!--                            <TextView-->
                        <!--                                android:id="@+id/tvOriginalPrice"-->
                        <!--                                style="@style/FontLocalization"-->
                        <!--                                android:layout_width="wrap_content"-->
                        <!--                                android:layout_height="wrap_content"-->
                        <!--                                android:layout_marginTop="4dp"-->
                        <!--                                android:drawablePadding="2dp"-->
                        <!--                                android:foreground="@drawable/strike_price"-->
                        <!--                                android:textColor="@color/black60"-->
                        <!--                                android:textSize="@dimen/_12ssp"-->
                        <!--                                android:textStyle="bold"-->
                        <!--                                android:visibility="gone"-->
                        <!--                                tools:text="$0.00"-->
                        <!--                                tools:visibility="visible" />-->


                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llActualReceiveAmount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center_vertical"
                    android:visibility="gone">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="15dp"
                        android:gravity="center_vertical"
                        android:text="@string/actual_received_amount"
                        android:textColor="@color/black80"
                        android:textSize="@dimen/_16ssp" />

                    <TextView
                        android:id="@+id/tvActualReceiveAmount"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:maxLines="1"
                        android:textColor="@color/black"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        tools:text="$99.99" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llRefundAmount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center_vertical"
                    android:visibility="gone">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="15dp"
                        android:gravity="center_vertical"
                        android:text="@string/refund_amount"
                        android:textColor="@color/black80"
                        android:textSize="@dimen/_16ssp" />

                    <TextView
                        android:id="@+id/tvRefundAmount"
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:maxLines="1"
                        android:textColor="@color/black"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        tools:text="$99.99" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <include
            android:id="@+id/layoutEmptyDetail"
            layout="@layout/layout_empty_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>