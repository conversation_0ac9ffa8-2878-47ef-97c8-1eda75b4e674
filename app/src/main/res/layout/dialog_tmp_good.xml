<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="32dp"
            android:paddingVertical="24dp"
            app:dialog_title="@string/temporary_goods" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutOrder"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.5"
                android:background="@drawable/background_e7e7e7_top_right_radius_10dp"
                android:paddingHorizontal="10dp"
                android:paddingTop="10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/topBar"
                app:layout_constraintWidth_percent="0.5">


                <LinearLayout
                    android:id="@+id/layoutMainOrdered"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/background_round_top_white"
                    android:orientation="vertical"
                    android:paddingHorizontal="10dp"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/layoutAction"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:id="@+id/layoutHeader"
                        android:layout_width="match_parent"
                        android:layout_height="47dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingHorizontal="10dp">

                        <TextView
                            android:id="@+id/tvDeleteAll"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="end"
                            android:layout_marginEnd="2dp"
                            android:layout_weight="1.8"
                            android:drawablePadding="3dp"
                            android:gravity="center_vertical"
                            android:text="@string/items"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="end"
                            android:layout_marginEnd="2dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="@string/quantity"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="end"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="@string/amount"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_14ssp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <View
                        android:id="@+id/vTopLine"
                        style="@style/commonDividerStyle" />

                    <FrameLayout
                        android:id="@+id/flOrderedFood"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recyclerOrderedFood"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:clipToPadding="false"
                                android:overScrollMode="never"
                                android:paddingVertical="10dp"
                                android:scrollbars="none"
                                android:visibility="visible"
                                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                                tools:itemCount="2"
                                tools:listitem="@layout/selected_menu_item" />

                        </LinearLayout>

                        <include
                            android:id="@+id/layoutEmpty"
                            layout="@layout/layout_empty_data"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </FrameLayout>


                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layoutAction"
                    android:layout_width="100dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="10dp"
                    android:layout_marginBottom="10dp"
                    android:orientation="vertical"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/layoutMainOrdered"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/background_white_radius_12dp"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/btnReduce"
                            android:layout_width="match_parent"
                            android:layout_height="60dp"
                            android:scaleType="centerInside"
                            android:src="@drawable/selector_good_reduce" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/black12" />

                        <TextView
                            android:id="@+id/tvGoodNum"
                            style="@style/FontLocalization"
                            android:layout_width="match_parent"
                            android:layout_height="60dp"
                            android:background="@null"
                            android:gravity="center"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_24ssp"
                            android:textStyle="bold"
                            tools:text="999" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/black12" />

                        <ImageButton
                            android:id="@+id/btnAdd"
                            android:layout_width="match_parent"
                            android:layout_height="60dp"
                            android:background="@color/transparent"
                            android:scaleType="centerInside"
                            android:src="@drawable/selector_good_add" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1" />

                </LinearLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutMenu"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="6dp"
                android:layout_weight="0.5"
                android:background="@drawable/background_e7e7e7_top_left_radius_10dp"
                android:paddingHorizontal="10dp"
                android:paddingTop="12dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/layoutOrder"
                app:layout_constraintTop_toBottomOf="@id/topBar">


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewMenu"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:clipToPadding="true"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0"
                    app:spanCount="3"
                    tools:listitem="@layout/new_menu_item" />

                <include
                    android:id="@+id/layoutEmptyFood"
                    layout="@layout/layout_empty_data"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="50dp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible" />

            </androidx.constraintlayout.widget.ConstraintLayout>


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingHorizontal="32dp"
            android:paddingTop="25dp"
            android:paddingBottom="32dp">

            <TextView
                android:id="@+id/btnAddTmpGood"
                style="@style/CustomOutlinedBox"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:background="@drawable/background_white_border_primary_radius_12"
                android:drawablePadding="10dp"
                android:gravity="center"
                android:paddingHorizontal="19dp"
                android:text="@string/add_new_tmp_good"
                android:textAllCaps="false"
                android:textColor="@color/primaryColor"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold"
                app:drawableStartCompat="@drawable/ic_good_add" />

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/btnYes"
                style="@style/CustomOutlinedBox"
                android:layout_width="370dp"
                android:layout_height="50dp"
                android:alpha="0.5"
                android:background="@drawable/background_primary_color_radius_12dp"
                android:drawablePadding="10dp"
                android:enabled="false"
                android:gravity="center"
                android:paddingHorizontal="19dp"
                android:text="@string/confirm"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold" />

        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
