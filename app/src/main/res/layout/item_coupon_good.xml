<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="6dp"
    android:background="@android:color/transparent"
    tools:ignore="MissingDefaultResource">

    <androidx.cardview.widget.CardView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:cardElevation="0dp"
        app:cardCornerRadius="10dp">

        <ImageView
            android:id="@+id/ivGood"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:scaleType="centerCrop" />

    </androidx.cardview.widget.CardView>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvGoodName"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:textSize="@dimen/_12ssp"
            tools:text="商品名称" />

        <TextView
            android:id="@+id/tvPrice"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black60"
            android:textSize="@dimen/_12ssp"
            tools:text="￥99" />

    </LinearLayout>

    <TextView
        android:id="@+id/tvNum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="x1" />

</LinearLayout>