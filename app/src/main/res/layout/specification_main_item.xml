<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="10dp"
    android:background="@android:color/transparent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvTitle"
        style="@style/FontLocalization"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black"
        android:textSize="@dimen/_14ssp"
        android:textStyle="bold"
        tools:text="Title" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerSpecificationItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:overScrollMode="never"
        android:scrollbars="none"
        tools:itemCount="2"
        tools:listitem="@layout/specification_item" />
</LinearLayout>
