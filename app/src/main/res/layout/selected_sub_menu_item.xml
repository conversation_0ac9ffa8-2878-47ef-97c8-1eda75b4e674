<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingBottom="29dp">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_weight="2"
        android:orientation="vertical"
        android:text="@string/items"
        android:textColor="@color/black50"
        android:textSize="14sp">

        <TextView
            android:id="@+id/tvName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_weight="1.2"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/black"
            android:textSize="24sp"
            tools:text="Lemonade" />

        <TextView
            android:id="@+id/tvSpecification"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginTop="6dp"
            android:layout_weight="1.2"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/black60"
            android:textSize="@dimen/_16ssp"
            tools:text="Large Normal ice, Normal sugar" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:gravity="start"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_weight="1.2"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/primaryColor"
                android:textSize="20sp"
                android:textStyle="bold"
                tools:text="$9.9" />

            <TextView
                android:id="@+id/tvVipPrice"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:drawablePadding="2dp"
                android:textColor="@color/member_price_color"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                android:visibility="gone"
                app:drawableStartCompat="@drawable/icon_vip"
                tools:text="$0.00"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvOriginalPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:ellipsize="end"
                android:foreground="@drawable/strike_price"
                android:maxLines="3"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp"
                android:visibility="gone"
                tools:text="12.99"
                tools:visibility="visible" />
            <!--            <TextView-->
            <!--                android:id="@+id/tvWeight"-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_gravity="center_horizontal"-->
            <!--                android:layout_weight="1.2"-->
            <!--                android:ellipsize="end"-->
            <!--                android:maxLines="1"-->
            <!--                android:textColor="@color/primaryColor"-->
            <!--                android:textSize="20sp"-->
            <!--                android:textStyle="bold"-->
            <!--                tools:text="(9.9)" />-->
        </LinearLayout>


    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:layout_marginStart="40dp"
        android:gravity="center">

        <ImageView
            android:id="@+id/imgMinus"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_minus" />

        <TextView
            android:id="@+id/tvQTY"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="4dp"
            android:maxLength="4"
            android:paddingHorizontal="10dp"
            android:textColor="@color/primaryColor"
            android:textSize="20sp"
            android:textStyle="bold"
            tools:text="10" />

        <ImageView
            android:id="@+id/imgPlus"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_add" />
    </LinearLayout>
</LinearLayout>
