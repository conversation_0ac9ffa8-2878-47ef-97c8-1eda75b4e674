<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/background_popup_dropdown"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:padding="10dp">

    <TextView
        android:id="@+id/tvTicket"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center"
        android:text="@string/ticket"
        android:textColor="@color/black" />

    <TextView
        android:id="@+id/tvLabel"
        style="@style/FontLocalization"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center"
        android:text="@string/label"
        android:textColor="@color/black" />

</LinearLayout>