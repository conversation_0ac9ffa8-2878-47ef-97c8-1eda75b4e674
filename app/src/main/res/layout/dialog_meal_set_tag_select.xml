<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
>

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="32dp"
            android:paddingVertical="24dp"
            app:dialog_title="@string/item_detail" />

        <TextView
            android:id="@+id/tvGoodName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="3"
            android:paddingHorizontal="32dp"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:textStyle="bold"
            tools:text="四季奶青" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="24dp"
            android:layout_weight="1"
            android:overScrollMode="never"
            android:layout_marginHorizontal="32dp"
            android:scrollbars="none"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        <View
            style="@style/commonDividerStyle"
            android:layout_width="match_parent"
            android:layout_height="1dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginStart="24dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginBottom="32dp">

            <LinearLayout
                android:id="@+id/llNumControl"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:layout_gravity="center_vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/imgMinus"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_minus"
                    android:visibility="invisible" />

                <TextView
                    android:id="@+id/tvQTY"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:maxLength="4"
                    android:paddingHorizontal="10dp"
                    android:text="1"
                    android:textColor="@color/black"
                    android:textSize="22sp" />

                <ImageView
                    android:id="@id/imgPlus"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_add" />
            </LinearLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnConfirm"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:layout_gravity="center_vertical"
                android:text="@string/add"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="visible"
                app:cornerRadius="12dp" />
        </LinearLayout>

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>