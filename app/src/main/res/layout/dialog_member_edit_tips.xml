<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="24dp"
        android:paddingVertical="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="25dp"
            android:gravity="center_vertical"
            tools:ignore="UseCompoundDrawables">

            <TextView
                android:id="@+id/tvTableID"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/edit_member"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingHorizontal="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="4dp"
            android:layout_marginTop="16dp">

            <TextView
                android:id="@+id/tvLableName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/customer_nickname"
                android:textColor="@color/black"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvLablePhone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="@string/phone_number"
                android:textColor="@color/black"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvName" />

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/barrier"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:barrierDirection="end"
                app:constraint_referenced_ids="tvLableName,tvLablePhone" />

            <TextView
                android:id="@+id/tvName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:drawablePadding="5dp"
                android:textColor="@color/black"
                app:drawableEndCompat="@drawable/ic_edit_member"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvLableName"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="@+id/barrier"
                app:layout_constraintWidth_default="wrap"
                tools:text="Jeff" />

            <!--            app:drawableEndCompat="@drawable/ic_edit_member"-->
            <TextView
                android:id="@+id/tvPhone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:drawablePadding="5dp"
                android:textColor="@color/black"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvLablePhone"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="@+id/barrier"
                tools:text="855 12345678" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
