<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:dialog_title="@string/please_select"
            android:layout_marginBottom="24dp"
            />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewChannels"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/item_popup_offline_channel" />

            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_gravity="center"
                android:visibility="gone" />
        </FrameLayout>

<!--        <LinearLayout-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:orientation="horizontal">-->

<!--            <com.google.android.material.button.MaterialButton-->
<!--                android:id="@+id/btnNo"-->
<!--                style="@style/CustomOutlinedBox"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="60dp"-->
<!--                android:layout_marginTop="30dp"-->
<!--                android:layout_marginEnd="10dp"-->
<!--                android:layout_weight="1"-->
<!--                android:clickable="true"-->
<!--                android:focusable="true"-->
<!--                android:gravity="center"-->
<!--                android:orientation="horizontal"-->
<!--                android:text="@string/cancel"-->
<!--                android:textColor="@color/black"-->
<!--                android:textSize="@dimen/_18ssp"-->
<!--                android:textStyle="bold"-->
<!--                app:backgroundTint="@color/white"-->
<!--                app:cornerRadius="15dp"-->
<!--                app:layout_constraintEnd_toEndOf="parent"-->
<!--                app:strokeColor="@color/black20"-->
<!--                app:strokeWidth="1dp" />-->


<!--            <com.google.android.material.button.MaterialButton-->
<!--                android:id="@+id/btnYes"-->
<!--                style="@style/FontLocalization"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="60dp"-->
<!--                android:layout_marginTop="30dp"-->
<!--                android:layout_weight="1"-->
<!--                android:alpha="0.5"-->
<!--                android:clickable="false"-->
<!--                android:enabled="false"-->
<!--                android:focusable="false"-->
<!--                android:gravity="center"-->
<!--                android:orientation="horizontal"-->
<!--                android:text="@string/payment"-->
<!--                android:textAllCaps="false"-->
<!--                android:textColor="@color/white"-->
<!--                android:textSize="@dimen/_18ssp"-->
<!--                android:textStyle="bold"-->
<!--                app:backgroundTint="@color/primaryColor"-->
<!--                app:cornerRadius="15dp"-->
<!--                app:layout_constraintEnd_toEndOf="parent"-->
<!--                app:strokeColor="@color/primaryColor"-->
<!--                app:strokeWidth="0dp" />-->
<!--        </LinearLayout>-->


    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>