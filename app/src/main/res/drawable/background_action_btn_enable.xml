<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <!--            <stroke-->
            <!--                android:width="0.5dp"-->
            <!--                android:color="@color/black12"/>-->
            <!--            <stroke-->
            <!--                android:width="1dp"-->
            <!--                android:color="@color/black12"-->
            <!--                android:dashWidth="0dp"-->
            <!--                android:dashGap="0dp" />-->
            <!--            <padding-->
            <!--                android:bottom="2dp"-->
            <!--                android:left="0dp"-->
            <!--                android:right="0dp"-->
            <!--                android:top="0dp" />-->
        </shape>
    </item>
    <item
        android:height="1dp"
        android:gravity="top"> <!--设置底部有边框-->
        <shape>
            <solid android:color="@color/black08" />  <!-- 主体背景颜色 -->
        </shape>
    </item>
</layer-list>