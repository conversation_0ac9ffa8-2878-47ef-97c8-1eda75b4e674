<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="120dp"
    android:height="120dp"
    android:viewportWidth="120"
    android:viewportHeight="120">
  <path
      android:pathData="M79.37,53.29V92.68H23.53C21.32,92.68 19.67,90.91 19.67,88.81V53.29H79.37Z"
      android:fillColor="#000000"
      android:fillAlpha="0.06"/>
  <path
      android:pathData="M101.52,53.29V88.81C101.52,91.02 99.69,92.68 97.5,92.68H79.37V53.29H101.52Z"
      android:fillAlpha="0.06">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="101.25"
          android:startY="93"
          android:endX="79.5"
          android:endY="53.25"
          android:type="linear">
        <item android:offset="0" android:color="#FF000000"/>
        <item android:offset="1" android:color="#11000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M41.21,53.29L52.95,34.83H113.21L101.14,53.29H41.21Z"
      android:fillAlpha="0.06">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="77.21"
          android:startY="34.83"
          android:endX="77.25"
          android:endY="54.37"
          android:type="linear">
        <item android:offset="0" android:color="#FF000000"/>
        <item android:offset="1" android:color="#00000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M101.52,53.4V74.21H85.37C83.87,74.21 82.83,73.24 82.6,71.84L79.37,53.29L101.52,53.4Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="1187.57"
          android:startY="2189.29"
          android:endX="1187.57"
          android:endY="282.79"
          android:type="linear">
        <item android:offset="0" android:color="#00606673"/>
        <item android:offset="1" android:color="#FFD2D2D2"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M101.06,53.29H79.37L90.38,69.98C91.17,71.09 92.4,71.75 93.64,71.75H110.5C111.62,71.75 112.41,70.42 111.73,69.54L101.06,53.29Z"
      android:fillColor="#000000"
      android:fillAlpha="0.06"/>
  <path
      android:pathData="M79.37,53.29L67.54,34.83H6.75L19.02,53.29H79.37Z"
      android:fillAlpha="0.06">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="48.75"
          android:startY="35.62"
          android:endX="50.63"
          android:endY="53.25"
          android:type="linear">
        <item android:offset="0" android:color="#FF000000"/>
        <item android:offset="1" android:color="#11000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M55.06,72H26.69C26.03,72 25.5,71.5 25.5,70.87C25.5,70.25 26.03,69.75 26.69,69.75H55.06C55.72,69.75 56.25,70.25 56.25,70.87C56.12,71.5 55.72,72 55.06,72Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M55.06,78H26.69C26.03,78 25.5,77.49 25.5,76.87C25.5,76.24 26.03,75.75 26.69,75.75H55.06C55.72,75.75 56.25,76.24 56.25,76.87C56.12,77.49 55.72,78 55.06,78Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M40.04,84.75H26.71C26.04,84.75 25.5,84.25 25.5,83.62C25.5,83 26.04,82.5 26.71,82.5H40.04C40.71,82.5 41.25,83 41.25,83.62C41.12,84.25 40.58,84.75 40.04,84.75Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M79.37,53.29V92.68H23.53C21.32,92.68 19.67,90.91 19.67,88.81V53.29H79.37Z"
      android:fillColor="#000000"
      android:fillAlpha="0.06"/>
  <path
      android:pathData="M101.52,53.29V88.81C101.52,91.02 99.69,92.68 97.5,92.68H79.37V53.29H101.52Z"
      android:fillAlpha="0.06">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="101.25"
          android:startY="93"
          android:endX="79.5"
          android:endY="53.25"
          android:type="linear">
        <item android:offset="0" android:color="#FF000000"/>
        <item android:offset="1" android:color="#11000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M41.21,53.29L52.95,34.83H113.21L101.14,53.29H41.21Z"
      android:fillAlpha="0.06">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="77.21"
          android:startY="34.83"
          android:endX="77.25"
          android:endY="54.37"
          android:type="linear">
        <item android:offset="0" android:color="#FF000000"/>
        <item android:offset="1" android:color="#00000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M101.52,53.4V74.21H85.37C83.87,74.21 82.83,73.24 82.6,71.84L79.37,53.29L101.52,53.4Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="1187.57"
          android:startY="2189.29"
          android:endX="1187.57"
          android:endY="282.79"
          android:type="linear">
        <item android:offset="0" android:color="#00606673"/>
        <item android:offset="1" android:color="#FFD2D2D2"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M101.06,53.29H79.37L90.38,69.98C91.17,71.09 92.4,71.75 93.64,71.75H110.5C111.62,71.75 112.41,70.42 111.73,69.54L101.06,53.29Z"
      android:fillColor="#000000"
      android:fillAlpha="0.06"/>
  <path
      android:pathData="M79.37,53.29L67.54,34.83H6.75L19.02,53.29H79.37Z"
      android:fillAlpha="0.06">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="48.75"
          android:startY="35.62"
          android:endX="50.63"
          android:endY="53.25"
          android:type="linear">
        <item android:offset="0" android:color="#FF000000"/>
        <item android:offset="1" android:color="#11000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M55.06,72H26.69C26.03,72 25.5,71.5 25.5,70.87C25.5,70.25 26.03,69.75 26.69,69.75H55.06C55.72,69.75 56.25,70.25 56.25,70.87C56.12,71.5 55.72,72 55.06,72Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M55.06,78H26.69C26.03,78 25.5,77.49 25.5,76.87C25.5,76.24 26.03,75.75 26.69,75.75H55.06C55.72,75.75 56.25,76.24 56.25,76.87C56.12,77.49 55.72,78 55.06,78Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M40.04,84.75H26.71C26.04,84.75 25.5,84.25 25.5,83.62C25.5,83 26.04,82.5 26.71,82.5H40.04C40.71,82.5 41.25,83 41.25,83.62C41.12,84.25 40.58,84.75 40.04,84.75Z"
      android:fillColor="#ffffff"/>
</vector>
