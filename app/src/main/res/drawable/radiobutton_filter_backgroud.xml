<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true" >
        <shape
            android:shape="rectangle">
            <solid
                android:color="@color/primaryColor" />
            <corners android:radius="25dp"/>
        </shape>
    </item>
    <item android:state_checked="true" >
        <shape
            android:shape="rectangle">
            <solid
                android:color="@color/primaryColor" />
            <corners android:radius="25dp"/>
        </shape>
    </item>
    <item android:state_checked="false">
        <shape
            android:shape="rectangle">
            <solid
                android:color="@color/mainWhite" />
            <corners android:radius="25dp"/>'
        </shape>

    </item>
</selector>