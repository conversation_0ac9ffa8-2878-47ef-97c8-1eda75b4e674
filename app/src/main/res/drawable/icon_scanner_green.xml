<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="32dp"
    android:height="32dp"
    android:viewportWidth="32"
    android:viewportHeight="32">
  <path
      android:pathData="M19.566,5.674C21.694,5.674 21.873,5.676 23.021,5.83C24.143,5.981 24.791,6.265 25.263,6.737C25.807,7.281 26.043,7.702 26.175,8.454C26.322,9.301 26.326,9.176 26.326,11.318C26.326,11.78 26.7,12.155 27.163,12.155C27.625,12.155 28,11.78 28,11.318L28,11.21C28,9.202 28,9.175 27.824,8.166C27.628,7.044 27.211,6.317 26.447,5.553C25.612,4.718 24.552,4.347 23.244,4.171C21.972,4 21.68,4 19.629,4H19.566C19.104,4 18.729,4.375 18.729,4.837C18.729,5.3 19.104,5.674 19.566,5.674Z"
      android:fillColor="#0F9D58"/>
  <path
      android:pathData="M4.837,19.845C5.3,19.845 5.674,20.22 5.674,20.682C5.674,22.824 5.678,22.699 5.825,23.546C5.957,24.298 6.193,24.719 6.737,25.263C7.209,25.735 7.856,26.019 8.98,26.17C10.127,26.324 10.306,26.326 12.434,26.326C12.896,26.326 13.271,26.7 13.271,27.163C13.271,27.625 12.896,28 12.434,28H12.371C10.32,28 10.028,28 8.756,27.829C7.448,27.653 6.388,27.282 5.553,26.447C4.789,25.683 4.372,24.956 4.176,23.834C4,22.825 4,22.798 4,20.79L4,20.682C4,20.22 4.375,19.845 4.837,19.845Z"
      android:fillColor="#0F9D58"/>
  <path
      android:pathData="M27.163,19.845C27.625,19.845 28,20.22 28,20.682L28,20.79C28,22.798 28,22.825 27.824,23.834C27.628,24.956 27.211,25.683 26.447,26.447C25.612,27.282 24.552,27.653 23.244,27.829C21.972,28 21.68,28 19.629,28H19.566C19.104,28 18.729,27.625 18.729,27.163C18.729,26.7 19.104,26.326 19.566,26.326C21.694,26.326 21.873,26.324 23.021,26.17C24.143,26.019 24.791,25.735 25.263,25.263C25.807,24.719 26.043,24.298 26.175,23.546C26.322,22.699 26.326,22.824 26.326,20.682C26.326,20.22 26.7,19.845 27.163,19.845Z"
      android:fillColor="#0F9D58"/>
  <path
      android:pathData="M12.371,4H12.434C12.896,4 13.271,4.375 13.271,4.837C13.271,5.3 12.896,5.674 12.434,5.674C10.306,5.674 10.127,5.676 8.98,5.83C7.856,5.981 7.209,6.265 6.737,6.737C6.193,7.281 5.957,7.702 5.825,8.454C5.678,9.301 5.674,9.176 5.674,11.318C5.674,11.78 5.3,12.155 4.837,12.155C4.375,12.155 4,11.78 4,11.318L4,11.21C4,9.202 4,9.175 4.176,8.166C4.372,7.044 4.789,6.317 5.553,5.553C6.388,4.718 7.448,4.347 8.756,4.171C10.028,4 10.32,4 12.371,4Z"
      android:fillColor="#0F9D58"/>
  <path
      android:pathData="M4.837,15.163C4.375,15.163 4,15.538 4,16C4,16.462 4.375,16.837 4.837,16.837H27.163C27.625,16.837 28,16.462 28,16C28,15.538 27.625,15.163 27.163,15.163H4.837Z"
      android:fillColor="#0F9D58"/>
  <path
      android:pathData="M19.566,5.674C21.694,5.674 21.873,5.676 23.021,5.83C24.143,5.981 24.791,6.265 25.263,6.737C25.807,7.281 26.043,7.702 26.175,8.454C26.322,9.301 26.326,9.176 26.326,11.318C26.326,11.78 26.7,12.155 27.163,12.155C27.625,12.155 28,11.78 28,11.318L28,11.21C28,9.202 28,9.175 27.824,8.166C27.628,7.044 27.211,6.317 26.447,5.553C25.612,4.718 24.552,4.347 23.244,4.171C21.972,4 21.68,4 19.629,4H19.566C19.104,4 18.729,4.375 18.729,4.837C18.729,5.3 19.104,5.674 19.566,5.674Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.6"
      android:fillColor="#00000000"
      android:strokeColor="#0F9D58"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M4.837,19.845C5.3,19.845 5.674,20.22 5.674,20.682C5.674,22.824 5.678,22.699 5.825,23.546C5.957,24.298 6.193,24.719 6.737,25.263C7.209,25.735 7.856,26.019 8.98,26.17C10.127,26.324 10.306,26.326 12.434,26.326C12.896,26.326 13.271,26.7 13.271,27.163C13.271,27.625 12.896,28 12.434,28H12.371C10.32,28 10.028,28 8.756,27.829C7.448,27.653 6.388,27.282 5.553,26.447C4.789,25.683 4.372,24.956 4.176,23.834C4,22.825 4,22.798 4,20.79L4,20.682C4,20.22 4.375,19.845 4.837,19.845Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.6"
      android:fillColor="#00000000"
      android:strokeColor="#0F9D58"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M27.163,19.845C27.625,19.845 28,20.22 28,20.682L28,20.79C28,22.798 28,22.825 27.824,23.834C27.628,24.956 27.211,25.683 26.447,26.447C25.612,27.282 24.552,27.653 23.244,27.829C21.972,28 21.68,28 19.629,28H19.566C19.104,28 18.729,27.625 18.729,27.163C18.729,26.7 19.104,26.326 19.566,26.326C21.694,26.326 21.873,26.324 23.021,26.17C24.143,26.019 24.791,25.735 25.263,25.263C25.807,24.719 26.043,24.298 26.175,23.546C26.322,22.699 26.326,22.824 26.326,20.682C26.326,20.22 26.7,19.845 27.163,19.845Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.6"
      android:fillColor="#00000000"
      android:strokeColor="#0F9D58"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M12.371,4H12.434C12.896,4 13.271,4.375 13.271,4.837C13.271,5.3 12.896,5.674 12.434,5.674C10.306,5.674 10.127,5.676 8.98,5.83C7.856,5.981 7.209,6.265 6.737,6.737C6.193,7.281 5.957,7.702 5.825,8.454C5.678,9.301 5.674,9.176 5.674,11.318C5.674,11.78 5.3,12.155 4.837,12.155C4.375,12.155 4,11.78 4,11.318L4,11.21C4,9.202 4,9.175 4.176,8.166C4.372,7.044 4.789,6.317 5.553,5.553C6.388,4.718 7.448,4.347 8.756,4.171C10.028,4 10.32,4 12.371,4Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.6"
      android:fillColor="#00000000"
      android:strokeColor="#0F9D58"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M4.837,15.163C4.375,15.163 4,15.538 4,16C4,16.462 4.375,16.837 4.837,16.837H27.163C27.625,16.837 28,16.462 28,16C28,15.538 27.625,15.163 27.163,15.163H4.837Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.6"
      android:fillColor="#00000000"
      android:strokeColor="#0F9D58"
      android:strokeLineCap="round"/>
</vector>
