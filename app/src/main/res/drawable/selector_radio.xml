<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 选中状态：绿色背景+左圆角 -->
    <item android:state_checked="true">
        <shape android:shape="rectangle">
            <!-- 仅左半部分圆角（与容器圆角一致） -->
            <corners android:radius="20dp" />
            <!-- 选中背景色（绿色，可根据需求调整） -->
            <solid android:color="@color/primaryColor" />
        </shape>
    </item>
    <!-- 未选中状态：白色背景+左圆角 -->
    <item android:state_checked="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/transparent" />
        </shape>
    </item>
</selector>