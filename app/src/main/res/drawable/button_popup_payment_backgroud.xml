<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true" >
        <shape
            android:shape="rectangle">
            <solid
                android:color="@color/mainWhite" />
            <corners android:radius="8dp"/>
            <stroke android:color="@color/primaryColor" android:width="1dp"/>
        </shape>
    </item>
    <item android:state_enabled="false" >
        <shape
            android:shape="rectangle">
            <solid
                android:color="@color/mainWhite" />
            <corners android:radius="8dp"/>
        </shape>
    </item>
    <item android:state_enabled="true">
        <shape
            android:shape="rectangle">
            <solid
                android:color="@color/mainWhite" />
            <corners android:radius="8dp"/>'
            <stroke android:color="@color/black08" android:width="1dp"/>
        </shape>

    </item>
</selector>