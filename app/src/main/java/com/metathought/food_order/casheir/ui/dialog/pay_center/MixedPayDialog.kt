package com.metathought.food_order.casheir.ui.dialog.pay_center

import android.app.Dialog
import android.content.DialogInterface
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.hbb20.CountryCodePicker
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OfflinePaymentChannelEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.constant.PaymentChannel
import com.metathought.food_order.casheir.constant.SceneEnum
import com.metathought.food_order.casheir.data.CashConvertModel
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.CreditRecord
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel
import com.metathought.food_order.casheir.data.model.base.response_model.order.PaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.RepaymentResponse
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.databinding.DialogMixedPayBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.getColor
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.hideKeyboard2
import com.metathought.food_order.casheir.extension.isZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.roundToTwoDecimalPlaces
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.extension.setNumberRange
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.helper.LocaleHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.ordered.offline.OfflineChannelsDialog
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.math.BigDecimal
import kotlin.math.absoluteValue
import kotlin.math.round


/**
 *<AUTHOR>
 *@time  2025/4/1
 *@desc
 **/

@AndroidEntryPoint
class MixedPayDialog : BaseDialogFragment(), CountryCodePicker.DialogEventsListener {
    companion object {
        private const val TAG = "MixedPayDialog"
        private const val CONVERSION_RATIO = "conversionRatio"
        private const val TOTAL_PRICE = "total_price"
        private const val COUNTRY_CODE = "countryCode"
        private const val PHONE = "phone"
        private const val SHOPPING_RECORD = "shopping_record"
        private const val ORDER_ID = "order_id"
        private const val OFFLINE_CHANNEL_LIST = "offline_channel_list"
        private const val IS_IN_MENU = "is_in_menu"
        fun showDialog(
            fragmentManager: FragmentManager,
            menuOrderScreen: SecondaryScreenUI? = null,
            orderId: String? = null,
            orderInfo: OrderedInfoResponse? = null,
            shoppingRecord: ShoppingRecord? = null,
            conversionRatio: Long? = null,
            totalPrice: Long? = null,
            countryCode: String? = null,
            phone: String? = null,
            offlineChannelList: List<OfflineChannelModel>? = null,
            scene: Int? = null,
            creditRecord: CreditRecord? = null,
            repaymentResponse: ((ApiResponse<RepaymentResponse>) -> Unit)? = null,
            paymentResponse: ((ApiResponse<PaymentResponse>) -> Unit)? = null,
            onlineSuccessListener: ((OrderedInfoResponse) -> Unit)? = null,
            onCloseListener: ((String?) -> Unit)? = null,
            onTopUpListener: ((CustomerMemberResponse) -> Unit)? = null,
            onClearShoppingListener: (() -> Unit)? = null,
            onUpdateCustomizeKhr: (() -> Unit)? = null,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(
                    menuOrderScreen = menuOrderScreen,
                    orderId = orderId,
                    orderInfo = orderInfo,
                    shoppingRecord = shoppingRecord,
                    conversionRatio = conversionRatio,
                    totalPrice = totalPrice,
                    paymentResponse = paymentResponse,
                    countryCode = countryCode,
                    phone = phone,
                    offlineChannelList = offlineChannelList,
                    scene = scene,
                    creditRecord = creditRecord,
                    repaymentResponse = repaymentResponse,
                    onlineSuccessListener = onlineSuccessListener,
                    onCloseListener = onCloseListener,
                    onTopUpListener = onTopUpListener,
                    onClearShoppingListener = onClearShoppingListener,
                    onUpdateCustomizeKhr = onUpdateCustomizeKhr
                )
            fragment.show(fragmentManager, TAG)
        }

        private fun newInstance(
            menuOrderScreen: SecondaryScreenUI? = null,
            orderId: String? = null,
            orderInfo: OrderedInfoResponse? = null,
            shoppingRecord: ShoppingRecord? = null,
            conversionRatio: Long? = null,
            totalPrice: Long? = null,
            countryCode: String? = null,
            phone: String? = null,
            offlineChannelList: List<OfflineChannelModel>? = null,
            scene: Int? = null,
            creditRecord: CreditRecord? = null,
            repaymentResponse: ((ApiResponse<RepaymentResponse>) -> Unit)? = null,
            paymentResponse: ((ApiResponse<PaymentResponse>) -> Unit)? = null,
            onlineSuccessListener: ((OrderedInfoResponse) -> Unit)? = null,
            onCloseListener: ((String?) -> Unit)? = null,
            onTopUpListener: ((CustomerMemberResponse) -> Unit)? = null,
            onClearShoppingListener: (() -> Unit)? = null,
            onUpdateCustomizeKhr: (() -> Unit)? = null,
        ): MixedPayDialog {
            val args = Bundle()
            val fragment = MixedPayDialog()
            if (orderId != null) {
                args.putString(ORDER_ID, orderId)
            }
            if (conversionRatio != null) {
                args.putLong(CONVERSION_RATIO, conversionRatio)
            }
            if (totalPrice != null) {
                args.putLong(TOTAL_PRICE, totalPrice)
            }

            if (countryCode != null) {
                args.putString(COUNTRY_CODE, countryCode)
            }
            if (phone != null) {
                args.putString(PHONE, phone)
            }
            if (shoppingRecord != null) {
                args.putParcelable(SHOPPING_RECORD, shoppingRecord)
            }
            if (offlineChannelList != null) {
                args.putParcelableArrayList(OFFLINE_CHANNEL_LIST, ArrayList(offlineChannelList))
            }


            fragment.arguments = args
            fragment.menuOrderScreen = menuOrderScreen
            fragment.orderInfo = orderInfo
            fragment.paymentResponse = paymentResponse
            fragment.onlineSuccessListener = onlineSuccessListener
            fragment.onCloseListener = onCloseListener
            fragment.onTopUpListener = onTopUpListener
            fragment.onClearShoppingListener = onClearShoppingListener
            fragment.onUpdateCustomizeKhr = onUpdateCustomizeKhr
            fragment.scene = scene
            fragment.creditRecord = creditRecord
            fragment.repaymentResponse = repaymentResponse
            return fragment
        }

        fun getDialog(fragmentManager: FragmentManager): MixedPayDialog? {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? MixedPayDialog
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? MixedPayDialog
            fragment?.dismissAllowingStateLoss()
        }
    }

    private val payViewModel: PayViewModel by viewModels()

    private var binding: DialogMixedPayBinding? = null

//    private var mAdapter: PayChannelAdapter? = null

    private var menuOrderScreen: SecondaryScreenUI? = null

    private var paymentResponse: ((ApiResponse<PaymentResponse>) -> Unit)? = null

    //线上支付成功
    private var onlineSuccessListener: ((OrderedInfoResponse) -> Unit)? = null

    //弹窗隐藏
    private var onCloseListener: ((String?) -> Unit)? = null

    //去充值
    private var onTopUpListener: ((CustomerMemberResponse) -> Unit)? = null

    //清空购物车
    private var onClearShoppingListener: (() -> Unit)? = null

    //更新自定义快捷khr
    private var onUpdateCustomizeKhr: (() -> Unit)? = null

    //汇率
    private var conversionRatio: Long = FoundationHelper.conversionRatio

    //总的销售价
    private var totalPrice: Long = 0


    //区号
    private var countryCode: String? = null

    //手机号
    private var phone: String? = null

    //现金支付model
    private var cashConvertModel: CashConvertModel? = null

    private var orderInfo: OrderedInfoResponse? = null

    private var orderAmountKHR: Long? = null
    private var edtUsdAmountHint: String? = null
    private var edtKhrAmountHint: String? = null

    //是否支付成功
    private var isPaySuccess = false

    //会员信息
    private var customerMemberResponse: CustomerMemberResponse? = null

//    //余额是否足够
//    private var isBalanceInsufficient: Boolean = false

    //先付款购物车
    private var shoppingRecord: ShoppingRecord? = null

    //扣掉余额支付剩余的金额（也就是剩余需要余额支付的金额）
    private var remainCashPrice: BigDecimal? = null

    //线下支付渠道
    private var offlineChannelModel: OfflineChannelModel? = null

    private var offlineChannelList: MutableList<OfflineChannelModel>? = null


    //当前进支付的场景
    private var scene: Int? = null

    //挂账记录
    private var creditRecord: CreditRecord? = null

    private var repaymentResponse: ((ApiResponse<RepaymentResponse>) -> Unit)? = null

//    private val clearErrorRunnable = Runnable {
//        binding?.apply {
//            edtBalanceAmount.setBackgroundResource(R.drawable.selector_editview_bg)
//        }
//    }
//
//    private fun postClear(duration: Int = 500) {
//        try {
//            binding?.apply {
//                edtBalanceAmount.removeCallbacks(clearErrorRunnable)
//                if (duration <= 0) {
//                    clearErrorRunnable.run()
//                } else {
//                    edtBalanceAmount.postDelayed(clearErrorRunnable, duration.toLong())
//                }
//            }
//        } catch (e: Exception) {
//
//        }
//
//    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogMixedPayBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        openKeyBoardListener()
        onTouchOutSide(binding?.root)


        initListener()
        initObserver()
        initView()
//        payViewModel.getChannels()

    }

    private fun initObserver() {
        payViewModel.uiRequestState.observe(viewLifecycleOwner) { state ->
            if (state.paymentResponse is ApiResponse.Loading) {
                binding?.viewKeyBoard?.setConfirm(false)

                paymentResponse?.invoke(state.paymentResponse)
            } else if (state.paymentResponse is ApiResponse.Success) {

                if (shoppingRecord != null) {
                    onClearShoppingListener?.invoke()
                }
                isPaySuccess = true
                paymentResponse?.invoke(state.paymentResponse)
//                dismissAllowingStateLoss()
            } else if (state.paymentResponse is ApiResponse.Error) {
                paymentResponse?.invoke(state.paymentResponse)
                checkBtn()
//                dismissAllowingStateLoss()
            }
        }
        payViewModel.uiRepaymentState.observe(viewLifecycleOwner) { state ->
            if (state.paymentResponse is ApiResponse.Loading) {
                binding?.viewKeyBoard?.setConfirm(false)

                repaymentResponse?.invoke(state.paymentResponse)
            } else if (state.paymentResponse is ApiResponse.Success) {

                if (shoppingRecord != null) {
                    onClearShoppingListener?.invoke()
                }
                isPaySuccess = true
                repaymentResponse?.invoke(state.paymentResponse)
//                dismissAllowingStateLoss()
            } else if (state.paymentResponse is ApiResponse.Error) {
                repaymentResponse?.invoke(state.paymentResponse)
                checkBtn()
//                dismissAllowingStateLoss()
            }
        }

        payViewModel.uiMemberState.observe(viewLifecycleOwner) {
            it.memberAccountResult?.let { res ->
                binding?.apply {
                    when (res) {
                        is ApiResponse.Loading -> {
//                            viewKeyBoard.setConfirm(false)
                            customerMemberResponse = null
                            pbLoadMember.isVisible = true
                            tvCustomerNameTitle.isVisible = false
                            tvCustomerName.isVisible = false
                            tvCustomerBalanceTitle.isVisible = false
                            tvCustomerBalance.isVisible = false
                            tvNotFound.isVisible = false
                            checkBtn()
                        }

                        is ApiResponse.Error -> {
//                            viewKeyBoard.setConfirm(false)
                            customerMemberResponse = null
                            pbLoadMember.isVisible = false
                            tvNotFound.isVisible = true
                            tvCustomerNameTitle.isVisible = false
                            tvCustomerName.isVisible = false
                            tvCustomerBalanceTitle.isVisible = false
                            tvCustomerBalance.isVisible = false
                            checkBtn()
                        }

                        is ApiResponse.Success -> {
                            pbLoadMember.setVisibleGone(false)
                            if (res.data != null && getCurrentPhone() == res.data?.telephone) {
                                customerMemberResponse = res.data
                                //如果返回的手机号是当前输入的手机号才有效
//                                viewKeyBoard.setConfirm(true)
                                tvCustomerName.text = res.data?.nickName
                                tvCustomerBalance.text =
                                    "${res.data?.balance?.priceFormatTwoDigitZero2()}"
                            } else {
                                customerMemberResponse = null
//                                viewKeyBoard.setConfirm(false)
                                pbLoadMember.isVisible = false
                                tvNotFound.isVisible = true
                                tvCustomerName.text = "— —"
                                tvCustomerBalance.text = "— —"
                            }
                            tvCustomerNameTitle.isVisible = true
                            tvCustomerName.isVisible = true
                            tvCustomerBalanceTitle.isVisible = true
                            tvCustomerBalance.isVisible = true
                            checkBtn()
                            if (edtBalanceAmount.text.isNotEmpty()) {
                                checkBalanceInput(true)
                            }

                        }
                    }
                }
            }
        }
    }

    private fun initListener() {
        binding?.apply {
            val lang = LocaleHelper.getLang(MyApplication.myAppInstance)
            if (lang.startsWith("zh")) {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.CHINESE_SIMPLIFIED)
            } else if (lang.startsWith("km")) {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.KHMER)
            } else {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.ENGLISH)
            }
            countryCodeHolder.supportFragementManager = activity?.supportFragmentManager
            countryCodeHolder.setDialogEventsListener(this@MixedPayDialog)
            countryCodeHolder.setOnCountryChangeListener {
                countryCode = countryCodeHolder.selectedCountryCode
                edtPhoneNumber.text?.isNotEmpty()?.let {
                    btnSearch.setEnable(it)
                }
                getCustomerInfo()
            }
            viewKeyBoard.setonConfirmClick {
                if (offlineChannelModel?.id == OfflinePaymentChannelEnum.CASH.id) {
                    Printer.openCashierBox()
                }
                val balanceInputAmount =
                    edtBalanceAmount.text.toString().toBigDecimalOrNull() ?: BigDecimal.ZERO
                if (balanceInputAmount > BigDecimal.valueOf(
                        (customerMemberResponse?.balance ?: 0).div(100.0)
                    ).halfUp(2)
                ) {
                    customerMemberResponse?.let { it1 ->
                        onTopUpListener?.invoke(it1)
                    }
                } else {
                    Timber.e("payViewModel.orderNo ${payViewModel.orderNo}")
                    customerMemberResponse?.let { it1 ->
                        if (scene == SceneEnum.MEMBER_CREDIT.id) {
                            payViewModel.repayment(
                                consumerId = creditRecord?.consumerId ?: 0,
                                payType = PayTypeEnum.MIXED_PAYMENT,
                                accountId = it1.accountId,
                                isPosPrint = false,
                                balancePayAmount = balanceInputAmount,
                                offlineChannelModel = offlineChannelModel,
                                cashConvertModel = cashConvertModel
                            )
                        } else if (payViewModel.orderNo != null) {
                            payViewModel?.payAgain(
                                orderInfo = orderInfo,
                                payType = PayTypeEnum.MIXED_PAYMENT,
                                accountId = it1.accountId,
                                balancePayAmount = balanceInputAmount,
                                offlineChannelModel = offlineChannelModel,
                                cashConvert = cashConvertModel
                            )
                        } else {
                            payViewModel?.payment(
                                payType = PayTypeEnum.MIXED_PAYMENT,
                                shareRecord = shoppingRecord,
                                accountId = it1.accountId,
                                balancePayAmount = balanceInputAmount,
                                offlineChannelModel = offlineChannelModel,
                                cashConvertModel = cashConvertModel
                            )
                        }
                    }

                }
            }

            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

            llCustomizePay.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    OfflineChannelsDialog.showDialog(
                        parentFragmentManager,
                        offlineChannelList,
                        offlineChannelModel
                    ) {
                        offlineChannelModel = it
                        updateOfflineChannel()
                    }
//                    showPopupWindowLogout(llCustomizePay)
                }
            }

            edtBalanceAmount.addTextChangedListener {
                Timber.e("edtBalanceAmount ${edtBalanceAmount.text}")
                val balancePriceStr = edtBalanceAmount.text.trim().toString().toBigDecimalOrNull()
                if (balancePriceStr == null) {
                    tvBalancePrice.text = "$ - -"
                } else {
                    tvBalancePrice.text = "${balancePriceStr.priceFormatTwoDigitZero2()}"
                }
                remainCashPrice =
                    BigDecimal.valueOf(totalPrice.div(100.0)) - (it.toString().toBigDecimalOrNull()
                        ?: BigDecimal.ZERO)
                checkBalanceInput(false)
                updateOfflineChannel()
            }
            edtBalanceAmount.setOnClickListener {
                root.post {
                    hideKeyboard2()
                }
            }
            edtBalanceAmount.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        hideKeyboard2()
                    }
                } else {
                    checkBalanceInput(true)
                }
                checkBtn()

                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsPhone(false)
                    viewKeyBoard.setIsInit(false)
                    viewKeyBoard.setRange(
                        BigDecimal.valueOf(0.01),
                        BigDecimal.valueOf(99999999.99),
                    )
                    viewKeyBoard.setCurrentEditText(edtBalanceAmount)
                }
            }

            edtPhoneNumber.addTextChangedListener {
                if (edtPhoneNumber.text.trim().isNullOrEmpty()) {
                    resetCustomerInfo()
                } else {
                    checkBtn()
                }
            }
            edtPhoneNumber.setOnClickListener {
                root.post {
                    hideKeyboard2()
                }
            }
            edtPhoneNumber.setOnFocusChangeListener { view, b ->
                Timber.e("edtPhoneNumber focus:${b}")
                if (b) {
                    root.post {
                        hideKeyboard2()
                    }
                } else {
                    getCustomerInfo()
                }
                checkBtn()

                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsPhone(true)
                    viewKeyBoard.setIsInit(true)
//                    viewKeyBoard.setRange(
//                        BigDecimal.valueOf(0),
//                        BigDecimal.valueOf(999999999999),
//                    )
                    viewKeyBoard.setCurrentEditText(edtPhoneNumber)
                }
            }

            edtUsdAmount.setOnClickListener {
                root.post {
                    hideKeyboard2()
                }
            }
            edtUsdAmount.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        hideKeyboard2()
                    }
                }

                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsPhone(false)
                    viewKeyBoard.setIsInit(false)
                    viewKeyBoard.setRange(
                        BigDecimal.valueOf(0.0),
                        BigDecimal.valueOf(99999999.99),
                    )
                    viewKeyBoard.setCurrentEditText(edtUsdAmount)
                }
            }

            edtKhrAmount.setOnClickListener {
                root.post {
                    hideKeyboard2()
                }
            }
            edtKhrAmount.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        hideKeyboard2()
                    }
                }
                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsPhone(false)
                    viewKeyBoard.setIsInit(true)
                    viewKeyBoard.setRange(
                        BigDecimal.valueOf(0.0),
                        BigDecimal.valueOf(999999900),
                    )
                    viewKeyBoard.setCurrentEditText(edtKhrAmount)
                }
            }

            //限制输入范围 Limit input range
//            edtUsdAmount.setNumberRange(0, 99999999)
            edtUsdAmount.addTextChangedListener {
                calculateChange()
            }
            edtKhrAmount.setNumberRange(0, 999999900)
            edtKhrAmount.addTextChangedListener {
                calculateChange()
            }

            btnSearch.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    getCustomerInfo()
                }
            }

            if (scene == SceneEnum.MEMBER_CREDIT.id) {
                edtPhoneNumber.isEnabled = false
                countryCodeHolder.setCcpClickable(false)
                btnSearch.isVisible = false
            }

        }
    }

    fun checkBalanceInput(isShowToast: Boolean) {
        binding?.apply {
            edtBalanceAmount.setBackgroundResource(R.drawable.selector_editview_bg)
            val price =
                edtBalanceAmount.text.toString().toBigDecimalOrNull() ?: BigDecimal.valueOf(
                    0
                )
            if (customerMemberResponse != null && price > BigDecimal.valueOf(
                    (customerMemberResponse?.balance ?: 0).div(100.0)
                )
            ) {
                //先判断余额是否充足
                if (isShowToast) {
                    showToast(getString(R.string.insufficient_balance))
                }
                edtBalanceAmount.setBackgroundResource(R.drawable.background_white_border_ff3141_radius_12)
//                        postClear(1000)
            } else if (price.isZero()) {
                //余额输入的金额不能为0,重设为0.01
//                        edtBalanceAmount.setText(
//                            BigDecimal.valueOf(
//                                0.01
//                            ).halfUp(2).toString()
//                        )
                if (isShowToast) {
                    showToast(getString(R.string.balance_pay_amount_not_zero))
                }
                edtBalanceAmount.setBackgroundResource(R.drawable.background_white_border_ff3141_radius_12)
//                        postClear(1000)
            } else if (price >= BigDecimal.valueOf(totalPrice.div(100.0))) {
                //余额输入的金额必须小于应付金额 重设为比最大值
//                        edtBalanceAmount.setText(
//                            BigDecimal.valueOf(totalPrice.div(100.0)).minus(
//                                BigDecimal.valueOf(
//                                    0.01
//                                )
//                            ).halfUp(2).toString()
//                        )
                if (isShowToast) {
                    showToast(getString(R.string.balance_pay_amount_must_small_than_total))
                }
                edtBalanceAmount.setBackgroundResource(R.drawable.background_white_border_ff3141_radius_12)
//                        postClear(1000)
            }
        }
    }

    fun setOrderId(orderNo: String?) {
        payViewModel.orderNo = orderNo
    }

    private fun initView() {
        conversionRatio = arguments?.getLong(CONVERSION_RATIO, FoundationHelper.conversionRatio)
            ?: FoundationHelper.conversionRatio

        totalPrice = arguments?.getLong(TOTAL_PRICE, 0L) ?: 0L

        countryCode = arguments?.getString(COUNTRY_CODE)
        phone = arguments?.getString(PHONE)
        shoppingRecord =
            if (arguments?.containsKey(SHOPPING_RECORD) == true) arguments?.getParcelable(
                SHOPPING_RECORD
            ) else null

        payViewModel.orderNo = if (arguments?.containsKey(ORDER_ID) == true) arguments?.getString(
            ORDER_ID
        ) else null

        offlineChannelList =
            if (arguments?.containsKey(OFFLINE_CHANNEL_LIST) == true) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    arguments?.getParcelableArrayList(
                        OFFLINE_CHANNEL_LIST,
                        OfflineChannelModel::class.java
                    )
                } else {
                    arguments?.getParcelableArrayList<OfflineChannelModel>(OFFLINE_CHANNEL_LIST)
                }
            } else mutableListOf()

        offlineChannelModel = offlineChannelList?.firstOrNull()
//            offlineChannelList?.firstOrNull() { it.id == OfflinePaymentChannelEnum.CASH.id }

//        val index = offlineChannelList?.indexOf(offlineChannelModel) ?: -1
//        if (index != -1) {
//            offlineChannelList?.removeAt(index)
//            offlineChannelList?.add(0, offlineChannelModel!!)
//        }


        binding?.apply {
            tvTotalPrice.text = totalPrice.priceFormatTwoDigitZero2()
            tvBalancePrice.text = "$ - -"
            tvCashKhr.text = "៛ - -"
            tvCashKhr.isVisible = false
            edtPhoneNumber.setText(phone)

            if (scene == SceneEnum.MEMBER_CREDIT.id) {
                //挂账还款，手机号输入栏禁用。所以默认焦点在金额输入栏
                edtBalanceAmount.requestFocus()
            } else {
                edtPhoneNumber.requestFocus()
            }
            if (!countryCode.isNullOrEmpty() && !phone.isNullOrEmpty()) {
                countryCodeHolder.setDefaultCountryUsingPhoneCode(
                    countryCode!!.replace(" ", "").toInt()
                )
                countryCodeHolder.setCountryForPhoneCode(countryCode!!.replace(" ", "").toInt())
            }
            getCustomerInfo()
            checkBtn()
        }

        cashPaymentView()
    }

    /**
     * 现金支付方式
     *
     */
    private fun cashPaymentView() {
        binding?.apply {
            clCash.isVisible = true
            llOther.isVisible = false

            edtUsdAmount.clearFocus()
            edtKhrAmount.clearFocus()
            requireActivity().hideKeyboard(edtUsdAmount)
            requireActivity().hideKeyboard(edtKhrAmount)

            viewCustomInput.getCustomValue(viewLifecycleOwner)
            viewCustomInput.setOnItemEditClickListener { index, longs ->
                PayCustomKhrInputDialog.showDialog(parentFragmentManager, index, longs) {
                    viewCustomInput.getCustomValue(viewLifecycleOwner)
                    onUpdateCustomizeKhr?.invoke()
                }
            }
            viewCustomInput.setCurrentEditText(edtKhrAmount)

            tvConversionRatio.text = "$1 = ៛${conversionRatio}"
            updateOfflineChannel()
        }
    }

//    private fun initCashData() {
//        binding?.apply {
//
//
//
//            updateOfflineChannel()
//        }
//    }

    private fun updateOfflineChannel() {
        binding?.apply {
            if (offlineChannelModel?.id == OfflinePaymentChannelEnum.CASH.id) {
                tvCustomizePay.text = getString(R.string.pay_by_cash)
                tvCashUsdTitle.text = getString(R.string.pay_by_cash)
                tvOffline.isVisible = false
                initCashPay()
            } else {
//                tvCustomizePay.text = offlineChannelModel?.getSpannableStringBuilder(
//                    requireContext(),
//                    requireContext().getColor(R.color.white),
//                    requireContext().getColor(R.color.white)
//                )
                tvCustomizePay.text = offlineChannelModel?.channelsName
                tvOffline.isVisible = true
                tvOffline.text = "(${getString(R.string.offline_payments)})"
                tvCashUsdTitle.text = offlineChannelModel?.channelsName
                initOtherPay()
            }
        }
    }

    private fun initCashPay() {
        binding?.apply {
            clCash.isVisible = true
            llOther.isVisible = false
            remainCashPrice?.let { orderPrice ->
                val price = orderPrice.times(BigDecimal.valueOf(100)).toLong()
                //预值都显示成瑞尔  四舍五入后的结果
                orderAmountKHR =
                    round(price.times(conversionRatio).div(10000.0)).times(100).toLong()

                edtUsdAmountHint = ""
                edtKhrAmountHint = "${orderAmountKHR?.decimalFormatZeroDigit()}"

                edtUsdAmount.hint = edtUsdAmountHint
                edtKhrAmount.hint = edtKhrAmountHint

                cashConvertModel = CashConvertModel(
                    collectCash = orderAmountKHR,
                    changeAmount = 0L,
                    collectCashDollar = BigDecimal.ZERO,
                    changeAmountDollar = 0.0
                )
                //找零金额 The amount of change
                val changeAmountStr = "$0.00"
                tvChangeAmount.text = changeAmountStr
            }
            calculateChange()
            checkBtn()
        }
    }

    private fun initOtherPay() {
        binding?.apply {
            clCash.isVisible = false
            llOther.isVisible = true
            tvCashKhr.isVisible = false
            tvCashUsd.isVisible = true
            cashConvertModel = null
            if (edtBalanceAmount.text.trim().isNullOrEmpty()) {
                tvOtherPaymentAmount.text = "$ - -"
                tvCashUsd.text = "$ - -"
            } else {
                tvOtherPaymentAmount.text = remainCashPrice?.priceFormatTwoDigitZero2()
                tvCashUsd.text = remainCashPrice?.priceFormatTwoDigitZero2()
            }
            checkBtn()
        }
    }

    /**
     * 现金支付计算
     *
     */
    private fun calculateChange() {
        binding?.apply {

            val usdAmountStr = edtUsdAmount.text?.trim().toString()
            var paidAmountUSD = BigDecimal.ZERO
            if (usdAmountStr.isNullOrEmpty()) {
                paidAmountUSD = BigDecimal.ZERO
                tvCashUsd.isVisible = false
            } else {
                paidAmountUSD = usdAmountStr.toBigDecimalOrNull() ?: BigDecimal.ZERO
                tvCashUsd.isVisible = true
                tvCashUsd.text = "$${paidAmountUSD.decimalFormatTwoDigitZero()}"
            }

            //限制美元的范围0到99999 Limit the range of USD to 0 to 99999
            if (paidAmountUSD < BigDecimal.ZERO || paidAmountUSD > BigDecimal.valueOf(99999999.99)) {
                paidAmountUSD = BigDecimal.ZERO
            }

            val khrAmountStr = edtKhrAmount.text?.trim().toString()
            var paidAmountKHR = 0L
            if (khrAmountStr.isNullOrEmpty()) {
                paidAmountKHR = 0L
                tvCashKhr.isVisible = false
            } else {
                paidAmountKHR = khrAmountStr.toLongOrNull() ?: 0L
                tvCashKhr.isVisible = true
                tvCashKhr.text = "៛${paidAmountKHR.decimalFormatZeroDigit()}"
            }

            val balanceCollectPriceStr = edtBalanceAmount.text?.trim().toString()
            if (balanceCollectPriceStr.isNullOrEmpty()) {
                //如果余额没填
                tvChangeAmount.text = "$ - -"
                edtUsdAmountHint = ""
                edtKhrAmountHint = ""
                edtUsdAmount.hint = edtUsdAmountHint
                edtKhrAmount.hint = edtKhrAmountHint
                tvChangeAmountTip.isVisible = false
                if (usdAmountStr.isNullOrEmpty() && khrAmountStr.isNullOrEmpty()) {
                    tvCashUsd.text = "$ - -"
                    tvCashUsd.isVisible = true
                    tvCashKhr.isVisible = false
                }
            } else {
                if (usdAmountStr.isNullOrEmpty() && khrAmountStr.isNullOrEmpty()) {
                    tvCashKhr.text = "៛${orderAmountKHR}"
                    tvCashUsd.isVisible = false
                    tvCashKhr.isVisible = true
                }
                val balanceCollectPrice =
                    if (balanceCollectPriceStr.isNullOrEmpty()) BigDecimal.ZERO else balanceCollectPriceStr.toBigDecimalOrNull()
                        ?: BigDecimal.ZERO

                //总的收取多少美元  整体计算完以后四舍五入
                val totalReceiveUsd = balanceCollectPrice.plus(paidAmountUSD)
                    .plus(BigDecimal.valueOf(paidAmountKHR.times(1.0) / conversionRatio)).halfUp(2)
                //找零的美元
                val changeUSD = (totalReceiveUsd - BigDecimal(
                    (totalPrice ?: 0).div(100.0)
                )).toDouble().roundToTwoDecimalPlaces()

                //找零的美元换算成瑞尔
                val changeKHRAfterCeil = FoundationHelper.usdConverToKhr(
                    conversionRatio,
                    changeUSD.times(100).toLong()
                )

                val changeAmountStr =
                    if (changeUSD < 0) "-$${changeUSD.absoluteValue.decimalFormatTwoDigitZero()}\n= -៛${changeKHRAfterCeil.absoluteValue.decimalFormatZeroDigit()}" else {
                        "$${changeUSD.absoluteValue.decimalFormatTwoDigitZero()}\n= ៛${changeKHRAfterCeil.absoluteValue.decimalFormatZeroDigit()}"
                    }

                if (edtUsdAmount.text.toString().trim()
                        .isNullOrEmpty() && edtKhrAmount.text.toString().trim().isNullOrEmpty()
                ) {
                    cashConvertModel = CashConvertModel(
                        collectCash = orderAmountKHR,
                        changeAmount = 0,
                        collectCashDollar = BigDecimal.ZERO,
                        changeAmountDollar = 0.0
                    )

                    edtUsdAmount.hint = edtUsdAmountHint
                    edtKhrAmount.hint = edtKhrAmountHint

                    tvChangeAmount.text = "$0.00"
                    tvChangeAmountTip.isVisible = false
                    context?.let {
                        tvChangeAmount.setTextColor(R.color.black.getColor(it))
                    }

                } else {
                    cashConvertModel = CashConvertModel(
                        collectCash = paidAmountKHR,
                        changeAmount = 0,
                        collectCashDollar = paidAmountUSD.halfUp(2),
                        changeAmountDollar = changeUSD
                    )
                    val enable = changeUSD >= 0

                    tvChangeAmountTip.isVisible = !enable
                    if (!enable) {
                        context?.let {
                            tvChangeAmount.setTextColor(R.color.main_red.getColor(it))
                        }
                    } else {
                        context?.let {
                            tvChangeAmount.setTextColor(R.color.black.getColor(it))
                        }
                    }

                    tvChangeAmount.text = changeAmountStr
                    edtUsdAmount.hint = ""
                    edtKhrAmount.hint = ""
                }
            }
        }
        checkBtn()
    }

    private fun checkBtn() {
        binding?.apply {
            btnSearch.setEnableWithAlpha(!edtPhoneNumber.text.isNullOrEmpty())
            if (payViewModel.uiMemberState.value?.memberAccountResult is ApiResponse.Loading) {
                viewKeyBoard.setConfirm(false)
                return@apply
            }
            var enable = true
            val price =
                edtBalanceAmount.text.toString().toBigDecimalOrNull() ?: BigDecimal.valueOf(
                    0
                )
            if (customerMemberResponse == null) {
                enable = false
            } else if (price > BigDecimal.valueOf(
                    (customerMemberResponse?.balance ?: 0).div(100.0)
                )
            ) {
                enable = false
            } else if (price.isZero()) {
                enable = false
            } else if (price >= BigDecimal.valueOf(totalPrice.div(100.0))) {
                enable = false
            }
            if (offlineChannelModel?.isCash() == true && tvChangeAmountTip.isVisible) {
                enable = false
            }

            viewKeyBoard.setConfirm(enable)
        }
    }

    /**
     * 获取用户信息
     *
     */
    private fun getCustomerInfo() {
        binding?.apply {
            if (edtPhoneNumber.text.toString().isNotEmpty()) {
                payViewModel.getMemberAccount(
                    countryCodeHolder.selectedCountryCode.plus(
                        edtPhoneNumber.text.toString()
                    )
                )
            } else {
                resetCustomerInfo()
            }
        }
    }

    /**
     * 获取当前手机号
     *
     * @return
     */
    private fun getCurrentPhone(): String {
        var phone = ""
        binding?.apply {
            if (edtPhoneNumber.text.toString().isNotEmpty()) {
                phone = countryCodeHolder.selectedCountryCode.plus(
                    edtPhoneNumber.text.toString()
                )
            }
        }
        return phone
    }

    /**
     * 重置客户信息
     *
     */
    private fun resetCustomerInfo() {
        binding?.apply {
            pbLoadMember.isVisible = false
            tvNotFound.isVisible = false
            tvCustomerName.text = "- -"
            tvCustomerBalance.text = "- -"
            tvCustomerName.isVisible = true
            tvCustomerNameTitle.isVisible = true
            tvCustomerBalance.isVisible = true
            tvCustomerBalanceTitle.isVisible = true
            customerMemberResponse = null
            btnSearch.setEnableWithAlpha(false)
            checkBtn()
        }
    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight =
                (displayMetrics.heightPixels * 0.9).toInt()
            val screenWidth =
                (displayMetrics.widthPixels * 0.9).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)

        }
    }

//    override fun onDismiss(dialog: DialogInterface) {
//        super.onDismiss(dialog)
//    }


    override fun onCcpDialogOpen(dialog: Dialog?) {
        binding?.apply {
            countryCodeHolder.setCcpClickable(false)
        }
    }

    override fun onCcpDialogDismiss(dialogInterface: DialogInterface?) {
        binding?.apply {
            edtPhoneNumber.isFocusable = true
            countryCodeHolder.setCcpClickable(true)
        }
    }

    override fun onCcpDialogCancel(dialogInterface: DialogInterface?) {
        binding?.apply {
            countryCodeHolder.setCcpClickable(true)
        }
    }

//    private fun showPopupWindowLogout(anchorView: View) {
//        activity?.hideKeyboard()
//
//        val popupView = PopupOfflineChannelBinding.inflate(layoutInflater)
//        val popupWindow = PopupWindow(
//            popupView.root,
//            DisplayUtils.dp2px(requireContext(), 260f),
//            ViewGroup.LayoutParams.WRAP_CONTENT,
//            true
//        )
//        binding?.ivArrow?.animate()?.rotation(180f)?.setDuration(200)
//        PopupWindowHelper.addPopupWindow(popupWindow)
//        popupWindow.animationStyle = R.style.PopupAnimation
//        popupWindow.showAsDropDown(anchorView)
//        popupWindow.setOnDismissListener {
//            binding?.ivArrow?.animate()?.rotation(0f)?.setDuration(200)
//            PopupWindowHelper.deletePopupWindow(popupWindow)
//        }
//        val adapter = PopUpOfflineChannelsAdapter(ArrayList(offlineChannelList)) {
//            offlineChannelModel = it
//            updateOfflineChannel()
//            popupWindow?.dismiss()
//        }
//        popupView.rvList.adapter = adapter
//        adapter.setSelectIndex(offlineChannelList?.indexOf(offlineChannelModel) ?: -1)
//    }

}