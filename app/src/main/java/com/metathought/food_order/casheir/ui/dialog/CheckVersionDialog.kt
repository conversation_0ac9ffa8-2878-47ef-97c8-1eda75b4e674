package com.metathought.food_order.casheir.ui.dialog

import android.app.Activity
import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Build
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper

import com.metathought.food_order.casheir.data.model.base.response_model.version.VersionCheckResponse
import com.metathought.food_order.casheir.databinding.DialogCheckVersionBinding
import com.metathought.food_order.casheir.helper.FolderHelper
import com.metathought.food_order.casheir.network.download.DownloadListener
import com.metathought.food_order.casheir.network.download.DownloadManager
import com.metathought.food_order.casheir.utils.ApplicationUtils
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File


class CheckVersionDialog : DialogFragment() {

    private var binding: DialogCheckVersionBinding? = null

    private var checkData: VersionCheckResponse? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogCheckVersionBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * PERCENT_HEIGHT).toInt()
            val screenWidth = (displayMetrics.widthPixels * PERCENT_WIDTH).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        initData()
        initListener()
    }

//    override fun onResume() {
//        super.onResume()
////        context?.let {
////            val displayMetrics = getDisplayMetrics(it)
////            val screenHeight = (displayMetrics.heightPixels * PERCENT_HEIGHT).toInt()
////            val screenWidth = (displayMetrics.widthPixels * PERCENT_WIDTH).toInt()
////            dialog?.window?.setLayout(screenWidth, screenHeight)
////        }
//    }

    private var fileName: String? = null

    private var dir: String? = null

    private fun initData() {

        dir = FolderHelper.getTempFileDir().absolutePath
        checkData = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arguments?.getParcelable(VERSION_DATA, VersionCheckResponse::class.java)!!
        } else {
            arguments?.getParcelable(VERSION_DATA)!!
        }

        fileName = "MPOS_Cashier_${checkData?.name ?: ""}.apk"

        binding?.apply {
            tvVersionName.text = checkData?.name
            tvContent.text = checkData?.getUpdateDesc()
        }

        initDownload()
    }

    private fun initListener() {
        binding?.apply {
            ivClose.setOnClickListener {
                dismissAllowingStateLoss()
            }

            tvNoCue.setOnClickListener {
                lifecycleScope.launch {
                    Timber.e("保存不再提示版本  ${checkData?.name ?: ""}")
                    PreferenceDataStoreHelper.getInstance().apply {
                        this.putPreference(
                            PreferenceDataStoreConstants.DATA_STORE_KEY_IGNORE_VERSION,
                            checkData?.name ?: ""
                        )
                    }
                }
                dismissAllowingStateLoss()
            }

            btnUpdate.setOnClickListener {
                llProgress.isVisible = true
                btnUpdate.isVisible = false
                ivClose.isVisible = false
                startDownApk()
            }

        }
    }

    private fun downloadOver() {
        Timber.e("下载完成")
        binding?.apply {
            llProgress.isVisible = false
            btnUpdate.isVisible = true
            btnUpdate.text = getString(R.string.install)
            ivClose.isVisible = checkData?.type != 2
            tvNoCue.isVisible = checkData?.type != 2
            dialog?.setCancelable(tvNoCue.isVisible)
        }
    }

    private fun initDownload() {
        binding?.apply {
            llProgress.isVisible = false
            btnUpdate.isVisible = true
            btnUpdate.text = getString(R.string.update_now)
            ivClose.isVisible = checkData?.type != 2
            tvNoCue.isVisible = checkData?.type != 2
        }
        isDownloadOver()
    }

    private fun isDownloadOver(): Boolean {

        if (File(dir, fileName).exists()) {
            (context as? Activity)?.apply {
                downloadOver()
            }
            return true
        }
        return false
    }

    private fun updateDownloading() {
        binding?.apply {
            tvProgress.text =
                "${getString(R.string.downloading)}(0%)"
            tvNoCue.isVisible = false
        }
    }

    private fun startDownApk() {

        if (isDownloadOver()) {
            (context as? Activity)?.apply {
                ApplicationUtils.installApk(this, File(dir, fileName))
            }
            return
        }

        updateDownloading()

        DownloadManager.getInstance()
            .download(
                TAG,
                checkData?.downloadUrl ?: "",
                fileName!!,
                dir ?: "",
                object : DownloadListener {
                    override fun onProgress(progress: Long, max: Long) {
                        (context as? Activity)?.apply {
                            Timber.e("progress $progress")
                            runOnUiThread {
                                binding?.apply {
                                    val currentProgress =
                                        ((progress * 1.0f / max).times(100)).toInt()
                                    tvProgress.text =
                                        "${getString(R.string.downloading)}(${currentProgress}%)"
                                    progressBar.progress = currentProgress

                                }
                            }
                        }
                    }

                    override fun onSuccess(localPath: String) {
                        val file = File(dir, fileName)
                        val renameToRes = File(localPath).renameTo(file)
                        // Log.e(TAG, "onSuccess-renameToRes: $renameToRes")
                        (context as? Activity)?.apply {
                            runOnUiThread {
                                if (file.exists()) {
                                    ApplicationUtils.installApk(this, file)
                                }
                                downloadOver()
                            }
                        }
                    }

                    override fun onFail(errorInfo: String) {
                        (context as? Activity)?.apply {
                            runOnUiThread {
                                dismiss()
                                initDownload()
                                if (errorInfo.isNotEmpty())
                                    Toast.makeText(context, errorInfo, Toast.LENGTH_LONG).show()
                            }
                        }
                    }
                })
    }


    companion object {
        private const val TAG = "CheckVersionDialog"

        private const val CHECK_VERSION_DIALOG = "CHECK_VERSION_DIALOG"
        private const val VERSION_DATA = "VERSION_DATA"
        private const val PERCENT_HEIGHT = 0.6
        private const val PERCENT_WIDTH = 0.4
        fun showDialog(
            fragmentManager: FragmentManager,
            versionData: VersionCheckResponse
        ) {
            var fragment = fragmentManager.findFragmentByTag(CHECK_VERSION_DIALOG)
            if (fragment != null) return
            fragment = newInstance(versionData)
            fragment.show(fragmentManager, CHECK_VERSION_DIALOG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            var fragment =
                fragmentManager.findFragmentByTag(CHECK_VERSION_DIALOG) as? CheckVersionDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(versionData: VersionCheckResponse): CheckVersionDialog {
            val args = Bundle()

            val fragment = CheckVersionDialog()
            args.putParcelable(VERSION_DATA, versionData)
            fragment.arguments = args
            return fragment
        }
    }


    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }
}
