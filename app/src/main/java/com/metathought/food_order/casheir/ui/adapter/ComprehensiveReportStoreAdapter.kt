package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.MasterReportData
import com.metathought.food_order.casheir.databinding.ItemComprehensiveReportStoreBinding

/**
 * 综合报表门店名称适配器
 */
class ComprehensiveReportStoreAdapter(
    private var dataList: ArrayList<MasterReportData>
) : RecyclerView.Adapter<ComprehensiveReportStoreAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemComprehensiveReportStoreBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(dataList[position])
    }

    override fun getItemCount(): Int = dataList.size

    fun replaceData(newData: ArrayList<MasterReportData>) {
        dataList.clear()
        dataList.addAll(newData)
        notifyDataSetChanged()
    }

    class ViewHolder(private val binding: ItemComprehensiveReportStoreBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(data: MasterReportData) {
            binding.tvStoreName.text = data.storeName
        }
    }
}
