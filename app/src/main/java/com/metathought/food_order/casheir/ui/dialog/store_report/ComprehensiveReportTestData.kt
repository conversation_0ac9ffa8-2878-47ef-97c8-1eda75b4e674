package com.metathought.food_order.casheir.ui.dialog.store_report

import com.metathought.food_order.casheir.data.model.base.response_model.ComprehensiveReportData
import com.metathought.food_order.casheir.data.model.base.response_model.Header
import com.metathought.food_order.casheir.data.model.base.response_model.MasterReportData

/**
 * 综合报表测试数据
 */
object ComprehensiveReportTestData {
    
    fun createSampleData(): ComprehensiveReportData {
        val headerList = listOf(
            Header("totalAmount", "总金额"),
            Header("dineInAmount", "堂食金额"),
            Header("takeawayAmount", "外带金额"),
            Header("deliveryAmount", "外卖金额"),
            Header("orderCount", "订单数量"),
            Header("avgOrderAmount", "平均订单金额")
        )
        
        val masterReportDataList = listOf(
            MasterReportData(
                storeId = "1",
                storeName = "主店",
                totalAmount = 15000.50,
                dineInAmount = 8000.25,
                takeawayAmount = 4000.15,
                reservationTotalAmount = 1500.10,
                deliveryAmount = 1500.00,
                itemNetSalesWithoutTax = 14000.00,
                dineInNetSales = 7500.00,
                takeawayNetSales = 3800.00,
                reservationTotalAmountExcludesVat = 1400.00,
                deliveryNetSales = 1300.00,
                itemNetSalesWithTax = 15000.50,
                dineInTotalAmountIncludesVat = 8000.25,
                takeawayTotalAmountIncludesVat = 4000.15,
                reservationTotalAmountIncludesVat = 1500.10,
                deliveryTotalAmountIncludesVat = 1500.00,
                totalDiscounts = 500.00,
                itemDiscounts = 300.00,
                orderDiscounts = 200.00,
                totalAmountOfCoupons = 100.00,
                marketingManagement = 50.00,
                totalCommission = 150.00,
                totalTaxes = 1000.50,
                serviceFee = 200.00,
                packagingFee = 100.00,
                totalVoids = 50.00,
                totalPax = 120.0,
                orderCount = 85.0,
                avgOrderAmount = 176.48,
                avgCustomerAmount = 125.00
            ),
            MasterReportData(
                storeId = "2",
                storeName = "分店A",
                totalAmount = 12000.75,
                dineInAmount = 6000.50,
                takeawayAmount = 3500.25,
                reservationTotalAmount = 1200.00,
                deliveryAmount = 1300.00,
                itemNetSalesWithoutTax = 11200.00,
                dineInNetSales = 5600.00,
                takeawayNetSales = 3300.00,
                reservationTotalAmountExcludesVat = 1100.00,
                deliveryNetSales = 1200.00,
                itemNetSalesWithTax = 12000.75,
                dineInTotalAmountIncludesVat = 6000.50,
                takeawayTotalAmountIncludesVat = 3500.25,
                reservationTotalAmountIncludesVat = 1200.00,
                deliveryTotalAmountIncludesVat = 1300.00,
                totalDiscounts = 400.00,
                itemDiscounts = 250.00,
                orderDiscounts = 150.00,
                totalAmountOfCoupons = 80.00,
                marketingManagement = 40.00,
                totalCommission = 120.00,
                totalTaxes = 800.75,
                serviceFee = 160.00,
                packagingFee = 80.00,
                totalVoids = 30.00,
                totalPax = 95.0,
                orderCount = 68.0,
                avgOrderAmount = 176.48,
                avgCustomerAmount = 126.32
            )
        )
        
        return ComprehensiveReportData(
            masterReportDataList = masterReportDataList,
            headerList = headerList
        )
    }
}
