package com.metathought.food_order.casheir.data.model.base.response_model.order

import android.content.Context
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.PricingMethodEnum
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderMealSetGood
import com.metathought.food_order.casheir.extension.isInt
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import timber.log.Timber
import java.io.Serializable
import java.math.BigDecimal
import java.util.Objects

data class Goods(
//    val addBonus: Int,
    @SerializedName("allFirstPingYin")
    val allFirstPingYin: String? = null,
    @SerializedName("costPrice")
    val costPrice: Int? = null,
    @SerializedName("createTime")
    val createTime: String? = null,
    @SerializedName("diningStyle")
    var diningStyle: Int? = null,
    @SerializedName("feeds")
    val feeds: ArrayList<Feed>? = null,
    @SerializedName("firstPingYin")
    val firstPingYin: String? = null,
    @SerializedName("goodReserveId")
    val goodReserveId: String? = null,
    @SerializedName("groupIds")
    val groupIds: String? = null,
    @SerializedName("id")
    val id: String,
    @SerializedName("infoPicUrls")
    val infoPicUrls: String? = null,
    @SerializedName("kitchenPrinter")
    val kitchenPrinter: Int? = null,
    @SerializedName("labels")
    val labels: String? = null,
    @SerializedName("mainStoreId")
    val mainStoreId: String? = null,
    @SerializedName("markerPrice")
    val markerPrice: Int? = null,
    @SerializedName("name")
    var name: String? = null,
    @SerializedName("note")
    val note: String? = null,
    @SerializedName("number")
    val number: String? = null,
    @SerializedName("otherLanName")
    val otherLanName: String? = null,
    @SerializedName("outsideStatus")
    val outsideStatus: Int? = null,
    @SerializedName("picUrl")
    var picUrl: String? = null,
    @SerializedName("plan")
    val plan: Boolean? = null,
    @SerializedName("points")
    val points: Int? = null,
    @SerializedName("prepaymentPercentage")
    val prepaymentPercentage: Int? = null,
    @SerializedName("remark")
    val remark: String? = null,
    @SerializedName("restrictNum")
    var restrictNum: Int? = null,
    @SerializedName("saasStoreId")
    val saasStoreId: String? = null,
    @SerializedName("sellCount")
    var sellCount: Int? = null,
    @SerializedName("sellPrice")
    var sellPrice: Long? = null,

    @SerializedName("packingFee")
    val packingFee: Int? = null,
//    @SerializedName("withPackingFee")
//    val withPackingFee: Boolean? = null,


    @SerializedName("vipPrice")
    var vipPrice: Long? = null,
    @SerializedName("finalVipPrice")
    val finalVipPrice: Long? = null,
    @SerializedName("finalVipVatPrice")
    val finalVipVatPrice: Long? = null,
    @SerializedName("withVipPrice")
    val withVipPrice: Boolean? = null,

    @SerializedName("tags")
    val tags: List<GoodsTag>? = null,

    //true: There is no service charge for this dish
    // false: Need to calculate service fee
    @SerializedName("vatWhitelisting")
    var vatWhitelisting: Boolean? = null,
    @SerializedName("vatPercentage")
    var vatPercentage: Int? = null,

    //是否堂食服务费白名单
    @SerializedName("serviceChargeWhitelisting")
    var serviceChargeWhitelisting: Boolean? = null,
    //堂食服务费率
    @SerializedName("serviceChargePercentage")
    var serviceChargePercentage: Int? = null,
//    //vip堂食服务费
//    @SerializedName("finalVipServiceCharge")
//    val finalVipServiceCharge: Long?,


    @SerializedName("status")
    val status: Int? = null,
    @SerializedName("storeId")
    val storeId: String? = null,
    @SerializedName("updateTime")
    val updateTime: String? = null,
    @SerializedName("withSpecifications")
    val withSpecifications: Boolean? = null,
    @SerializedName("cartsId")
    var cartsId: String? = null,

    @SerializedName("discountRate")
    val discountRate: Double? = null,
    /**
     * 计价方式
     * WHOLE_UNIT(0, "整份计费", ""),
     * PER_KILOGRAM(1, "每公斤", "KG"),
     * PER_POUND(2, "每磅", "LB"),
     * PER_LITER(3, "每升", "L"),
     * PER_OUNCE(4, "每盎司", "OZ"),
     * PER_GALLON(5, "每加仑", "GAL"),
     * PER_GRAM(6, "每克", "G");
     * PER_GRAM(7, "时价菜", ""); 7 的时候是时价菜 ，价格另外设置
     */
    @SerializedName("pricingMethod")
    var pricingMethod: Int? = null,

    /**
     * 重量  订单的菜品model 转成购物车菜品model 的时候赋值
     */
    @SerializedName("weight")
    var weight: Double? = null,

    //TODO 2.16.10 新增字段
    @SerializedName("uuid")
    var uuid: String?,
    /**
     * 是否称重完成true是false否
     */
    @SerializedName("weighingCompleted")
    var weighingCompleted: Boolean? = false,

    /**
     * 是否时价菜定价定价完成
     */
    @SerializedName("pricingCompleted")
    var pricingCompleted: Boolean? = false,

    /**
     * 是否已经定价  有确定的价格
     */
    @SerializedName("isProcessed")
    var isProcessed: Boolean? = true,

    /**
     * 是否时价菜
     */
    @SerializedName("currentPrice")
    var currentPrice: Boolean? = true,

//    @Transient
//    @SerializedName ("goodsHeader")
//    override val header: Boolean? =false,
    @Transient
    var groupID: String? = "",
    @Transient
    var groupName: String? = "",

    @Transient
    var totalPrice: Long? = 0L,
    @Transient
    var totalCount: Int? = 0,
    @Transient
    var position: Int? = 0,

    @Transient
    var feedStr: String? = "",

    /**
     * 当前商品 id +小料+规格 hash值
     */
    @Transient
    var goodHashCode: String? = "",

    /**
     * 单个商品折扣价 ，如果有这个价格就把原价展示为划线价
     */
    @SerializedName("discountPrice")
    var discountPrice: Long? = null,

    /**
     * 是否已售罄
     */
    @SerializedName("soldOut")
    var soldOut: Boolean? = null,

    /**
     * 商品参与优惠活动标签
     */
    @SerializedName("activityLabels")
    var activityLabels: List<ActivityLabel>? = null,

    //商品类型:0-普通商品， 1-临时商品"
    @SerializedName("goodsType")
    var goodsType: Int? = null,

    /**
     * 是否不参与折扣商品白名单，true：不参与，false：参与，仅限于整单减免和优惠券减免 也不参与优惠活动
     */
    @SerializedName("discountItemWhitelisting")
    var discountItemWhitelisting: Boolean? = null,

    /**
     * 佣金比例
     */
    @SerializedName("commissionPercentage")
    var commissionPercentage: BigDecimal? = null,

    //菜品类型: 0-单品，1-固定套餐，2-可选套餐
    @SerializedName("type")
    var type: Int? = null,

    /**
     * 参与满赠活动列表
     */
    @SerializedName("giftLabels")
    val giftLabels: List<GiftLabel>? = null,

    /**
     * 套餐内容
     */
    @SerializedName("mealSetInfo")
    var mealSetInfo: MealSetData? = null,

    /**
     * 打包费显示，0:独立显示，1:计入商品价格
     */
    @SerializedName("packingFeeDisplay")
    var packingFeeDisplay: Boolean? = null,

    //=======================================以下本地的哪里要用哪里传进来
    //堂食服务费
    @Transient
    var totalServiceCharge: Long? = null,

    //折扣堂食服务费
    @Transient
    var totalDiscountServiceCharge: Long? = null,

    //Vip堂食服务费
    @Transient
    var totalVipServiceCharge: Long? = null,

    //佣金
    @Transient
    var totalCommission: Long? = null,

    /**
     * 已选套餐内容
     */
    @SerializedName("orderMealSetGoodsDTOList")
    var orderMealSetGoodsDTOList: List<OrderMealSetGood>? = null,

    /**
     * 单品减免信息
     */
    @SerializedName("singleItemDiscount")
    val singleItemDiscount: SingleItemDiscount? = null,

    //该商品 参与了优惠活动的数量，展示服务费的时候才有用到
    @SerializedName("discountActivityPair")
    var discountActivityPair: Pair<Int, Long>? = null,
    @SerializedName("discountActivityVipPair")
    var discountActivityVipPair: Pair<Int, Long>? = null,

    //加购的时候这个置为true  是否加购时候的菜品
    var isOrderMoreGoods: Boolean = false,

    //自定义菜品备注
    var goodNote: String? = ""
//================================================

) : BaseGoods(), Serializable {

    override fun hashCode(): Int {
        return Objects.hash(id, diningStyle)
    }

    override fun equals(other: Any?): Boolean {
        if (other === this) return true
        if (other !is Goods) return false
        return other.id == this.id && other.diningStyle == this.diningStyle
    }

    /**
     * 打包费是否计入 true是计入打包费
     */
    fun isPackingFeeDisplay(): Boolean {
        Timber.e("packingFeeDisplay: $packingFeeDisplay")
        if (packingFeeDisplay == null) {
            return false
        }
        return packingFeeDisplay == true
    }

    /**
     * 获取参与计算的SellPrice
     */
    fun getCalculateSellPrice(): Long? {
        if (isPackingFeeDisplay()) {
            return (sellPrice ?: 0L).plus(packingFee ?: 0)
        }
        return sellPrice
    }

    /**
     * 获取参与计算的VipPrice
     */
    fun getCalculateVipPrice(): Long? {
        if (vipPrice == null) {
            return null
        }
        if (isPackingFeeDisplay()) {
            return (vipPrice ?: 0L).plus(packingFee ?: 0)
        }
        return vipPrice
    }

    /**
     * 获取参与计算的discountPrice
     */
    fun getCalculateDiscountPrice(): Long? {
        if (!isHasDiscountPrice()) {
            return getCalculateSellPrice()
        }
        Timber.e("折扣价  ${isPackingFeeDisplay()}")
        if (isPackingFeeDisplay()) {
            return (discountPrice ?: 0L).plus(packingFee ?: 0)
        }
        return discountPrice
    }

    /**
     * 获取参与计算的打包费
     */
    fun getCalculatePackingFee(): Int {
        if (isPackingFeeDisplay()) {
            return 0
        }
        return packingFee ?: 0
    }


    fun isShowVipPrice(): Boolean {
        if (vipPrice == null) {
            return false
        }
        return (vipPrice ?: 0) >= 0 && (vipPrice ?: 0) != (sellPrice
            ?: 0L)   //&& withVipPrice == true
    }

    /**
     * 是否有折扣价
     *
     * @return
     */
    fun isHasDiscountPrice(): Boolean {
        return discountPrice != null
    }

    /**
     * 是否时价菜
     *
     * @return
     */
    fun isTimePriceGood(): Boolean {
        if (currentPrice == null) {
            return false
        }
        return currentPrice == true
    }

    /**
     * 是否已经设置时价菜价格
     *
     * @return
     */
    fun isHasCompletePricing(): Boolean {
        return pricingCompleted ?: false
    }

    /**
     *  设置是否已经设时价菜价格
     *
     * @return
     */
    fun setPriceCompletedFlag(flag: Boolean) {
        pricingCompleted = flag
    }

    /**
     * 是否称重菜
     *
     * @return
     */
    fun isToBeWeighed(): Boolean {
        if (!orderMealSetGoodsDTOList.isNullOrEmpty()) {
            val isToBeWeighed = orderMealSetGoodsDTOList?.firstOrNull { it.isToBeWeighed() }
            if (isToBeWeighed != null) {
                return true
            }
            return false
        } else if (mealSetInfo != null) {
            val mealSetGroupList = mealSetInfo?.mealSetGroupList ?: return false
            for (mealSetGroup in mealSetGroupList) {
                val isToBeWeighed =
                    mealSetGroup.mealSetGoodsList?.firstOrNull { it.isToBeWeighed() && it.selectItems.size > 0 }
                Timber.e("isToBeWeighed  ${isToBeWeighed}")
                if (isToBeWeighed != null) {
                    return true
                }
            }
            return false
        } else {
            return (pricingMethod ?: 0) > PricingMethodEnum.WHOLE_UNIT.id
        }
    }

    //是否包含称重子商品
    fun isContainWeightGoods(): Boolean {
        if (mealSetInfo != null) {
            val mealSetGroupList = mealSetInfo?.mealSetGroupList ?: return false
            for (mealSetGroup in mealSetGroupList) {
                val isToBeWeighed =
                    mealSetGroup.mealSetGoodsList?.firstOrNull { it.isToBeWeighed() }
                if (isToBeWeighed != null) {
                    return true
                }
            }
            return false
        }
        return true
    }

    /**
     * 是否已经设置重量
     *
     * @return
     */
    fun isHasCompleteWeight(): Boolean {
        if (!orderMealSetGoodsDTOList.isNullOrEmpty()) {
            val goods = orderMealSetGoodsDTOList?.firstOrNull { !it.isHasCompleteWeight() }
            return goods == null
        } else if (mealSetInfo != null) {
            val goods = mealSetInfo?.mealSetGroupList?.firstOrNull { !it.isHasCompleteWeight() }
            return goods == null
        } else {
            if (weighingCompleted == null) {
                return ((weight ?: 0.0) > 0.0)
            }
            return weighingCompleted ?: false
        }
    }

    /**
     *  设置是否已经设置重量
     *
     * @return
     */
    fun setWeighingCompletedFlag(flag: Boolean) {
        weighingCompleted = flag
    }

    //获取称重后 重量加单位  描述
    fun getWeightStr(): String {
        if (weight?.isInt() == true) {
            return "${weight?.toInt()}${getWeightUnit()}"
        }
        return "${weight ?: 0}${getWeightUnit()}"
    }

    fun getWeightUnit(): String {
        return PricingMethodEnum.entries.find {
            it.id == (pricingMethod ?: 0)
        }?.unit ?: ""
    }

//    fun getCurrentPriceWithOutSingleDiscount(): Long {
//        if (isHasDiscountPrice()) {
//            return (discountPrice ?: 0)
//        }
//        return (sellPrice ?: 0)
//    }

//    /**
//     * 是否需要称重
//     *
//     * @return
//     */
//    fun isHasNeedWeight(): Boolean {
//        val isNeedWeight = isToBeWeighed() && !isHasCompleteWeight()
//        return isNeedWeight
//    }

    /**
     * 是否已定价
     *
     * @return
     */
    fun isHasProcessed(): Boolean {
        isProcessed = true
        if (isTimePriceGood()) {
            /**
             * 如果过是时价菜
             */
            //如果时价菜还未设置价格
            if (!isHasCompletePricing()) {
                isProcessed = false
            }
            //如果称重时价菜还未设置重量
            if (isToBeWeighed() && !isHasCompleteWeight()) {
                isProcessed = false
            }
        } else if (isToBeWeighed()) {
            //如果称重菜 还未设置重量
            if (!isHasCompleteWeight()) {
                isProcessed = false
            }
        } else {

        }
        return isProcessed == true
    }


    /**
     * Get show price
     *
     * @param context
     * @return
     */
    fun getShowPrice(context: Context): String {
        if (isTimePriceGood()) {
            if (isHasCompletePricing() && isToBeWeighed() && !isHasCompleteWeight()) {
                return context.getString(R.string.to_be_weighed)
            }

            return context.getString(R.string.time_price)
        }
        if (isToBeWeighed() && !isHasCompleteWeight()) {
            return context.getString(R.string.to_be_weighed)
        }
        //下面不会走到了
        Timber.e("getShowPrice :  $totalPrice")
        return "${totalPrice?.priceFormatTwoDigitZero2()}"
    }


    fun getVatPercentage(): Int {
        return vatPercentage
            ?: MainDashboardFragment.CURRENT_USER?.getCurrentVatPercentage() ?: 0
    }

    fun getServiceChargePercentage(): Int {
        return serviceChargePercentage
            ?: MainDashboardFragment.CURRENT_USER?.getCurrentServiceChargePercentage() ?: 0
    }

    /**
     * 佣金比例
     *
     * @return
     */
    fun getCommissionPercent(): BigDecimal {
        return (commissionPercentage ?: BigDecimal.ZERO)
    }

    /**
     * 是否售罄
     *
     * @return
     */
    fun isSoldOut(): Boolean {
        return soldOut ?: false
    }

    /**
     * 不参与整单减免 优惠券 优惠活动
     *
     * @return
     */
    fun isDiscountItemWhitelisting(): Boolean {
        return discountItemWhitelisting ?: false
    }

    /**
     * 是否套餐
     */
    fun isMealSet(): Boolean {
        return isSelectedMealSet() || isFixedMealSet()
    }

    /**
     * 是否可选套餐
     */
    fun isSelectedMealSet(): Boolean {
        return type == 2
    }

    /**
     * 是否固定套餐
     */
    fun isFixedMealSet(): Boolean {
        return type == 1
    }
}