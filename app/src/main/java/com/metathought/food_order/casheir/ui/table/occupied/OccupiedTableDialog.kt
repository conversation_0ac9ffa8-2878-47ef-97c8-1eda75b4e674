package com.metathought.food_order.casheir.ui.table.occupied

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.google.gson.Gson
import com.metathought.food_order.casheir.data.model.base.response_model.order.Customer
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.databinding.DialogOccupiedBinding


class OccupiedTableDialog : DialogFragment() {
    private var binding: DialogOccupiedBinding? = null
    private var clearTableButtonListener: (() -> Unit)? = null
    private var viewOrderedButtonListener: (() -> Unit)? = null
    private var printerButtonListener: (() -> Unit)? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogOccupiedBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
    }

    override fun onResume() {
        super.onResume()
//        context?.let {
//            val displayMetrics = getDisplayMetrics(it)
//            val screenHeight = (displayMetrics.heightPixels * PERCENT_70).toInt()
//            val screenWidth = (displayMetrics.widthPixels * PERCENT_70).toInt()
//            dialog?.window?.setLayout(screenWidth, screenHeight)
//        }
    }

    private fun initData() {
        val content = Gson().fromJson(arguments?.getString(CONTENT), TableResponseItem::class.java)
        binding?.apply {
            tvPeople.text =  "0/${content.maxPeopleCount}"
            val customer =
                Gson().fromJson<Customer>(content.customerJson, Customer::class.java)
            customer?.let {
                tvPeople.text =  "${customer.diningNumber}/${content.maxPeopleCount}"
            }

            tvTableID.text = content.name
        }
    }

    private fun initListener() {
        binding?.apply {
            btnClose.setOnClickListener() {
                dismissAllowingStateLoss()
            }
            btnClearTable.setOnClickListener {
                clearTableButtonListener?.invoke()
                dismissAllowingStateLoss()
            }
            btnViewOrder.setOnClickListener {
                viewOrderedButtonListener?.invoke()
                dismissAllowingStateLoss()
            }
            cardPrinter.setOnClickListener {
                //打印临时桌码 Print temporary table code
                printerButtonListener?.invoke()
                dismissAllowingStateLoss()
            }
        }
    }

    companion object {
        private const val RESERVE_DIALOG = "RESERVE_DIALOG"
        private const val CONTENT = "CONTENT"


        // start - viettran1 - AM -133 - 18/07/2023
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        // end - viettran1 - AM -133 - 18/07/2023
        fun showDialog(
            fragmentManager: FragmentManager,
            content: String? = null,
            clearTableButtonListener: (() -> Unit),
            viewOrderedButtonListener: (() -> Unit),
            printerButtonListener: (() -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(RESERVE_DIALOG)
            if (fragment != null) return
            fragment = newInstance(iOrderListener = clearTableButtonListener, iCancelListener = viewOrderedButtonListener, iPrinterListener =printerButtonListener, content = content)
            fragment.show(fragmentManager, RESERVE_DIALOG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            var fragment =
                fragmentManager.findFragmentByTag(RESERVE_DIALOG) as? OccupiedTableDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            iCancelListener: (() -> Unit),
            iOrderListener: (() -> Unit),
            iPrinterListener: (() -> Unit),
            content: String? = null
        ): OccupiedTableDialog {
            val args = Bundle()
            args.putString(CONTENT, content)
            val fragment = OccupiedTableDialog()
            fragment.clearTableButtonListener = iOrderListener
            fragment.viewOrderedButtonListener = iCancelListener
            fragment.printerButtonListener = iPrinterListener
            fragment.arguments = args
            return fragment
        }
    }


    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }
}
