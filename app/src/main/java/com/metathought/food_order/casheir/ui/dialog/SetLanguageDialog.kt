package com.metathought.food_order.casheir.ui.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.databinding.DialogSetLanguageBinding
import com.metathought.food_order.casheir.utils.SingleClickUtils
import java.util.Locale

/**
 * 设置语言
 * **/

class SetLanguageDialog : BaseDialogFragment() {

    private var binding: DialogSetLanguageBinding? = null
    private var callBack: ((locale: Locale) -> Unit)? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogSetLanguageBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)

        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)

        initData()
        initListener()
    }


    private fun initData() {

    }

    private fun initListener() {
        binding?.apply {

            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

            when (Locale.getDefault()) {
                Locale.CHINESE -> {
                    tvChinese.isSelected = true
                }

                Locale.ENGLISH -> {
                    tvEnglish.isSelected = true
                }

                MyApplication.LOCALE_KHMER -> {
                    tvKm.isSelected = true
                }

                else -> {
                    tvChinese.isSelected = true
                }
            }
            tvChinese.setOnClickListener {
                SingleClickUtils.isFastDoubleClick(600) {
                    dismissAllowingStateLoss()
                    callBack?.invoke(Locale.CHINESE)
                }
            }
            tvKm.setOnClickListener {
                SingleClickUtils.isFastDoubleClick(600) {
                    dismissAllowingStateLoss()
                    callBack?.invoke(MyApplication.LOCALE_KHMER)
                }
            }
            tvEnglish.setOnClickListener {
                SingleClickUtils.isFastDoubleClick(600) {
                    dismissAllowingStateLoss()
                    callBack?.invoke(Locale.ENGLISH)
                }
            }
        }
    }


    companion object {
        private const val TAG = "SetLanguageDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            callBack: (locale: Locale) -> Unit
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(callBack)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? SetLanguageDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            callBack: (locale: Locale) -> Unit
        ): SetLanguageDialog {
            val args = Bundle()
            val fragment = SetLanguageDialog()
            fragment.arguments = args
            fragment.callBack = callBack
            return fragment
        }
    }

}
