package com.metathought.food_order.casheir.ui.widget

import android.content.Context
import android.text.InputType
import android.util.AttributeSet
import android.view.ActionMode
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.widget.EditText
import android.widget.LinearLayout
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.databinding.ViewCustomerQuickInputBinding
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.PreferenceHelper
import kotlinx.coroutines.launch
import timber.log.Timber


/**
 * 自定义快捷输入
 *
 * @constructor
 *
 * @param context
 * @param attrs
 */
class CustomerQuickInputView @JvmOverloads constructor(
    baseContext: Context,
    attrs: AttributeSet? = null
) :
    LinearLayout(baseContext, attrs) {

    companion object {
        const val KEYBOARD_CLEAR = "KEYBOARD_CLEAR"
        const val KEYBOARD_DELETE = "KEYBOARD_DELETE"
        const val KEYBOARD_POINT = "."
    }

    private var _binding: ViewCustomerQuickInputBinding? = null

    private var onItemEditClickListener: ((Int, MutableList<Long>) -> Unit)? = null
    private var currentEditText: EditText? = null

    private var initList = mutableListOf<Long>()

    init {

        val typedArray =
            context.obtainStyledAttributes(attrs, R.styleable.CustomerQuickInputView)

        try {

            initView()

            _binding?.apply {

            }
        } catch (e: Exception) {

        } finally {
            // 最后需要回收数组
            typedArray.recycle()
        }
    }

    private lateinit var lifecycleOwner: LifecycleOwner
    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (context is LifecycleOwner) {
            Timber.e("onAttachedToWindow111")
            lifecycleOwner = context as LifecycleOwner
            //可以在这里使用 lifecycleOwner
            lifecycleOwner.lifecycle.addObserver(object :
                androidx.lifecycle.DefaultLifecycleObserver {
                override fun onCreate(owner: LifecycleOwner) {
                    super.onCreate(owner)
                    // 处理 onCreate 事件
                    Timber.e("onAttachedToWindow222")
                }
            })
        }
    }

    private fun initView() {
        _binding =
            ViewCustomerQuickInputBinding.inflate(LayoutInflater.from(context), this, true)
        initList = mutableListOf(100000, 50000, 20000, 15000, 10000, 5000)

        setValue()

        _binding?.apply {

            tvNumber0.setOnClickListener {
                afterInput(initList[0])
            }
            ivEdit0.setOnClickListener {
                editItemValue(0)
            }

            tvNumber1.setOnClickListener {
                afterInput(initList[1])
            }
            ivEdit1.setOnClickListener {
                editItemValue(1)
            }

            tvNumber2.setOnClickListener {
                afterInput(initList[2])
            }
            ivEdit2.setOnClickListener {
                editItemValue(2)
            }

            tvNumber3.setOnClickListener {
                afterInput(initList[3])
            }
            ivEdit3.setOnClickListener {
                editItemValue(3)
            }

            tvNumber4.setOnClickListener {
                afterInput(initList[4])
            }
            ivEdit4.setOnClickListener {
                editItemValue(4)
            }

            tvNumber5.setOnClickListener {
                afterInput(initList[5])
            }
            ivEdit5.setOnClickListener {
                editItemValue(5)
            }

        }
    }

    private var lifecycleOwner2: LifecycleOwner? = null

    fun getCustomValue(lifecycleOwner: LifecycleOwner) {
        try {
            lifecycleOwner2 = lifecycleOwner
            lifecycleOwner2?.lifecycleScope?.launch {
                val list = PreferenceHelper.getCustomerInputKhrList()
                if (list.isNotEmpty()) {
                    initList = list
                    setValue()
                }
            }
        } catch (e: Exception) {

        }
    }

    fun setValue() {
        try {
            _binding?.apply {
                tvNumber0.text = "៛${initList[0].decimalFormatZeroDigit()}"
                tvNumber1.text = "៛${initList[1].decimalFormatZeroDigit()}"
                tvNumber2.text = "៛${initList[2].decimalFormatZeroDigit()}"
                tvNumber3.text = "៛${initList[3].decimalFormatZeroDigit()}"
                tvNumber4.text = "៛${initList[4].decimalFormatZeroDigit()}"
                tvNumber5.text = "៛${initList[5].decimalFormatZeroDigit()}"
            }
        } catch (e: Exception) {

        }
    }

    fun setOnItemEditClickListener(onItemEditClickListener: ((Int, MutableList<Long>) -> Unit)? = null) {
        this.onItemEditClickListener = onItemEditClickListener
    }

    private fun editItemValue(position: Int) {
        onItemEditClickListener?.invoke(position, initList)
//        initList[position] = value
//        lifecycleOwner2?.lifecycleScope?.launch {
//            PreferenceDataStoreHelper.getInstance().apply {
//                putPreference(
//                    PreferenceDataStoreConstants.DATA_STORE_KEY_CUSTOM_KHR_INPUT,
//                    initList.toJson()
//                )
//            }
//        }
//        setValue()
    }


    private fun afterInput(str: Long) {
        if (currentEditText != null) {
            currentEditText?.requestFocus()
            currentEditText?.setText("$str")
        }
    }

    fun setCurrentEditText(editText: EditText?) {
        currentEditText = editText
        currentEditText?.isCursorVisible = false
        currentEditText?.inputType = InputType.TYPE_NULL
        currentEditText?.customSelectionActionModeCallback = object : ActionMode.Callback {
            override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                return false
            }


            override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                return false
            }


            override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                return false
            }


            override fun onDestroyActionMode(mode: ActionMode?) {
            }
        }
        currentEditText?.setTextIsSelectable(false)
        currentEditText?.isLongClickable = false
    }

}