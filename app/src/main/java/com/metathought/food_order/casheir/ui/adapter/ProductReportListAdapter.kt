package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.report.SalesItemOrdersDetail
import com.metathought.food_order.casheir.databinding.ItemProductReportListBinding

/**
 * <AUTHOR>
 * @date 2024/08/28 16:42
 * @description 商品报表预览adapter
 */
class ProductReportListAdapter(
    val list: ArrayList<SalesItemOrdersDetail>,
) : RecyclerView.Adapter<ProductReportListAdapter.ProduceReportItemViewHolder>() {


    inner class ProduceReportItemViewHolder(val binding: ItemProductReportListBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: SalesItemOrdersDetail, position: Int) {
            binding.apply {
                tvGroupName.text = "${resource.itemGroupName ?: ""}"
                tvItem.text = "${resource.itemName ?: ""}"
                tvQty.text = "${resource.itemNum ?: "0"}"
                tvTotal.text = "${resource.itemTotalPrice ?: "0.00"}"
                tvServiceFee.text = "${resource.serviceCharge ?: "0.00"}"
                tvPackageFee.text = "${resource.packingFee ?: "0.00"}"
                tvOrderNum.text = "${resource.orderNum ?: "0"}"
                itemTotalAmount.text = "${resource.amountRatio ?: "0.00"}%"
                vLine.isVisible = position != list.size - 1
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = ProduceReportItemViewHolder(
        ItemProductReportListBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: ProductReportListAdapter.ProduceReportItemViewHolder,
        position: Int
    ) {
        holder.bind(list[position], position)
    }

    fun replaceData(list: List<SalesItemOrdersDetail>) {
        this.list.clear()
        this.list.addAll(list)
        notifyDataSetChanged()
    }

}