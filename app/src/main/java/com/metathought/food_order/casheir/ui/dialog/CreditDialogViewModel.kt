package com.metathought.food_order.casheir.ui.dialog

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class CreditDialogViewModel
@Inject
constructor(val repository: Repository) : ViewModel() {

    private val _uiState = MutableLiveData<UIModel>()
    val uiState get() = _uiState

    private var getMemberAccountJob: Job? = null
    fun getMemberAccount(phoneNumber: String) {
        getMemberAccountJob?.cancel()
        getMemberAccountJob = viewModelScope.launch {
            delay(500)
            try {
                val tableResponse = repository.consumerPayAccount(phoneNumber)
                emitUiState(tableResponse)
            } catch (e: Exception) {
                emitUiState(ApiResponse.Error(e.message))
            }
        }
    }


    fun emitUiState(memberAccountResult: ApiResponse<CustomerMemberResponse?>? = null) {
        val uiModel = UIModel(memberAccountResult)
        _uiState.value = uiModel
    }

    data class UIModel(val memberAccountResult: ApiResponse<CustomerMemberResponse?>?)
}