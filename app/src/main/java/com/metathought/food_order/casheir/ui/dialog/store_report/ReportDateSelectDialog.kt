package com.metathought.food_order.casheir.ui.dialog.store_report

import android.app.DatePickerDialog
import android.content.DialogInterface
import android.icu.util.Calendar
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.DatePicker
import android.widget.EditText
import android.widget.TextView
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_SHOW
import com.metathought.food_order.casheir.constant.ReportDateType
import com.metathought.food_order.casheir.databinding.DialogReportDateSelectBinding
import com.metathought.food_order.casheir.extension.formatDateStr
import com.metathought.food_order.casheir.extension.parseDate
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.ui.common.RangeTimePickerDialog
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.Date


/**
 *<AUTHOR>
 *@time  2025/4/15
 *@desc  报表时间选择
 **/

@AndroidEntryPoint
class ReportDateSelectDialog : BaseDialogFragment() {
    companion object {
        private const val TAG = "ReportDateSelectDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            startTime: Date, endTime: Date, type: Int,
            onConfirmCallBack: ((startTime: Date, endTime: Date, type: Int) -> Unit)? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(startTime, endTime, type, onConfirmCallBack)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? ReportDateSelectDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            startTime: Date, endTime: Date, type: Int,
            onConfirmCallBack: ((startTime: Date, endTime: Date, type: Int) -> Unit)? = null
        ): ReportDateSelectDialog {
            val args = Bundle()
            val fragment = ReportDateSelectDialog()
            fragment.arguments = args
            fragment.onConfirmCallBack = onConfirmCallBack
            fragment.startTime = startTime
            fragment.endTime = endTime
            fragment.type = type
            return fragment
        }
    }

    private var binding: DialogReportDateSelectBinding? = null

    private var onConfirmCallBack: ((startTime: Date, endTime: Date, type: Int) -> Unit)? = null
    private var startTime = Date()
    private var endTime = Date()
    private var type: Int = ReportDateType.YESTERDAY.id

    private var isQuickSelect = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogReportDateSelectBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()

    }

    private fun initData() {
        binding?.apply {
            tvStartTime.text = startTime.formatDateStr(FORMAT_DATE_TIME_SHOW)
            tvEndTime.text = endTime.formatDateStr(FORMAT_DATE_TIME_SHOW)
            isQuickSelect = true
            when (type) {
                ReportDateType.CUSTOMIZE.id -> {
                    isQuickSelect = false
                }

                ReportDateType.TODAY.id -> {
                    tvToday.isSelected = true
                }

                ReportDateType.YESTERDAY.id -> {
                    tvYesterday.isSelected = true
                }

                ReportDateType.LAST_WEEK.id -> {
                    tvLastWeek.isSelected = true
                }

                ReportDateType.LAST_MONTH.id -> {
                    tvLastMonth.isSelected = true
                }
            }

            checkBtnEnable()
        }
    }


    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

            tvToday.setOnClickListener {
                lifecycleScope.launch {
                    isQuickSelect = true
                    resetSelect()
                    tvToday.isSelected = true
                    type = ReportDateType.TODAY.id
                    val storeInfo = PreferenceHelper.getStoreInfo()
                    val startCalendar = Calendar.getInstance()
                    if (storeInfo != null && storeInfo.isSetOpenStartTime()) {
                        Timber.e("有设置营业时间")
                        val startTimeList = storeInfo.getStartTime()
                        //有设置营业时间 营业开始时间- 第二天营业开始时间

                        //然后设置营业开始时间
                        startCalendar.set(Calendar.HOUR_OF_DAY, startTimeList[0])
                        startCalendar.set(Calendar.MINUTE, startTimeList[1])
                        startCalendar.set(Calendar.SECOND, startTimeList[2])
                        startCalendar.set(Calendar.MILLISECOND, 0)
                        startTime = (startCalendar.clone() as Calendar).time

                        // 先将当前日期调整为明天  的营业开始时间为结束时间
                        startCalendar.add(Calendar.DAY_OF_MONTH, +1)
                        startCalendar.set(Calendar.MILLISECOND, 0)
                        endTime = (startCalendar.clone() as Calendar).time

                    } else {
                        Timber.e("没设置营业时间")
                        //今日0点-当前时间
                        val endCalendar = Calendar.getInstance()
                        endTime = (endCalendar.clone() as Calendar).time
                        endCalendar.set(Calendar.HOUR_OF_DAY, 0)
                        endCalendar.set(Calendar.MINUTE, 0)
                        endCalendar.set(Calendar.SECOND, 0)
                        endCalendar.set(Calendar.MILLISECOND, 0)
                        startTime = endCalendar.time
                    }
                    tvStartTime.text = startTime.formatDateStr(FORMAT_DATE_TIME_SHOW)
                    tvEndTime.text = endTime.formatDateStr(FORMAT_DATE_TIME_SHOW)
                    checkBtnEnable()

                }
            }

            tvYesterday.setOnClickListener {
                lifecycleScope.launch {
                    isQuickSelect = true
                    resetSelect()
                    tvYesterday.isSelected = true
                    type = ReportDateType.YESTERDAY.id
                    val storeInfo = PreferenceHelper.getStoreInfo()
                    val startCalendar = Calendar.getInstance()
                    if (storeInfo != null && storeInfo.isSetOpenStartTime()) {
                        val startTimeList = storeInfo.getStartTime()
                        startCalendar.add(Calendar.DAY_OF_MONTH, -1) // 先将当前日期调整为昨天

                        //然后设置营业开始时间
                        startCalendar.set(Calendar.HOUR_OF_DAY, startTimeList[0])
                        startCalendar.set(Calendar.MINUTE, startTimeList[1])
                        startCalendar.set(Calendar.SECOND, startTimeList[2])
                        startCalendar.set(Calendar.MILLISECOND, 0)
                        startTime = (startCalendar.clone() as Calendar).time

                        // 先将当前日期调整为今天  的营业开始时间为结束时间
                        startCalendar.add(Calendar.DAY_OF_MONTH, +1)
                        startCalendar.set(Calendar.MILLISECOND, 0)
                        endTime = (startCalendar.clone() as Calendar).time

                    } else {
                        startCalendar.add(Calendar.DAY_OF_MONTH, -1) // 先将当前日期调整为昨天

                        // 获取昨天开始时间（零点整）
                        startCalendar.set(Calendar.HOUR_OF_DAY, 0)
                        startCalendar.set(Calendar.MINUTE, 0)
                        startCalendar.set(Calendar.SECOND, 0)
                        startCalendar.set(Calendar.MILLISECOND, 0)
                        startTime = (startCalendar.clone() as Calendar).time

                        // 获取昨天结束时间（23点59分59秒）
                        startCalendar.set(Calendar.HOUR_OF_DAY, 23)
                        startCalendar.set(Calendar.MINUTE, 59)
                        startCalendar.set(Calendar.SECOND, 59)
                        startCalendar.set(Calendar.MILLISECOND, 999)
                        endTime = startCalendar.time
                    }

                    tvStartTime.text = startTime.formatDateStr(FORMAT_DATE_TIME_SHOW)
                    tvEndTime.text = endTime.formatDateStr(FORMAT_DATE_TIME_SHOW)
                    checkBtnEnable()
                }
            }

            tvLastWeek.setOnClickListener {
                lifecycleScope.launch {
                    isQuickSelect = true
                    resetSelect()
                    tvLastWeek.isSelected = true
                    type = ReportDateType.LAST_WEEK.id
                    val storeInfo = PreferenceHelper.getStoreInfo()
                    if (storeInfo != null && storeInfo.isSetOpenStartTime()) {
                        val startTimeList = storeInfo.getStartTime()
                        // 将日期回退到上周日
                        val calendar = Calendar.getInstance()
                        // 获取当前周的周一
                        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
                        // 减去 7 天得到上周的周一
                        calendar.add(Calendar.DAY_OF_MONTH, -7)
                        val lastMonday = (calendar.clone() as Calendar)
                        // 计算上周的周日
                        calendar.add(Calendar.DAY_OF_MONTH, 6)
                        val lastSunday = (calendar.clone() as Calendar)

                        // 设置上周周一的时间为 00:00:00
                        lastMonday.set(Calendar.HOUR_OF_DAY, startTimeList[0])
                        lastMonday.set(Calendar.MINUTE, startTimeList[1])
                        lastMonday.set(Calendar.SECOND, startTimeList[2])
                        lastMonday.set(Calendar.MILLISECOND, 0)
                        startTime = (lastMonday.clone() as Calendar).time
                        // 设置上周周日的时间为 23:59:59
                        lastSunday.add(Calendar.DAY_OF_MONTH, +1)
                        lastSunday.set(Calendar.HOUR_OF_DAY, startTimeList[0])
                        lastSunday.set(Calendar.MINUTE, startTimeList[1])
                        lastSunday.set(Calendar.SECOND, startTimeList[2])
                        lastSunday.set(Calendar.MILLISECOND, 999)
                        endTime = (lastSunday.clone() as Calendar).time
                    } else {
                        // 将日期回退到上周日
                        val calendar = Calendar.getInstance()
                        // 获取当前周的周一
                        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
                        // 减去 7 天得到上周的周一
                        calendar.add(Calendar.DAY_OF_MONTH, -7)
                        val lastMonday = (calendar.clone() as Calendar)
                        // 计算上周的周日
                        calendar.add(Calendar.DAY_OF_MONTH, 6)
                        val lastSunday = (calendar.clone() as Calendar)

                        // 设置上周周一的时间为 00:00:00
                        lastMonday.set(Calendar.HOUR_OF_DAY, 0)
                        lastMonday.set(Calendar.MINUTE, 0)
                        lastMonday.set(Calendar.SECOND, 0)
                        lastMonday.set(Calendar.MILLISECOND, 0)
                        startTime = (lastMonday.clone() as Calendar).time
                        // 设置上周周日的时间为 23:59:59
                        lastSunday.set(Calendar.HOUR_OF_DAY, 23)
                        lastSunday.set(Calendar.MINUTE, 59)
                        lastSunday.set(Calendar.SECOND, 59)
                        lastSunday.set(Calendar.MILLISECOND, 999)
                        endTime = (lastSunday.clone() as Calendar).time
                    }
                }

                tvStartTime.text = startTime.formatDateStr(FORMAT_DATE_TIME_SHOW)
                tvEndTime.text = endTime.formatDateStr(FORMAT_DATE_TIME_SHOW)
                checkBtnEnable()
            }

            tvLastMonth.setOnClickListener {
                lifecycleScope.launch {
                    isQuickSelect = true
                    resetSelect()
                    tvLastMonth.isSelected = true
                    type = ReportDateType.LAST_MONTH.id
                    val storeInfo = PreferenceHelper.getStoreInfo()
                    if (storeInfo != null && storeInfo.isSetOpenStartTime()) {
                        val startTimeList = storeInfo.getStartTime()
                        val calendar = Calendar.getInstance()
                        // 将日期设置为上个月的第一天
                        calendar.add(Calendar.MONTH, -1)
                        calendar.set(Calendar.DAY_OF_MONTH, 1)
                        calendar.set(Calendar.HOUR_OF_DAY, startTimeList[0])
                        calendar.set(Calendar.MINUTE, startTimeList[1])
                        calendar.set(Calendar.SECOND, startTimeList[2])
                        calendar.set(Calendar.MILLISECOND, 0)
                        startTime = (calendar.clone() as Calendar).time

                        // 将日期设置为上个月的最后一天
                        calendar.set(
                            Calendar.DAY_OF_MONTH,
                            calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
                        )
                        //然后加一天
                        calendar.add(Calendar.DAY_OF_MONTH, +1)
                        calendar.set(Calendar.HOUR_OF_DAY, startTimeList[0])
                        calendar.set(Calendar.MINUTE, startTimeList[1])
                        calendar.set(Calendar.SECOND, startTimeList[2])
                        calendar.set(Calendar.MILLISECOND, 999)
                        endTime = (calendar.clone() as Calendar).time
                    } else {
                        val calendar = Calendar.getInstance()
                        // 将日期设置为上个月的第一天
                        calendar.add(Calendar.MONTH, -1)
                        calendar.set(Calendar.DAY_OF_MONTH, 1)
                        calendar.set(Calendar.HOUR_OF_DAY, 0)
                        calendar.set(Calendar.MINUTE, 0)
                        calendar.set(Calendar.SECOND, 0)
                        calendar.set(Calendar.MILLISECOND, 0)
                        startTime = (calendar.clone() as Calendar).time

                        // 将日期设置为上个月的最后一天
                        calendar.set(
                            Calendar.DAY_OF_MONTH,
                            calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
                        )
                        calendar.set(Calendar.HOUR_OF_DAY, 23)
                        calendar.set(Calendar.MINUTE, 59)
                        calendar.set(Calendar.SECOND, 59)
                        calendar.set(Calendar.MILLISECOND, 999)
                        endTime = (calendar.clone() as Calendar).time
                    }
                }

                tvStartTime.text = startTime.formatDateStr(FORMAT_DATE_TIME_SHOW)
                tvEndTime.text = endTime.formatDateStr(FORMAT_DATE_TIME_SHOW)
                checkBtnEnable()
            }

//            edtType.setOnClickListener {
//                showPopupWindow(textInputLayoutType)
//            }

            edtStartTime.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    showDateTimePicker(edtStartTime, tvStartTime, true)
                }

            }

            edtEndTime.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    showDateTimePicker(edtEndTime, tvEndTime, false)
                }
            }

//            radioPrintType.setOnCheckedChangeListener { radioGroup, i ->
//                showFormatSelect()
//            }

            btnYes.setOnClickListener {
                onConfirmCallBack?.invoke(
                    startTime,
                    endTime,
                    type
                )
                dismissAllowingStateLoss()
            }
        }
    }

    private fun resetSelect() {
        binding?.apply {
            tvToday.isSelected = false
            tvYesterday.isSelected = false
            tvLastWeek.isSelected = false
            tvLastMonth.isSelected = false
            edtStartTime.setText("")
            edtEndTime.setText("")
        }
    }

    private fun checkBtnEnable() {
        binding?.apply {
            if (tvStartTime.text.isNullOrEmpty() || tvEndTime.text.isNullOrEmpty()) {
                btnYes.setEnableWithAlpha(false)
            } else {
                val startTime =
                    tvStartTime.text.toString().parseDate(FORMAT_DATE_TIME_SHOW) ?: Date()
                val endTime =
                    tvEndTime.text.toString().parseDate(FORMAT_DATE_TIME_SHOW) ?: Date()

                if (startTime.time > endTime.time) {
                    //如果选择的开始时间大于结束时间禁用了
                    btnYes.setEnableWithAlpha(false)
                } else {
                    btnYes.setEnableWithAlpha(true)
                }
            }
        }
    }

    private fun showDateTimePicker(editText: EditText, textView: TextView, isStart: Boolean) {
        val calendar: Calendar = Calendar.getInstance()
        val datePickerDialog = context?.let {
            DatePickerDialog(
                it, { _: DatePicker, year: Int, monthOfYear: Int, dayOfMonth: Int ->
                    calendar.set(Calendar.YEAR, year)
                    calendar.set(Calendar.MONTH, monthOfYear)
                    calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth)

                    showTimePicker(calendar, editText, textView, isStart)
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
            )
        }

        datePickerDialog?.datePicker?.maxDate = System.currentTimeMillis()

        datePickerDialog?.setCancelable(false)
        datePickerDialog?.show()

        datePickerDialog?.getButton(DialogInterface.BUTTON_POSITIVE)?.text =
            getString(R.string.confirm2)
        datePickerDialog?.getButton(DialogInterface.BUTTON_NEGATIVE)?.text =
            getString(R.string.cancel)
    }

    private fun showTimePicker(
        calendar: Calendar,
        editText: EditText,
        textView: TextView,
        isStart: Boolean
    ) {
        val currentCalendar = Calendar.getInstance()
        val currentHour = currentCalendar.get(Calendar.HOUR_OF_DAY) // 24-hour format
        val currentMinute = currentCalendar.get(Calendar.MINUTE)
        val timePickerDialog = RangeTimePickerDialog(
            context,
            { view, hourOfDay, minute ->
                if (isQuickSelect) {
                    binding?.apply {
                        tvStartTime.text = ""
                        tvEndTime.text = ""
                    }
                }
                isQuickSelect = false
                type = ReportDateType.CUSTOMIZE.id
                calendar.set(Calendar.HOUR_OF_DAY, hourOfDay)
                calendar.set(Calendar.MINUTE, minute)
                if (isStart) {
                    startTime = (calendar.clone() as Calendar).time
                } else {
                    calendar.set(Calendar.SECOND, 59)
                    endTime = (calendar.clone() as Calendar).time
                }
                editText.setText(calendar.time.formatDateStr(FORMAT_DATE_TIME_SHOW))
                textView.text = calendar.time.formatDateStr(FORMAT_DATE_TIME_SHOW)

                binding?.apply {
                    tvToday.isSelected = false
                    tvYesterday.isSelected = false
                    tvLastWeek.isSelected = false
                    tvLastMonth.isSelected = false
                }
                checkBtnEnable()

            },
            currentHour,
            currentMinute,
            true
        )

        timePickerDialog.setCancelable(false)
        timePickerDialog.show()

        timePickerDialog?.getButton(DialogInterface.BUTTON_POSITIVE)?.text =
            getString(R.string.confirm2)
        timePickerDialog?.getButton(DialogInterface.BUTTON_NEGATIVE)?.text =
            getString(R.string.cancel)
    }


}