package com.metathought.food_order.casheir.network.service

import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.BaseResponse
import com.metathought.food_order.casheir.data.model.base.request_model.CustomerInfoVo
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLog
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLogVo
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.ShiftOrderDiscountInfo
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.ShiftReportPrint
import com.metathought.food_order.casheir.data.model.base.response_model.ComprehensiveReportData
import com.metathought.food_order.casheir.data.model.base.response_model.cart.CartInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.cart.OrderMoreDataResponse
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.RechargeTierPageResponse
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.data.model.base.response_model.customer.SearchCustomerMemberResponse
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.StoreOrderDetail
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home.StoreStatisticResponse
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.order_list.StoreOrderListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.login.LoginUserInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.login.PermissionResponse
import com.metathought.food_order.casheir.data.model.base.response_model.login.StoreInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.ConsumerRechargeInfo
import com.metathought.food_order.casheir.data.model.base.response_model.member.CreditInfo
import com.metathought.food_order.casheir.data.model.base.response_model.member.CreditListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.CreditRecordResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.MemberListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.PaymentStatusResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeDetailResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeQRStatusResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.RepaymentRecordResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.rechargelist.BalanceListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.statistic.MemberStatisticResponse
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelTotalModel
import com.metathought.food_order.casheir.data.model.base.response_model.offline.PaymentChannelResponse
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.PaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.order.ReserveGoodDetail
import com.metathought.food_order.casheir.data.model.base.response_model.order.ReserveGoodListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.ChangeGoodResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.ConsumerResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.DiscountReduceInfo
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.GoodClassificationModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.MergeOrderListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.MultipleOrderResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.NoticeListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.NoticeResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTotalResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTranslateResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.PaymentChannelModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.QuickRemarkModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.ReduceDiscountDetailModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.RepaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.TableOrderStatus
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.UnReadAndPrintResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.UncompletedOrderCountResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.ab_normal.AbNormalOrderResponse
import com.metathought.food_order.casheir.data.model.base.response_model.pending.PendingListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.pending.PendingRecord
import com.metathought.food_order.casheir.data.model.base.response_model.pending.SerialNumberResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterConfigInfo
import com.metathought.food_order.casheir.data.model.base.response_model.report.DailyReportResponse
import com.metathought.food_order.casheir.data.model.base.response_model.report.PaymentMethodReportResponse
import com.metathought.food_order.casheir.data.model.base.response_model.report.ProductReportResponse
import com.metathought.food_order.casheir.data.model.base.response_model.report.SaleReportResponse
import com.metathought.food_order.casheir.data.model.base.response_model.report.UnreadCashierMsgResponse
import com.metathought.food_order.casheir.data.model.base.response_model.store.StoreInfoMultLanguageListRespnose
import com.metathought.food_order.casheir.data.model.base.response_model.table.CreateTempTableCodeResponse
import com.metathought.food_order.casheir.data.model.base.response_model.table.SwitchTableResponse
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponse
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.data.model.base.response_model.upload.UploadTokenResponse
import com.metathought.food_order.casheir.data.model.base.response_model.version.VersionCheckResponse
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

interface ApiService {

    @POST("cash-register/login")
    suspend fun postLogin(@Body body: RequestBody): BaseResponse<UserLoginResponse>

    @GET("cash-register/getPermissions")
    suspend fun getPermissions(@Query("storeUserId") storeUserId: String): BaseResponse<PermissionResponse>

    @GET("cash-register/tables/list")
    suspend fun getTable(@Query("keyword") keyword: String): BaseResponse<TableResponse>

    @PUT("cash-register/tables/freeTable/{tableID}")
    suspend fun freeTable(@Path("tableID") tableID: String): BaseResponse<TableResponseItem>

    // old API get only free table(removed)
//    @GET("cash-register/tables/electFreelist")
//    suspend fun electFreelist(
//        @Query("plan") plan: Int,
//        @Query("diningStyle") diningStyle: Int
//    ): BaseResponse<TableResponse>
    //New API
    @GET("cash-register/tables/electTableList")
    suspend fun electFreelist(@Query("diningStyle") diningStyle: Int): BaseResponse<TableResponse>

    @PUT("cash-register/tables/seatTable")
    suspend fun seatTable(@Body body: RequestBody): BaseBooleanResponse

    @GET("consumer/goods/cashRegisterList")
    suspend fun getGoodsList(
        @Query("diningStyle") diningStyle: String,
        @Query("goodsName") goodsName: String,
        @Query("lastChangeDate") lastChangeData: String,
    ): BaseResponse<ReserveGoodListResponse>

    //菜品列表
    @GET("consumer/goods/cashRegisterList/v2")
    suspend fun getGoodsListV2(
        @Query("diningStyle") diningStyle: String,
        @Query("goodsName") goodsName: String,
        @Query("lastChangeDate") lastChangeData: String,
    ): BaseResponse<ReserveGoodListResponse>

    @GET("consumer/goods_reserve/list")
    suspend fun getGoodsReserveList(
        @Query("goodsName") goodsName: String,
        @Query("lastChangeDate") lastChangeData: String,
    ): BaseResponse<ReserveGoodListResponse>

    @GET("consumer/goods_reserve/single?")
    suspend fun getReserveGoodsDetail(@Query("goodsId") diningStyle: String): BaseResponse<ReserveGoodDetail>

    @GET("consumer/goods/single?")
    suspend fun getGoodsDetail(@Query("goodsId") diningStyle: String): BaseResponse<ReserveGoodDetail>

    @GET("consumerPayAccount/info")
    suspend fun consumerPayAccount(@Query("telephone") telephone: String): BaseResponse<CustomerMemberResponse>

//    @POST("consumer/goods_reserve/saveReserve")
//    suspend fun saveReserve(@Body body: RequestBody): BaseResponse<PaymentResponse>

    @POST("consumer/orders/cartCreatOrder")
    suspend fun cartCreateOrder(@Body body: RequestBody): BaseResponse<PaymentResponse>


    @PUT("cash-register/order/list")
    suspend fun orderList(@Body body: RequestBody): BaseResponse<OrderedTotalResponse>

    @PUT("cash-register/order/list/V2")
    suspend fun orderListV2(@Body body: RequestBody): BaseResponse<OrderedTotalResponse>


    @GET("cash-register/order/{orderNo}")
    suspend fun orderInfo(
        @Path("orderNo") orderNo: String?,
        @Query("isReadOrder") isReadOrder: Boolean? = false
    ): BaseResponse<OrderedInfoResponse>


    @PUT("cash-register/order/againPay")
    suspend fun againPay(@Body body: RequestBody): BaseResponse<PaymentResponse>


    @POST("cash-register/order/getKhqr")
    suspend fun getKhqr(@Body body: RequestBody): BaseResponse<PaymentResponse>


    @PUT("store/orders/fullRefund")
    suspend fun fullRefund(
        @Query("orderId") orderId: String?,
        //1-Online Payment; 2-Cash Payment; 3-User Balance Payment
        @Query("payType") payType: Int,
        //Refund path: 0-Employee 1-Background 2-User 3-Cashier
        @Query("refundRoute") refundRoute: Int,
        @Query("note") node: String = "",
        @Query("autoInStock") autoInStock: Boolean
    ): BaseResponse<PaymentResponse>


    @POST("store/orders/partRefund")
    suspend fun partRefund(@Body body: RequestBody): BaseResponse<PaymentResponse>

    @GET("cash-register/tables/getStoreTable")
    suspend fun storeTable(): BaseResponse<OrderedTableListResponse>

    @POST("cash-register/pendingOrder/add")
    suspend fun addPendingOrder(@Body body: RequestBody): BaseBooleanResponse

    @PUT("cash-register/pendingOrder/list")
    suspend fun getPendingOrderList(@Body body: RequestBody): BaseResponse<PendingListResponse>

    @DELETE("cash-register/pendingOrder/{orderNo}")
    suspend fun deletePendingOrder(@Path("orderNo") orderNo: String?): BaseBooleanResponse

    /**
     * 取出挂单
     *
     * @param orderNo
     * @return
     */
    @PUT("cash-register/pendingOrder/{orderNo}")
    suspend fun getPendingOrder(@Path("orderNo") orderNo: String?): BaseResponse<PendingRecord>

    @GET("consumer/printerTemplate")
    suspend fun getPrinterTemplate(): BaseResponse<PrintTamplateResponse>

    //Add dish to cart for isTableService
    @POST("consumer/cart/add")
    suspend fun addToCart(@Body body: RequestBody): BaseResponse<CartInfoResponse>

    @POST("consumer/cart/change")
    suspend fun changeNum(@Body body: RequestBody): BaseResponse<CartInfoResponse>

    @DELETE("consumer/cart/delGoods")
    suspend fun reduceFromCart(
        @Query("diningStyle") diningStyle: Int?,
        @Query("tableUuid") tableUuid: String?,
        @Query("goodsId") goodsId: String?,
        @Query("cartId") cartId: String?,
    ): BaseResponse<CartInfoResponse>

    @DELETE("consumer/cart/empty")
    suspend fun emptyCart(
        @Query("diningStyle") diningStyle: Int?,
        @Query("tableUuid") tableUuid: String?
    ): BaseBooleanResponse


    @GET("consumer/cart")
    suspend fun getCartInfo(
        @Query("diningStyle") diningStyle: Int?,
        @Query("tableUuid") tableUuid: String?
    ): BaseResponse<CartInfoResponse>

    @PUT("cash-register/tables/switchTable")
    suspend fun switchTable(
        @Body body: RequestBody
    ): BaseResponse<SwitchTableResponse>

    @GET("consumer/orders/addGoodsInfo")
    suspend fun getOrderMore(
        @Query("diningStyle") diningStyle: Int?,
        @Query("tableUuid") tableUuid: String?,
        @Query("orderId") orderId: String?
    ): BaseResponse<OrderMoreDataResponse>

    @POST("consumer/orders/addGoodsToOrders")
    suspend fun addMoreGood(
        @Body requestBody: RequestBody?,
    ): BaseResponse<OrderedInfoResponse>

    @POST("store/consumer_pay_account/list")
    suspend fun getMemberList(
        @Body requestBody: RequestBody?
//        @Query("pageSize") pageSize: Int?,
//        @Query("page") page: Int?,
//        @Query("upayAccount") upayAccount: String?,
//        @Query("userType") userType: String?,
//        @Query("startTime") startTime: String?,
//        @Query("endTime") endTime: String?,
    ): BaseResponse<MemberListResponse>

    @GET("store/consumer_pay_account/rechargeDetailPage")
    suspend fun getRechargeDetailPage(
        @Query("consumerPayAccountId") consumerPayAccountId: String?,
    ): BaseResponse<RechargeDetailResponse>


    @GET("couponTemplate/rechargeTier/page")
    suspend fun getRechargeTierPage(
        @Query("page") page: Int?,  //页码
        @Query("pageSize") pageSize: Int?,  //数量
        @Query("name") name: String?,   //优惠券模板名称
        @Query("type") type: String?,   //优惠券分类
        @Query("activeState") activeState: String?, //状态 NOT_STARTED未开始 IN_PROGRESS进行中
        @Query("available") available: Boolean?,    //是否启用，不传查全部启用状态
    ): BaseResponse<RechargeTierPageResponse>

    @GET("store/consumer_pay_account/rechargeList")
    suspend fun getMemberBalanceList(
        @Query("pageSize") pageSize: Int?,
        @Query("page") page: Int?,
        @Query("upayAccount") upayAccount: String?,
        @Query("type") type: String?,
        @Query("startTime") startTime: String?,
        @Query("endTime") endTime: String?,
        @Query("exactMatch") exactMatch: Boolean?,
    ): BaseResponse<BalanceListResponse>

    @POST("employee/creditList")
    suspend fun getCreditList(@Body requestBody: RequestBody?): BaseResponse<CreditListResponse>

    //挂账详情
    @GET("employee/creditInfo")
    suspend fun getCreditInfo(@Query("consumerId") consumerId: Long?): BaseResponse<CreditInfo>

    //挂账记录列表
    @GET("employee/creditRecordList")
    suspend fun getCreditRecordList(
        @Query("consumerId") consumerId: Long,
        @Query("page") page: Int?,
        @Query("pageSize") pageSize: Int?,
    ): BaseResponse<CreditRecordResponse>

    //还款记录列表
    @GET("employee/repaymentRecordList")
    suspend fun getRepaymentRecordList(
        @Query("consumerId") consumerId: Long,
        @Query("page") page: Int?,
        @Query("pageSize") pageSize: Int?,
    ): BaseResponse<RepaymentRecordResponse>

    //余额明细详情
    @GET("store/consumer_pay_account/recharge/info/{id}")
    suspend fun getOrderDetailInfo(@Path("id") id: String?): BaseResponse<ConsumerRechargeInfo>


    @PUT("store/consumer_pay_account/recharge")
    suspend fun rechargeBalance(@Body requestBody: RequestBody?): RechargeResponse

    @GET("store/data/home/<USER>")
    suspend fun getDashBoard(
        @Query("pageSize") pageSize: Int?,
        @Query("page") page: Int?,
        @Query("type") type: String?,
        @Query("startTime") startTime: String?,
        @Query("endTime") endTime: String?
    ): BaseResponse<StoreStatisticResponse>

    @GET("store/data/order/detail/{orderNo}")
    suspend fun getOrderDetail(@Path("orderNo") orderID: String?): BaseResponse<StoreOrderDetail>

    @GET("store/data/order/list")
    suspend fun getStoreOrder(
        @Query("pageSize") pageSize: Int?,
        @Query("page") page: Int?,
        @Query("startTime") startTime: String?,
        @Query("endTime") endTime: String?,
        @Query("paymentMethod") paymentMethod: String?,
        @Query("orderStatus") orderStatus: String?,
//        @Query("type") type: String?,
        @Query("keyword") keyword: String?,
        //线下支付渠道
        @Query("channelsId") channelsId: Int?,
    ): BaseResponse<StoreOrderListResponse>

    @GET("store/consumer_pay_account/memberOverview/total")
    suspend fun getMemberStatistic(
        @Query("type") type: String?,
        @Query("startTime") startTime: String?,
        @Query("endTime") endTime: String?,
    ): BaseResponse<MemberStatisticResponse>

    @GET("cash-register/order/multiplePrint/{orderNo}")
    suspend fun getMultiplePrinter(@Path("orderNo") orderID: String?): BaseResponse<MultipleOrderResponse>

    @POST("cash-register/order/printerAgain")
    suspend fun postPrinterAgain(@Body requestBody: RequestBody?): BaseBooleanResponse

    @GET("cash-register/order/getConsumer")
    suspend fun getConsumer(@Query("keyword") keyword: String?): BaseResponse<List<ConsumerResponse>>

    @PUT("cash-register/logout")
    suspend fun putLogout(@Body requestBody: RequestBody?): BaseResponse<ShiftReportPrint>

    @GET("cash-register/loginUserInfo")
    suspend fun getLoginUserInfo(): BaseResponse<LoginUserInfoResponse>

    @GET("consumer/orders/abnormalOrder")
    suspend fun getAbNormalOrder(
        @Query("tableUuid") tableUuid: String?,
        @Query("payStatus") payStatus: Int?,
    ): AbNormalOrderResponse

    @GET("consumer/recharge/status")
    suspend fun rechargeQRStatus(
        @Query("orderId") orderNo: String?
    ): BaseResponse<RechargeQRStatusResponse>


    @GET("employee/paymentPolling")
    suspend fun getPaymentStatusPolling(
        @Query("outTradeNo") outTradeNo: String?
    ): BaseResponse<PaymentStatusResponse>

    @GET("cash-register/pendingOrder/getSerialNumber")
    suspend fun getSerialNumber(): BaseResponse<SerialNumberResponse>

    @PUT("store/consumer_pay_account/cancelPay")
    suspend fun putCancelTopUp(@Query("id") id: String?): BaseBooleanResponse


    /**
     * 获取线下支付渠道列表
     * Get a list of offline payment channels
     */
    @GET("offline/payment/channels/list")
    suspend fun getOfflineChannels(): BaseResponse<OfflineChannelTotalModel>


    /**
     * 取消订单
     * Cancel Order
     */
    @PUT("employee/cancelOrder")
    suspend fun cancelOrder(
        @Body requestBody: RequestBody?
//        @Path("orderNo") orderId: String,
//        @Query("cancelReason") cancelReason: String?
    ): BaseBooleanResponse


    /**
     * 取消挂账
     */
    @POST("employee/cancelCredit")
    suspend fun cancelCredit(
        @Body requestBody: RequestBody?
    ): BaseBooleanResponse

    /**
     * 还款
     */
    @POST("employee/repayment")
    suspend fun repayment(
        @Body requestBody: RequestBody?
    ): BaseResponse<RepaymentResponse>


    /**
     * 退菜
     * Change Goods
     */
    @POST("consumer/orders/changeGood")
    suspend fun changeGood(@Body requestBody: RequestBody?): BaseResponse<ChangeGoodResponse>


    /**
     * 创建临时桌子二维码
     * Create a temporary table QR code
     */
    @POST("employee/createTempTableCode")
    suspend fun createTempTableCode(
        @Query("refTableId") refTableId: Long?,
        @Query("isPosPrint") isPosPrint: Boolean = true
    ): BaseResponse<CreateTempTableCodeResponse>


    /**
     * 修改可称重菜品重量/或者时价菜价格
     * Modify Product Weight
     */
    @POST("consumer/orders/confirmPendingGoods")
    suspend fun confirmPendingGoods(@Body requestBody: RequestBody?): BaseResponse<List<OrderedGoods>>

    /**
     * 餐桌是否有未完成订单
     *
     */
    @GET("employee/table/uncompletedOrderCount")
    suspend fun uncompletedOrderCount(@Query("tableUuids") tableUuid: String?): BaseResponse<List<TableOrderStatus>>

    /**
     * 餐桌是否有未完成订单v2
     *
     */
    @GET("employee/table/uncompletedOrderCount/v2")
    suspend fun uncompletedOrderCountV2(
        @Query("tableUuids") tableUuid: String?,
        @Query("orderNo") orderNo: String?
    ): BaseResponse<UncompletedOrderCountResponse>

    /**
     * 订单换桌
     *
     */
    @PUT("cash-register/order/orderToChangeTable")
    suspend fun orderToChangeTable(@Body requestBody: RequestBody?): BaseBooleanResponse


    /**
     * 根据订单ID获取翻译的菜品文案
     */
    @PUT("consumer/orders/translate/v2")
    suspend fun orderTranslate(@Body requestBody: RequestBody?): BaseResponse<OrderedTranslateResponse>

    /**
     * 查询异常单
     */
    @PUT("consumer/orders/abnormalOrder/v2")
    suspend fun abnormalOrderV2(@Body requestBody: RequestBody?): BaseResponse<List<OrderedInfoResponse>>


    /**
     * 版本检查
     */
    @PUT("version/check")
    suspend fun checkVersion(@Body requestBody: RequestBody?): BaseResponse<VersionCheckResponse>


    /**
     * 获取顾客信息
     */
    @PUT("cash-register/tables/customerInfo")
    suspend fun getCustomerInfo(@Body requestBody: RequestBody?): BaseResponse<CustomerInfoVo>

    /**
     *获取支付方式
     */
    @GET("payment/method/list")
    suspend fun getPaymentMethodList(@Query("type") type: Int?): BaseResponse<List<PaymentChannelModel>>

    /**
     *减免折扣详情
     */
    @GET("reduce/discount/detail/infoV2")
    suspend fun getReduceDiscountDetail(@Query("orderNo") orderNo: String?): BaseResponse<ReduceDiscountDetailModel>

    /**
     *确认订单减免折扣信息
     */
    @POST("reduce/discount/order/confirm/v2")
    suspend fun getReduceDiscountConfirm(@Body requestBody: RequestBody?): BaseBooleanResponse

    /**
     * 保存单品减免折扣信息
     */
    @POST("reduce/discount/singleItem")
    suspend fun setSingleItemReduceDiscount(@Body requestBody: RequestBody?): BaseBooleanResponse

    /**
     *获取USB打印配置
     */
    @GET("printer/usbPrinterInfo")
    suspend fun getUsbPrinterInfo(): BaseResponse<PrinterConfigInfo>

    /**
     *获取USB打印配置和wifi打印配置
     */
    @GET("printer/localPrinterInfo")
    suspend fun getPrinterInfoList(): BaseResponse<List<PrinterConfigInfo>>

    /**
     *获取未读数和未打印数
     */
    @GET("cash-register/order/unReadNumAndUnPrint")
    suspend fun getUnReadNumAndUnPrint(): BaseResponse<UnReadAndPrintResponse>


    /**
     *修改打印记录(收银端usb 打印后调用)
     */
    @PUT("printer/updateLog/{orderNo}")
    suspend fun updatePrintLog(@Path("orderNo") orderId: String?): BaseBooleanResponse

    /**
     *添加顾客
     */
    @POST("platformUser/addConsumer")
    suspend fun addConsumer(@Body requestBody: RequestBody?): BaseBooleanResponse

    /**
     *修改店铺会员账户
     */
    @POST("platformUser/updateConsumer")
    suspend fun updateConsumer(@Body requestBody: RequestBody?): BaseBooleanResponse

    /**
     * 发送短信验证码-修改账号信息
     */
    @GET("platformUser/edit/sendSmsCode")
    suspend fun sendSmsCode(
        @Query("consumerId") consumerId: String?,
        @Query("phone") phone: String?
    ): BaseBooleanResponse


    /**
     *云打印打预付款
     */
    @PUT("consumer/orders/preSettlement")
    suspend fun preSettlement(@Body requestBody: RequestBody?): BaseResponse<OrderedInfoResponse>


    /**
     *获取store 信息
     */
    @GET("cash-register/storeInfo")
    suspend fun getStoreInfo(): BaseResponse<StoreInfoResponse>

    /**
     *修改分店信息
     */
    @PUT("store")
    suspend fun saveStore(@Body requestBody: RequestBody?): BaseBooleanResponse

    /**
     *修改订单备注
     */
    @PUT("consumer/orders/updateNote")
    suspend fun updateNote(@Body requestBody: RequestBody?): BaseBooleanResponse

    /**
     *识别优惠券
     */
    @POST("employee/coupon/couponCodeRecognition")
    suspend fun couponCodeRecognition(@Body requestBody: RequestBody?): BaseResponse<CouponModel>

    /**
     *获取订单可用优惠券列表
     */
    @POST("employee/coupon/findOrderCanUseCoupon")
    suspend fun findOrderCanUseCoupon(@Body requestBody: RequestBody?): BaseResponse<List<CouponModel>>

    /**
     *获取充值可用优惠券列表
     */
    @POST("employee/coupon/findTopUpCanUseCoupon")
    suspend fun findTopUpCanUseCoupon(@Body requestBody: RequestBody?): BaseResponse<List<CouponModel>>

    /**
     * 获取接单列表
     */
    @PUT("employee/acceptOrder/page")
    suspend fun getAcceptOrderList(@Body body: RequestBody): BaseResponse<OrderedTotalResponse>

    /**
     * 获取接单详情
     */
    @GET("employee/acceptOrder/info")
    suspend fun getAcceptOrderInfo(
        @Query("id") id: String? = null,
        @Query("orderNo") orderNo: String? = null,
        @Query("isReadOrder") isReadOrder: Boolean? = null,
    ): BaseResponse<OrderedInfoResponse>

    /**
     * 接单
     */
    @POST("employee/order/accept")
    suspend fun acceptOrder(@Body requestBody: RequestBody?): BaseBooleanResponse


    /**
     * 取消接单
     * Cancel Order
     */
    @PUT("employee/acceptOrder/cancelOrder")
    suspend fun cancelAcceptOrder(
        @Body requestBody: RequestBody?
    ): BaseBooleanResponse

    /**
     *获取接单未读数和未打印数
     */
    @GET("employee/acceptOrder/unreadNum")
    suspend fun getAcceptOrderUnReadNumAndUnPrint(): BaseResponse<UnReadAndPrintResponse>


    /**
     *获取公告列表
     */
    @GET("notice")
    suspend fun getNoticeList(
        @Query("page") page: Int?,
        @Query("pageSize") pageSize: Int?
    ): BaseResponse<NoticeListResponse>

    /**
     *获取最后一条公告
     */
    @GET("notice/lastNotice")
    suspend fun getLastNotice(): BaseResponse<NoticeResponse>

    /**
     *获取未读公告数
     */
    @GET("notice/countUnread")
    suspend fun getNoticeUnread(): BaseResponse<Int>

    /**
     *公告已读
     */
    @GET("notice/detail")
    suspend fun getNoticeDetail(
        @Query("id") id: String,
        @Query("read") read: Boolean
    ): BaseResponse<NoticeResponse>


    /**
     *反结账
     */
    @POST("employee/order/reverseCheckOut")
    suspend fun reverseCheckOut(
        @Body requestBody: RequestBody?
    ): BaseBooleanResponse


    /**
     *获取上传路径
     */
    @GET("storage/getUploadToken")
    suspend fun getUploadPath(
        @Query("fileType") fileType: String,
        @Query("fileName") fileName: String,
        @Query("fileSize") fileSize: Long,
    ): BaseResponse<UploadTokenResponse>


    /**
     *获取多语言翻译
     */
    @GET("i18nData")
    suspend fun i18nData(
        @Query("tableName") tableName: String = "store",
        @Query("columnId") columnId: String,
    ): BaseResponse<StoreInfoMultLanguageListRespnose>

    /**
     *设置多语言翻译
     */
    @POST("i18nData")
    suspend fun setI18nData(
        @Body requestBody: RequestBody?
    ): BaseBooleanResponse


    /**
     *可并单列表
     */
    @PUT("employee/mergeAbleOrderList")
    suspend fun mergeAbleOrderList(
        @Body requestBody: RequestBody?
    ): BaseResponse<MergeOrderListResponse>

    /**
     *并单操作
     */
    @PUT("employee/mergeOrder")
    suspend fun mergeOrder(
        @Body requestBody: RequestBody?
    ): BaseBooleanResponse

    /**
     *拆单操作
     */
    @PUT("employee/splitOrder/{orderId}")
    suspend fun splitOrder(
        @Path("orderId") orderId: String?
    ): BaseBooleanResponse


    /**
     *获取logo 的base64
     */
    @GET("store/getLogoBase64")
    suspend fun getLogoBase64(): BaseResponse<String>

    /**
     *获取商品报表打印数据
     */
    @GET("employee/forms/sales/item/orders/getSummarySalesReportPrint")
    suspend fun getSummarySalesReportPrint(
        @Query("startTime") startTime: String,
        @Query("endTime") endTime: String,
        @Query("keyword") keyword: String,
        @Query("groupIds") groupIds: String,
        @Query("orderByColumn") orderByColumn: String,
    ): BaseResponse<ProductReportResponse>

    /**
     * 每日报表数据查询
     */
    @POST("daily/report/data")
    suspend fun getDailyReportData(@Body requestBody: RequestBody?): BaseResponse<DailyReportResponse>

    /**
     *获取支付方式报表打印数据
     */
    @GET("employee/forms/sales/item/orders/getPayMethodReportPrint")
    suspend fun getPayMethodReportPrint(
        @Query("startTime") startTime: String,
        @Query("endTime") endTime: String,
    ): BaseResponse<PaymentMethodReportResponse>


    @GET("cashierMsg/unread")
    suspend fun getUnreadCashierMsg(): BaseResponse<List<UnreadCashierMsgResponse>>


    /**
     * 获取临时菜列表
     *
     * @return
     */
    @GET("store/goods_temporary")
    suspend fun getGoodsTemporaryList(): BaseResponse<List<Goods>>


    /**
     * 创建临时菜列表/修改临时菜
     *
     * @return
     */
    @POST("store/goods_temporary")
    suspend fun createGoodsTemporary(@Body requestBody: RequestBody?): BaseBooleanResponse


    /**
     * 删除临时菜
     * @return
     */
    @DELETE("store/goods_temporary/{id}")
    suspend fun deleteGoodsTemporary(@Path("id") id: Long?): BaseBooleanResponse

    /**
     * 批量加购临时菜品
     * @return
     */
    @POST("consumer/cart/temporary/batchAdd")
    suspend fun temporaryBatchAdd(@Body requestBody: RequestBody?): BaseBooleanResponse

    /**
     * 保存或更新备用金信息
     *
     */
    @POST("shift/saveOrUpdateOpeningCash")
    suspend fun saveOrUpdateOpeningCash(@Body requestBody: RequestBody?): BaseBooleanResponse

    /**
     * 开班
     */
    @POST("shift/start")
    suspend fun shiftStart(@Body requestBody: RequestBody?): BaseBooleanResponse

    /**
     * 获取当前用户交接班记录
     */
    @GET("shift/getLog")
    suspend fun getShiftLog(): BaseResponse<CashRegisterHandoverLogVo>

    /**
     * 分页获取交接班记录
     */
    @GET("employee/pageCashRegisterHandoverLog")
    suspend fun pageCashRegisterHandoverLog(
        @Query("page") page: Int?,
        @Query("pageSize") pageSize: Int?,
        @Query("keyword") keyword: String?,
        @Query("startTime") startTime: String?,
        @Query("endTime") endTime: String?,
    ): BaseResponse<CashRegisterHandoverLog>

    /**
     * 获取交接班记录报表
     */
    @GET("employee/forms/sales/item/orders/getShiftReportPrint")
    suspend fun getShiftReportPrint(
        @Query("shiftId") shiftId: Long,    //交接班id
    ): BaseResponse<ShiftReportPrint>

    /**
     * 获取交接班记录折扣订单信息
     */
    @GET("employee/getShiftOrderDiscountInfo")
    suspend fun getShiftOrderDiscountInfo(
        @Query("shiftId") shiftId: Long,    //交接班id
    ): BaseResponse<List<ShiftOrderDiscountInfo>>

    /**
     * 获取每日报表折扣详情
     */
    @GET("daily/report/discount-details")
    suspend fun getDailyReportDiscountDetails(
        @Query("dailyReportId") dailyReportId: Long,    //交接班id
    ): BaseResponse<List<ShiftOrderDiscountInfo>>

    /**
     * 上报日志
     */
    @POST("print/log")
    suspend fun uploadLog(@Body requestBody: RequestBody?): BaseBooleanResponse


    /**
     * 更新顾客信息
     */
    @PUT("consumer/orders/updateCustomerInfo")
    suspend fun updateCustomerInfo(@Body requestBody: RequestBody?): BaseBooleanResponse

    /**
     * 订单所属减免折扣详情列表
     */
    @POST("reduce/discount/order/discountInfo/list")
    suspend fun orderDiscountInfoList(@Body requestBody: RequestBody?): BaseResponse<List<DiscountReduceInfo>>

    /**
     * 导出  type  2 商品报表
     */
    @POST("employee/forms/sales/orders/export")
    suspend fun getSalesOrdersExport(
        @Body requestBody: RequestBody?
    ): BaseResponse<String>

    /**
     * 导出每日报表
     */
    @POST("daily/report/export/url")
    suspend fun exportDailyReport(
        @Body requestBody: RequestBody?
    ): BaseResponse<String>

    /**
     *  外卖平台列表
     */
    @GET("store/deliveryPlatform/deliveryPlatformList")
    suspend fun getDeliveryPlatformList(
    ): BaseResponse<List<TakeOutPlatformModel>>

    /**
     *  外卖平台菜品列表
     */

    @GET("consumer/goods/deliveryPlatform/cashRegisterList")
    suspend fun getDeliveryPlatformGoodList(
        @Query("deliveryPlatformId") deliveryPlatformId: String,
        @Query("goodsName") goodsName: String,
        @Query("lastChangeDate") lastChangeData: String,
    ): BaseResponse<ReserveGoodListResponse>

    /**
     *  更新外卖订单id
     */

    @PUT("consumer/orders/editDeliveryOrderNo")
    suspend fun editDeliveryOrderNo(
        @Body requestBody: RequestBody?
    ): BaseBooleanResponse

    /**
     *  修改菜品备注及订单备注
     */

    @POST("consumer/orders/modifyOrdersGoodsNotes")
    suspend fun updateOrderOrGoodNote(
        @Body requestBody: RequestBody?
    ): BaseResponse<OrderedInfoResponse>


    /**
     *  购物车菜品修改
     */

    @PUT("consumer/cart/edit")
    suspend fun cartGoodEdit(
        @Body requestBody: RequestBody?
    ): BaseResponse<CartInfoResponse>


    /**
     *销售订单列表汇总
     */
    @GET("employee/forms/summary/sales/orders")
    suspend fun getSalesReportPrint(
        @Query("startTime") startTime: String,
        @Query("endTime") endTime: String,
        @Query("keyword") keyword: String,
        @Query("orderByColumn") orderByColumn: String?,
    ): BaseResponse<SaleReportResponse>

    /**
     *获取商品分类
     */
    @GET("store/goods_groups/simple/list")
    suspend fun getClassificationList(
    ): BaseResponse<List<GoodClassificationModel>>

    /**
     *获取快捷备注列表
     */
    @GET("cash-register/shortcutNoteList")
    suspend fun getShortcutNoteList(
    ): BaseResponse<ArrayList<QuickRemarkModel>>


    @POST("consumer/cart/confirmPendingGoods")
    suspend fun cartConfirmPendingGoods(
        @Body requestBody: RequestBody?
    ): BaseResponse<CartInfoResponse>

    @POST("consumer/cart/asyncChange")
    suspend fun asyncChange(
        @Body requestBody: RequestBody?
    ): BaseResponse<CartInfoResponse>

    @PUT("consumerPayAccount/info/V2")
    suspend fun consumerPayAccountV2(
        @Body requestBody: RequestBody?
    ): BaseResponse<SearchCustomerMemberResponse>


    @GET("store/listPayChannel")
    suspend fun getListPayChannel(
    ): BaseResponse<PaymentChannelResponse>

    @POST("master/report/forms/page")
    suspend fun getReportFormPage(
        @Body requestBody: RequestBody?
    ): BaseResponse<ComprehensiveReportData>

//    /**
//     *称重
//     */
//    @GET("consumer/orders/confirmPendingGoods")
//    suspend fun getClassificationList(
//    ): BaseResponse<List<GoodClassificationModel>>
}