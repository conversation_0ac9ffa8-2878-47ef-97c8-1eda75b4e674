package com.metathought.food_order.casheir.ui.ordered.refunds


import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.GoodTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.DialogNewCancelDishBinding
import com.metathought.food_order.casheir.extension.addMealSetTag
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnableAdd
import com.metathought.food_order.casheir.extension.setEnableMinus
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.extension.setStrokeAndColor
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class NewCancelDishDialog : DialogFragment() {
    private var binding: DialogNewCancelDishBinding? = null
    private var onConfirmClickListener: ((OrderedInfoResponse, Boolean) -> Unit)? = null

    private var currentOrderedInfo: OrderedInfoResponse? = null

    private var orderGoods: OrderedGoods? = null

    /**
     * 将退还商品自动入库 true 是 false 否
     */
    private var autoInStock = true

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogNewCancelDishBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
        initObserver()
    }

    private fun initObserver() {

    }

    private fun initData() {
        binding?.apply {
            context?.let {
                if (currentOrderedInfo?.showAutoInStockBtn == true) {
                    llAutoInStore.isVisible = true
                    autoInStock = true
                    updateSwitch()
                }

                llAutoInStore.isVisible = currentOrderedInfo?.showAutoInStockBtn == true

//                if (currentOrderedInfo?.isPaymentAdvance == true || currentOrderedInfo?.payStatus == OrderedStatusEnum.TO_BE_CONFIRM.id
//                ) {
//                    //先付款  或者待确认单 或者是待称重商品部显示入库按钮
//                    llAutoInStore.isVisible = false
//                    autoInStock = false
//                    updateSwitch()
//                } else {
//                    if (orderGoods?.orderMealSetGoodsDTOList.isNullOrEmpty()) {
//                        //普通商品
//                        llAutoInStore.isVisible =
//                            orderGoods?.materialId != null && orderGoods?.warehouseId != null && orderGoods?.isHasNeedWeight() == false
//                        autoInStock = llAutoInStore.isVisible
//                        updateSwitch()
//                    } else {
//                        //套餐子商品是否有绑定物料的  但凡子商品有一个有绑定物料就显示入库
//                        var isBind = false
//                        orderGoods?.orderMealSetGoodsDTOList?.forEach {
//                            if (it?.materialId != null && it?.warehouseId != null) {
//                                isBind = true
//                            }
//                        }
//                        llAutoInStore.isVisible = isBind
//                        autoInStock = llAutoInStore.isVisible
//                        updateSwitch()
//                    }
//                }


                orderGoods?.let { data ->
                    binding.apply {
                        if (data.refundsNum == null) data.refundsNum = 0

                        tvFoodName.text = data.name
                        if (!data.orderMealSetGoodsDTOList.isNullOrEmpty()) {
                            tvFoodName.addMealSetTag(requireContext())
                        }
                        tvFoodSubName.isVisible = data.getGoodsTagStr().isNotEmpty()
                        tvFoodSubName.text = data.getGoodsTagStr()

                        val activityLabel = data?.activityLabels?.firstOrNull()
                        if (activityLabel != null) {
                            tvDiscountActivity.setStrokeAndColor(
                                color = Color.parseColor(
                                    activityLabel.color
                                )
                            )
                            tvDiscountActivity.setTextColor(Color.parseColor(activityLabel.color))
                            tvDiscountActivity.text = activityLabel.name
                            tvDiscountActivity.isVisible = true
                        } else {
                            tvDiscountActivity.isVisible = false
                        }

                        if (data?.goodsType == GoodTypeEnum.TEMPORARY.id) {
                            tvTmpSign.setStrokeAndColor(color = R.color.black60)
                            tvTmpSign.isVisible = true
                        } else {
                            tvTmpSign.isVisible = false
                        }

                        layoutPrice.tvWeight.isVisible = false
                        layoutPrice.tvTimePriceSign.isVisible = false
                        if (data.isToBeWeighed()) {
                            //如果是称重商品
                            if (data.isHasCompleteWeight()) {
                                layoutPrice.tvWeight.isVisible = true
                                layoutPrice.tvWeight.text = "(${data.getWeightStr()})"
                                if (data.isMealSet()) {
                                    layoutPrice.tvWeight.isVisible = false
                                }
                            } else {
                                layoutPrice.tvFoodPrice.text = getString(R.string.to_be_weighed)
                                layoutPrice.tvVipPrice.isVisible = false
                            }
                        }

                        if (data.isTimePriceGood()) {
                            //如果是时价菜
                            if (!data.isHasCompletePricing()) {
                                layoutPrice.tvFoodPrice.text = getString(R.string.time_price)
                            } else {
                                layoutPrice.tvTimePriceSign.isVisible = true
                            }
                        }

                        if (data.isHasProcessed()) {
                            layoutPrice.tvFoodPrice.text =
                                data.totalFinalPriceAfterFirstRefund(false)
                                    .priceFormatTwoDigitZero2()
                        }

                        updateRefundNum()
                    }
                }
            }
        }
    }

    private fun updateRefundNum() {
        binding?.apply {
            orderGoods?.let { data ->
                tvQTY.text = "${data.getCanRefundNum() - (data.refundsNum ?: 0)}"
                tvRefundMinusCount.text =
                    "${getString(R.string.retreat)}${data.refundsNum.toString()}"
                tvRefundMinusCount.isVisible = data.refundsNum!! > 0

                imgMinus.setEnableMinus(data.refundsNum!! > 0)
                imgPlus.setEnableAdd(data.refundsNum!! < data.getCanRefundNum())

                btnConfirm.setEnableWithAlpha((data.refundsNum ?: 0) > 0)
            }
        }
    }

    private fun initListener() {
        binding?.apply {

            ivSwitch.setOnClickListener {
                autoInStock = !autoInStock
                updateSwitch()
            }

            btnClose.setOnClickListener {
                //副屏取消弹窗
                dismissAllowingStateLoss()
            }
            btnCancel.setOnClickListener {
                //副屏取消弹窗
                dismissAllowingStateLoss()
            }
            btnConfirm.setOnClickListener {
                currentOrderedInfo?.let {
                    onConfirmClickListener?.invoke(
                        it,
                        autoInStock
                    )
                }
                dismissAllowingStateLoss()
            }

            imgPlus.setOnClickListener {
                if (orderGoods!!.refundsNum!! < orderGoods!!.getCanRefundNum()) {
                    orderGoods!!.refundsNum = orderGoods!!.refundsNum!! + 1
                    updateRefundNum()
                }
            }


            imgMinus.setOnClickListener {
                if (orderGoods!!.refundsNum!! > 0) {
                    orderGoods!!.refundsNum = orderGoods!!.refundsNum!! - 1
                    updateRefundNum()
                }
            }
        }
    }

    private fun updateSwitch() {
        binding?.apply {
            ivSwitch.setImageResource(if (autoInStock) R.drawable.icon_switch_open else R.drawable.icon_switch_close)
        }
    }


    companion object {
        private const val TAG = "NewCancelDishDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            currentOrderedInfo: OrderedInfoResponse,
            orderGoods: OrderedGoods,
            onConfirmClickListener: ((OrderedInfoResponse?, Boolean) -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            currentOrderedInfo.goods?.forEach { it.refundsNum = 0 }
            fragment = newInstance(
                onConfirmClickListener = onConfirmClickListener,
                orderGoods = orderGoods,
                currentOrderedInfo = currentOrderedInfo
            )
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? NewCancelDishDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            currentOrderedInfo: OrderedInfoResponse,

            orderGoods: OrderedGoods,
            onConfirmClickListener: ((OrderedInfoResponse?, Boolean) -> Unit),
        ): NewCancelDishDialog {
            val args = Bundle()
            val fragment = NewCancelDishDialog()
            fragment.onConfirmClickListener = onConfirmClickListener
            fragment.currentOrderedInfo = currentOrderedInfo
            fragment.orderGoods = orderGoods
            fragment.arguments = args

            return fragment
        }
    }

}
