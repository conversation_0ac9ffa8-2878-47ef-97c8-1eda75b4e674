package com.metathought.food_order.casheir.ui.store_dashbord

import android.annotation.SuppressLint
import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home.SalesRanking
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home.StoreStatisticResponse
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.order_list.StoreOrderListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelTotalModel
import com.metathought.food_order.casheir.data.model.base.response_model.offline.PaymentMethodModel
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.network.websocket.ApiWebSocket
import com.tinder.scarlet.Lifecycle
import com.tinder.scarlet.WebSocket
import com.tinder.scarlet.lifecycle.LifecycleRegistry
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class StoreDashboardViewModel
@Inject
constructor(private val repository: Repository) : ViewModel() {
    private var pagNo = 1
    private val pageSize = 20
    private val _uiStoreStatisticState = MutableLiveData<UIListModel>()
    val uiStoreStatisticState get() = _uiStoreStatisticState

    private val _uiStoreOrdersState = MutableLiveData<UIOrderListModel>()
    val uiStoreOrdersState get() = _uiStoreOrdersState
    val uiModeState get() = _uiModeState
    private val _uiModeState = MutableLiveData<UIModel>()

    fun getStoreStatistic(
        isRefresh: Boolean? = null,
        type: String? = null,
        startDate: String? = null,
        endDate: String? = null
    ) {
        viewModelScope.launch {
            if (isRefresh != false) {
                pagNo = 1
                if (isRefresh == null) {
                    emitUIListState(showLoading = true)
                }
            }
            try {
                val response = repository.getStoreStatistic(
                    pageSize = pageSize,
                    page = pagNo,
                    type = type,
                    startTime = startDate,
                    endTime = endDate
                )

                if (response is ApiResponse.Success) {
                    val response = response.data
                    if (response.storeMoneyOrderDetailList?.storeMoneyOrderDetailVos.isNullOrEmpty()) {
                        emitUIListState(
                            showEnd = true,
                            isRefresh = isRefresh,
                            showLoading = false,
                            showSuccess = response
                        )
                        return@launch
                    }
                    pagNo++
                    emitUIListState(
                        showSuccess = response,
                        isRefresh = isRefresh,
                        showLoading = false
                    )

                } else if (response is ApiResponse.Error) {
                    emitUIListState(showError = response.message, showLoading = false)
                }

            } catch (e: Exception) {
                Timber.e("111111")
                emitUIListState(showError = e.message, showLoading = false)
            }
        }
    }

    private suspend fun emitUIListState(
        showLoading: Boolean? = null,
        showError: String? = null,
        showSuccess: StoreStatisticResponse? = null,
        showEnd: Boolean = false,
        isRefresh: Boolean? = null,
    ) {
        val uiModel =
            UIListModel(
                showLoading,
                showError,
                showSuccess,
                showEnd,
                isRefresh
            )
        withContext(Dispatchers.Main) {
            _uiStoreStatisticState.value = uiModel
        }
    }

    data class UIListModel(
        val showLoading: Boolean?,
        val showError: String?,
        val showSuccess: StoreStatisticResponse?,
        val showEnd: Boolean,
        val isRefresh: Boolean?,
    )

    fun getDefaultTopSaleList(): List<SalesRanking> {
        return List(5) { SalesRanking("- -", 0) }
    }

    var offlineChannelList: OfflineChannelTotalModel? = null
    var paymentMethodList: ArrayList<PaymentMethodModel>? = null
    fun getChannels() {
        viewModelScope.launch {
            emitUIState(ApiResponse.Loading)
            try {
                val result = repository.getOfflineChannels()
                emitUIState(result = result)
            } catch (e: Exception) {
                emitUIState(result = ApiResponse.Error(e.message))
            }
        }
    }

    fun getStoreOrderList(
        isRefresh: Boolean? = null,
        startDate: String? = null,
        endDate: String? = null,
        paymentMethod: String? = null,
        orderStatus: String? = null,
        channelsId: Int? = null,
//        type: String?,
        keyword: String?
    ) {
        viewModelScope.launch {
            if (isRefresh != false) {
                pagNo = 1
                if (isRefresh == null) {
                    emitUIOrderListState(showLoading = true)
                }
            }
            try {
                if (offlineChannelList == null) {
                    val result = repository.getOfflineChannels()
                    if (result is ApiResponse.Success) {
                        paymentMethodList = ArrayList()
                        paymentMethodList?.add(PaymentMethodModel(paymentMethod = -1))
                        paymentMethodList?.add(PaymentMethodModel(paymentMethod = PayTypeEnum.ONLINE_PAYMENT.id))
                        offlineChannelList = result.data.apply {
                            forEach {
                                paymentMethodList?.add(
                                    PaymentMethodModel(
                                        offlineChannelId = it.id,
                                        channelsName = it.channelsName,
                                        paymentMethod = PayTypeEnum.CASH_PAYMENT.id
                                    )
                                )
                            }
                        }
                        paymentMethodList?.add(PaymentMethodModel(paymentMethod = PayTypeEnum.USER_BALANCE.id))
                        paymentMethodList?.add(PaymentMethodModel(paymentMethod = PayTypeEnum.PAY_OTHER.id))
                        paymentMethodList?.add(PaymentMethodModel(paymentMethod = PayTypeEnum.CREDIT.id))
                    }
                }
                val response = repository.getStoreOrdered(
                    pageSize = pageSize,
                    page = pagNo,
                    startTime = startDate,
                    endTime = endDate,
                    orderStatus = orderStatus,
                    paymentMethod = paymentMethod,
//                    type = type,
                    keyword = keyword,
                    channelsId = channelsId
                )
                withContext(Dispatchers.Main) {
                    if (response is ApiResponse.Success) {
                        val response = response.data
                        response.offlineChannelTotalModel = offlineChannelList
                        if (response.storeDataOrderVos.isNullOrEmpty()) {
                            emitUIOrderListState(
                                showEnd = true,
                                isRefresh = isRefresh,
                                showLoading = false,
                                keyword = keyword
                            )
                            return@withContext
                        }
                        pagNo++
                        emitUIOrderListState(
                            showSuccess = response,
                            isRefresh = isRefresh,
                            showLoading = false,
                            keyword = keyword
                        )

                    } else if (response is ApiResponse.Error) {
                        emitUIOrderListState(showError = response.message, showLoading = false)
                    }
                }
            } catch (e: Exception) {
                emitUIOrderListState(showError = e.message, showLoading = false)
            }
        }
    }

    private suspend fun emitUIOrderListState(
        showLoading: Boolean? = null,
        showError: String? = null,
        showSuccess: StoreOrderListResponse? = null,
        showEnd: Boolean = false,
        isRefresh: Boolean? = null,
        keyword: String? = null,
    ) {
        val uiModel =
            UIOrderListModel(
                showLoading,
                showError,
                showSuccess,
                showEnd,
                isRefresh,
                keyword
            )
        withContext(Dispatchers.Main) {
            _uiStoreOrdersState.value = uiModel
        }
    }

    data class UIOrderListModel(
        val showLoading: Boolean?,
        val showError: String?,
        val showSuccess: StoreOrderListResponse?,
        val showEnd: Boolean,
        val isRefresh: Boolean?,
        val keyword: String? = null,
    )

    private suspend fun emitUIState(
        result: ApiResponse<OfflineChannelTotalModel>? = null
    ) {
        withContext(Dispatchers.Main) {
            _uiModeState.value = UIModel(result = result)
        }
    }

    data class UIModel(
        val result: ApiResponse<OfflineChannelTotalModel>?,
    )




//    //Socket
//    private val _liveDATA = MutableLiveData<WebSocket.Event>()
//    val liveDataRespose get() = _liveDATA
//    val lifecycleRegistry = LifecycleRegistry()
//    val apiWebSocketService = ApiWebSocket.provideSocketApi(lifecycleRegistry)
//
//    @SuppressLint("CheckResult")
//    fun connectWebsocket() {
//        apiWebSocketService.observeConnection().observeOn(Schedulers.io()).subscribe({ response ->
//            Timber.e("WebSocket  ${response.toString()}")
//            viewModelScope.launch {
//                _liveDATA.value = response
//            }
//        }, { error ->
//            Timber.e("WebSocket ${error.message.orEmpty()}")
//        })
//    }
//
//    fun testingWebsocketSendMessage() {
//        apiWebSocketService.sendMessage("pong")
//        Log.d("WebSocket", "send back pong")
//    }
//
//
//    fun destroylifeCycle() {
//        lifecycleRegistry.onNext(Lifecycle.State.Destroyed)
//    }
//
//    fun startLifeCycle() {
//        lifecycleRegistry.onNext(Lifecycle.State.Started)
//    }
}