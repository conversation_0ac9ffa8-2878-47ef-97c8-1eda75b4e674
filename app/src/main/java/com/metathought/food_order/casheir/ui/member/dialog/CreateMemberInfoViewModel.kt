package com.metathought.food_order.casheir.ui.member.dialog

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2024/07/29 23:03
 * @description
 */
@HiltViewModel
class CreateMemberInfoViewModel
@Inject
constructor(private val repository: Repository) : ViewModel() {
    private val _uiState = MutableLiveData<UIModel>()

    val uiState get() = _uiState


     fun addConsumer(userName: String?, phone: String?) {
        viewModelScope.launch {
            val result = repository.addConsumer(userName, phone)
            if (result is ApiResponse.Success) {
                emitUiState(success = result.data)

            } else if (result is ApiResponse.Error) {
                emitUiState(error = result.message)
            }
        }
    }


    private fun emitUiState(
        error: String? = null,
        success: BaseBooleanResponse? = null,
    ) {
        val uiModel = UIModel(error, success)
        _uiState.postValue(uiModel)
    }

    data class UIModel(
        val error: String?,
        val success: BaseBooleanResponse?,
    )
}
