package com.metathought.food_order.casheir.ui.dialog.store_manager

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLogVo
import com.metathought.food_order.casheir.data.model.base.response_model.login.ConversionRatioRecord
import com.metathought.food_order.casheir.databinding.DialogExchangeRateChangeHistoryBinding
import com.metathought.food_order.casheir.databinding.DialogShiftHandoverOfflineRecordsDetailBinding
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.ExchangeRateChangeHistoryAdapter
import com.metathought.food_order.casheir.ui.adapter.ShiftHandoverOfflineRecordsAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import dagger.hilt.android.AndroidEntryPoint

/**
 * 修改汇率历史记录
 */
@AndroidEntryPoint
class ExchangeRateChangeHistoryDialog : BaseDialogFragment() {
    private var binding: DialogExchangeRateChangeHistoryBinding? = null
    private var conversionRatioRecordList: List<ConversionRatioRecord>? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogExchangeRateChangeHistoryBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
//        initObserver()
    }


    private fun initData() {
        binding?.apply {
            rvList.adapter = ExchangeRateChangeHistoryAdapter(conversionRatioRecordList ?: listOf())
        }
    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

        }
    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.4).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.4).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    companion object {
        private const val TAG = "ExchangeRateChangeHisto"


        fun showDialog(
            fragmentManager: FragmentManager,
            conversionRatioRecordList: List<ConversionRatioRecord>? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(conversionRatioRecordList)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? ExchangeRateChangeHistoryDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            conversionRatioRecordList: List<ConversionRatioRecord>? = null
        ): ExchangeRateChangeHistoryDialog {
            val args = Bundle()
            val fragment = ExchangeRateChangeHistoryDialog()
            fragment.conversionRatioRecordList = conversionRatioRecordList
            fragment.arguments = args
            return fragment
        }
    }

}
