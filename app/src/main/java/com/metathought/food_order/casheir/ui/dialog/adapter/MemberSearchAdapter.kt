package com.metathought.food_order.casheir.ui.dialog.adapter

import android.annotation.SuppressLint
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.databinding.ItemMemberSearchBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.utils.SingleClickUtils
import timber.log.Timber
import java.util.regex.Pattern

class MemberSearchAdapter(
    private val onMemberClick: (CustomerMemberResponse) -> Unit
) : RecyclerView.Adapter<MemberSearchAdapter.MemberViewHolder>() {

    // 新增：当前搜索关键字（用于高亮）
    private var currentKeyword: String? = null

    // 新增：设置当前搜索关键字
    fun setCurrentKeyword(keyword: String?) {
        currentKeyword = keyword
    }

    // 内部数据列表
    private val memberList = mutableListOf<CustomerMemberResponse>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MemberViewHolder {
        val binding = ItemMemberSearchBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return MemberViewHolder(binding)
    }

    override fun onBindViewHolder(holder: MemberViewHolder, position: Int) {
        holder.bind(memberList[position])
    }

    override fun getItemCount(): Int = memberList.size

    inner class MemberViewHolder(
        private val binding: ItemMemberSearchBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        @SuppressLint("SetTextI18n")
        fun bind(member: CustomerMemberResponse) {
            binding.apply {
                Timber.e("MemberViewHolder", "绑定数据: memberId=${member.nickName}")
                // 高亮昵称中的关键字
                val nickName = member.nickName ?: ""
                tvMemberName.text = highlightSearchKeyword(nickName, currentKeyword)

                // 高亮会员号中的关键字
                val memberNumber = "${itemView.context.getString(R.string.member_number)}: ${
                    member.memberNumber ?: ""
                }"
                tvMemberNumber.text = highlightSearchKeyword(
                    memberNumber,
                    currentKeyword
                )

                // 高亮手机号中的关键字
                val telephone = "${itemView.context.getString(R.string.account_number)}: ${
                    member.telephone ?: ""
                }"
                tvMemberPhone.text = highlightSearchKeyword(
                    telephone,
                    currentKeyword
                )

                // 余额显示（无需修改）
                val balance = member.balance ?: 0L
                tvMemberBalance.text = balance.priceFormatTwoDigitZero2()


                // 点击事件（无需修改）
                root.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        onMemberClick(member)
                    }
                }
            }
        }

        private fun highlightSearchKeyword(text: String, keyword: String?): SpannableString {
            val spannable = SpannableString(text)
            if (keyword.isNullOrEmpty()) return spannable // 无关键字时不高亮

            // 不区分大小写匹配关键字
            val pattern = Pattern.compile(Pattern.quote(keyword), Pattern.CASE_INSENSITIVE)
            val matcher = pattern.matcher(text)
            while (matcher.find()) {
                // 设置高亮颜色（使用 highlight_color 资源）
                spannable.setSpan(
                    ForegroundColorSpan(
                        ContextCompat.getColor(
                            binding.root.context,
                            R.color.primaryColor
                        )
                    ),
                    matcher.start(),
                    matcher.end(),
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
            return spannable
        }
    }


    /**
     * 添加数据到现有列表（用于分页加载）
     */
    fun addData(newData: List<CustomerMemberResponse>) {
        android.util.Log.d(
            "MemberAdapter",
            "addData: 当前${memberList.size}条, 新增${newData.size}条"
        )

        val startPosition = memberList.size
        memberList.addAll(newData)

        // 通知适配器有新数据插入
        notifyItemRangeInserted(startPosition, newData.size)

        android.util.Log.d("MemberAdapter", "addData完成: 适配器数量=${itemCount}")
    }

    /**
     * 替换所有数据（用于新搜索）
     */
    fun replaceData(newData: List<CustomerMemberResponse>) {
        android.util.Log.d(
            "MemberAdapter",
            "replaceData: 新数据${newData.size}条, 当前列表${memberList.size}条"
        )

        memberList.clear()
        memberList.addAll(newData)

        // 通知适配器数据已完全改变
        notifyDataSetChanged()

        android.util.Log.d("MemberAdapter", "replaceData完成: 适配器数量=${itemCount}")
    }

    /**
     * 清空数据
     */
    fun clearData() {
        android.util.Log.d("MemberAdapter", "clearData: 清空数据")

        val oldSize = memberList.size
        memberList.clear()

        // 通知适配器数据已移除
        if (oldSize > 0) {
            notifyItemRangeRemoved(0, oldSize)
        }

        android.util.Log.d("MemberAdapter", "clearData完成: 适配器数量=${itemCount}")
    }

    /**
     * 获取指定位置的会员数据
     */
    fun getMemberAt(position: Int): CustomerMemberResponse? {
        return if (position in 0 until memberList.size) {
            memberList[position]
        } else {
            null
        }
    }

    /**
     * 根据ID查找会员
     */
    fun findMemberById(memberId: String): CustomerMemberResponse? {
        return memberList.find { it.accountId == memberId }
    }


}
