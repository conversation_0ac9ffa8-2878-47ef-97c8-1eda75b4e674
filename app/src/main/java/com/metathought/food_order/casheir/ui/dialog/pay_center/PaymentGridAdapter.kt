package com.metathought.food_order.casheir.ui.dialog.pay_center

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import coil3.load
import coil3.request.crossfade
import coil3.request.error
import coil3.request.placeholder
import coil3.request.transformations
import coil3.transform.RoundedCornersTransformation
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.PaymentChannel
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.extension.toJson
import timber.log.Timber

class PaymentGridAdapter(
    private var items: List<OfflineChannelModel>,
    private var payViewModel: PayViewModel,
    private val onItemClick: (OfflineChannelModel, Int) -> Unit
) : RecyclerView.Adapter<PaymentGridAdapter.GridViewHolder>() {

    private val onlinePaymentId = PaymentChannel.ONLINE.id // 新增：线上支付ID常量
    private val balancePaymentId = PaymentChannel.BALANCE.id // 新增：余额支付ID常量
    private var selectItems: MutableList<OfflineChannelModel> = mutableListOf()

    private var isSupportMixedPayment = true

    class GridViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val icon: ImageView = itemView.findViewById(R.id.gridIcon)
        val title: TextView = itemView.findViewById(R.id.gridTitle)
        val container: View = itemView
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GridViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_grid_payment, parent, false)
        return GridViewHolder(view)
    }

    override fun onBindViewHolder(holder: GridViewHolder, position: Int) {
        val item = items[position]

//        holder.icon.setImageResource(item.iconRes)

        holder.title.text = item.channelsName
        holder.title.isVisible = true
        holder.icon.isVisible = true
        holder.container.setBackgroundResource(R.drawable.background_white_radius_4dp)

        var isDisabled = false

        holder.container.setEnable(true)
        if (item.id == PaymentChannel.EXPAND.id) {
            // 展开按钮处理
            holder.icon.load(R.drawable.icon_payment_export)
            // 根据混合支付状态和选择数量判断是否禁用展开按钮
//            isDisabled = when {
//                isSupportMixedPayment && selectItems.size >= 2 -> true
//                !isSupportMixedPayment && selectItems.size >= 1 -> true
//                else -> false
//            }
            isDisabled = false
        } else if (item.id == PaymentChannel.COLLAPSE.id) {
            // 关闭按钮处理
            holder.icon.load(R.drawable.icon_payment_collapse)
            isDisabled = false
        } else {
            if (item.id == PaymentChannel.ONLINE.id) {
                holder.icon.load(R.drawable.icon_payment_online)
            } else if (item.id == PaymentChannel.BALANCE.id) {
                holder.icon.load(R.drawable.icon_payment_balance)
            } else if (item.id == PaymentChannel.CASH.id) {
                holder.icon.load(R.drawable.icon_payment_cash)
            } else {
                try {
                    holder.icon.load(item.logoUrl) {  // 取消注释并启用头像加载
                        memoryCacheKey(item.id.toString())
                        transformations(RoundedCornersTransformation(8f, 8f, 8f, 8f))
                        crossfade(true)  // 渐变动画
                        placeholder(R.drawable.icon_payment_default)  // 默认占位图
                        error(R.drawable.icon_payment_default)  // 加载失败默认图
                    }
                } catch (e: Exception) {
                    Timber.e("加载头像异常: ${e.message}")
                    holder.icon.load(R.drawable.icon_payment_default)
                }
            }

            // 判断是否被选中
            val isSelected = selectItems.contains(item)
            // 判断是否需要禁用（非线上支付且未选中且已选数量≥2）
            isDisabled = when {
                // 规则1：线上支付始终可点击
                item.id == onlinePaymentId -> false
                // 规则2：已选中的项不禁用
                isSelected -> false
                //不支持混合支付且已选1个时，禁用其他未选中项
                !isSupportMixedPayment && selectItems.size >= 1 && !isSelected -> true
                // 规则3：已选满2个时禁用其他项
                isSupportMixedPayment && selectItems.size >= 2 -> true
                // 规则4：已选非线上支付时，仅允许选余额
                isSupportMixedPayment && selectItems.size == 1 -> {
                    val existingItem = selectItems.first()
                    val existingIsNonOnline =
                        existingItem.id != onlinePaymentId && existingItem.id != balancePaymentId
                    val currentIsNonOnline =
                        item.id != onlinePaymentId && item.id != balancePaymentId
                    existingIsNonOnline && currentIsNonOnline  // 已选非线上时，其他非线上禁用
                }

                else -> false
            }

            // 设置选中状态
            if (isSelected) {
                holder.container.setBackgroundResource(R.drawable.background_payment_methon_select)
            } else {
                holder.container.setBackgroundResource(R.drawable.background_white_radius_4dp)
            }
//            // 禁用时设置透明度和不可点击
//            holder.icon.alpha = if (isDisabled) 0.5f else 1f
//            holder.title.alpha = if (isDisabled) 0.5f else 1f
//            holder.container.isClickable = !isDisabled
        }
        // 禁用时设置透明度和不可点击
        holder.icon.alpha = if (isDisabled) 0.5f else 1f
        holder.title.alpha = if (isDisabled) 0.5f else 1f
        holder.container.isClickable = !isDisabled


        holder.container.setOnClickListener {
            if (isDisabled) {
                return@setOnClickListener
            }
            onItemClick(item, position)
        }
    }

    override fun getItemCount(): Int = items.size

    fun updateData(newItems: List<OfflineChannelModel>) {
        items = newItems
        notifyDataSetChanged()
    }

    fun selectItem(item: OfflineChannelModel): Boolean {
        return selectItems.any { it.id == item.id }
    }

    fun toggleSelectItem(item: OfflineChannelModel): Boolean {
        val isAlreadySelected = selectItems.any { it.id == item.id }
        if (isAlreadySelected) {
            selectItems.removeAll { it.id == item.id }
            notifyDataSetChanged()
            return false
        } else {
            // 不支持混合支付时最多选1个，支持时最多选2个
            if (selectItems.size >= (if (isSupportMixedPayment) 2 else 1)) return false


            // 已选1个时检查类型限制
            if (isSupportMixedPayment && selectItems.size == 1) {
                val existingItem = selectItems.first()
                val existingIsNonOnline =
                    existingItem.id != onlinePaymentId && existingItem.id != balancePaymentId
                val currentIsNonOnline = item.id != onlinePaymentId && item.id != balancePaymentId

                // 已选非线上支付时，新项必须是余额
                if (existingIsNonOnline && item.id != balancePaymentId) {
                    return false
                }
            }

            selectItems.add(item)
            notifyDataSetChanged()
            return true
        }
    }

    fun setSupportMixedPayment(support: Boolean) {
        isSupportMixedPayment = support
        notifyDataSetChanged()
    }

    // 新增：检查是否仅选中余额
    fun isOnlySelectBalance(): Boolean {
        return selectItems.size == 1 && selectItems.first().id == PaymentChannel.BALANCE.id
    }

    fun isSelectCash(): Boolean {
        return selectItems.filter { it.id == PaymentChannel.CASH.id }.isNotEmpty()
    }

    fun getCashItem(): OfflineChannelModel? {
        return items.firstOrNull { it.id == PaymentChannel.CASH.id }
    }

    fun getSelectItems(): List<OfflineChannelModel> {
        return selectItems
    }

    // 新增：检查是否组合支付
    fun isMixedPayment(): Boolean {
        return selectItems.size >= 2
    }

    //    查询已选择的支付方式中，排除余额和现金支付的其他支付方式
    fun getOtherSelectedPayments(): List<OfflineChannelModel> {
        return selectItems.filter { item ->
            item.id != PaymentChannel.BALANCE.id &&
                    item.id != PaymentChannel.CASH.id
        }
    }
}
