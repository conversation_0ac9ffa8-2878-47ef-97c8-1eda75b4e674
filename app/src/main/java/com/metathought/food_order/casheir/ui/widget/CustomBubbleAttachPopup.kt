package com.metathought.food_order.casheir.ui.widget

import android.content.Context
import android.graphics.Color
import android.util.Log
import android.widget.TextView
import com.lxj.xpopup.core.BubbleAttachPopupView
import com.lxj.xpopup.enums.PopupPosition
import com.lxj.xpopup.util.XPopupUtils
import com.metathought.food_order.casheir.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.Timer
import java.util.TimerTask


/**
 *<AUTHOR>
 *@time  2024/12/16
 *@desc
 **/

class CustomBubbleAttachPopup(context: Context?, var content: String?, var duration: Long? = 3000) :
    BubbleAttachPopupView(
        context!!
    ) {
    override fun getImplLayoutId(): Int {
        return R.layout.custom_bubble_attach_popup
    }

    private var timer: Timer? = null
    override fun onCreate() {
        super.onCreate()
        popupInfo.popupPosition = PopupPosition.Top
        setBubbleBgColor(Color.BLACK)
        setBubbleRadius(XPopupUtils.dp2px(context, 10f))
        setArrowWidth(XPopupUtils.dp2px(context, 8f))
        setArrowHeight(XPopupUtils.dp2px(context, 9f))
        setArrowRadius(XPopupUtils.dp2px(context, 2f))
        timer?.cancel()
        timer = Timer()
        timer?.schedule(object : TimerTask() {
            override fun run() {
                Timber.e("结束")
                timer?.cancel()
                timer = null
                runBlocking {
                    withContext(Dispatchers.Main) {
                        dismiss()
                    }
                }

            }
        }, duration!!)


        findViewById<TextView>(R.id.tvContent).text = content
    }

    override fun onDismiss() {
        super.onDismiss()
        timer?.cancel()
        timer = null
    }
}