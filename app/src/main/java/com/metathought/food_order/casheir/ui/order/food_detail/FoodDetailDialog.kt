package com.metathought.food_order.casheir.ui.order.food_detail

import android.annotation.SuppressLint
import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.widget.NestedScrollView
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.dboy.chips.ChipsLayoutManager
import com.denzcoskun.imageslider.constants.ScaleTypes
import com.denzcoskun.imageslider.models.SlideModel
import com.google.gson.Gson
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.SpecificationTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.order.Feed
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTag
import com.metathought.food_order.casheir.data.model.base.response_model.order.GoodsTagItem
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetGroup
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderMealSetGood
import com.metathought.food_order.casheir.database.dao.GoodsHelper
import com.metathought.food_order.casheir.database.dao.TakeOutPlatformToDiningHelper
import com.metathought.food_order.casheir.databinding.DialogFoodDetailBinding
import com.metathought.food_order.casheir.databinding.DialogSelectedTagPopWindowBinding
import com.metathought.food_order.casheir.extension.addMealSetTag
import com.metathought.food_order.casheir.extension.isInt
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.extension.setStrokeAndColor
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.extension.setVisibleInvisible
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.helper.OrderHelper
import com.metathought.food_order.casheir.helper.PopupWindowHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.GOOD_MAX_NUM
import com.metathought.food_order.casheir.ui.adapter.MainSpecificationAdapter
import com.metathought.food_order.casheir.ui.adapter.MealSetAdapter
import com.metathought.food_order.casheir.ui.adapter.SelectedTagPopWindowAdapter
import com.metathought.food_order.casheir.ui.adapter.ToppingsAdapter
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.order.MenuOrderViewModel
import com.metathought.food_order.casheir.ui.order.change_num.EditGoodNumDialog
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import com.metathought.food_order.casheir.ui.second_display.menu.food_detail.OrderFoodDetailDialog
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import androidx.core.graphics.toColorInt
import androidx.lifecycle.lifecycleScope
import com.metathought.food_order.casheir.data.model.base.response_model.cart.CartInfoResponse
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * 菜品详情弹窗
 *
 * @constructor Create empty Food detail dialog
 */
@AndroidEntryPoint
class FoodDetailDialog : BaseDialogFragment() {
    private var binding: DialogFoodDetailBinding? = null
    private var reserveButtonListener: (() -> Unit)? = null
    private var orderButtonListener: ((Int, ArrayList<Feed>?, ArrayList<GoodsTagItem>?, List<OrderMealSetGood>?) -> Unit)? =
        null
    private var cartCount = 1
    private val menuOrderViewModel: MenuOrderViewModel by viewModels()
    private var totalPriceFeed = 0.0
    private var totalPriceTags = 0.0
    private lateinit var content: Goods
    private var toppingsAdapter: ToppingsAdapter? = null
    private var specificationSubItemAdapter: MainSpecificationAdapter? = null
    private var mealSetAdapter: MealSetAdapter? = null
    private var diningStyle = 0

    private var errorMsg = ""

    private var menuOrderScreen: SecondaryScreenUI? = null

    private var isTakeOut = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogFoodDetailBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initEventBus()
        initData()
        initObserver()
        initListener()
    }

    private fun initEventBus() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    private fun initObserver() {
        menuOrderViewModel.goodsReserve.observe(viewLifecycleOwner) {
            when (it) {
                is ApiResponse.Loading -> {
                    binding?.apply {
                        pbLoadSpecification.setVisibleInvisible(true)
                        btnAdd.setEnableWithAlpha(false)
                        imgPlus.setEnableWithAlpha(false)
                    }
                }

                is ApiResponse.Success -> {
//                    Toast.makeText(context,"${it.data}",Toast.LENGTH_SHORT).show()
                    binding?.apply {
                        pbLoadSpecification.setVisibleGone(false)
                        btnAdd.setEnableWithAlpha(true)
                        imgPlus.setEnableWithAlpha(true)
                    }
                    (it.data.feedInfoList ?: listOf()).let { it -> updateUIFeed(it) }
                    (it.data.goodsTags ?: listOf()).let { it -> updateUIGoodTag(it) }

                    if (content.isMealSet()) {
                        updateMealSet()
                    }

                    getSecondScreen()?.initMealSet(content)
                    context?.let { it1 -> checkingShowingReachLayout(content, it1, cartCount) }
                }

                is ApiResponse.Error -> {
                    binding?.apply {
                        pbLoadSpecification.setVisibleGone(false)
                        btnAdd.setEnableWithAlpha(true)
                        imgPlus.setEnableWithAlpha(true)
                    }
                    (content.feeds ?: listOf()).let { it -> updateUIFeed(it) }
                    (content.tags ?: listOf()).let { it -> updateUIGoodTag(it) }

                    if (content.isMealSet()) {
                        updateMealSet()
                    }
                    getSecondScreen()?.initMealSet(content)
                    context?.let { it1 -> checkingShowingReachLayout(content, it1, cartCount) }

                    if (!it.message.isNullOrEmpty()) {
                        Toast.makeText(context, "${it.message}", Toast.LENGTH_SHORT).show()
                    }
                }

                else -> {
                    binding?.apply {
                        pbLoadSpecification.setVisibleGone(false)
                    }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.85).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.8).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    @SuppressLint("SetTextI18n")
    private fun initData() {
        content = Gson().fromJson<Goods>(arguments?.getString(CONTENT), Goods::class.java)

        menuOrderViewModel.getGoodsReserveDetail(content.diningStyle ?: 0, content.id)
        isTakeOut = diningStyle >= TakeOutPlatformToDiningHelper.BASE_INDEX
        binding?.apply {
            val listImage = arrayListOf<SlideModel>()
            if (content.infoPicUrls.isNullOrEmpty()) {
                if (content.picUrl.isNullOrEmpty()) {
                    listImage.add(SlideModel(R.drawable.icon_menu_default2))
                    imageSlider.setImageList(listImage, ScaleTypes.CENTER_CROP)
                } else {
                    listImage.add(SlideModel(content.picUrl))
                    imageSlider.setImageList(listImage, ScaleTypes.CENTER_CROP)
                }
            } else {
                val arraysImage = content?.infoPicUrls!!.split(";")
                for (imageUrl in arraysImage) {
                    listImage.add(SlideModel(imageUrl))
                }
                if (listImage.isEmpty())
                    listImage.add(SlideModel(R.drawable.icon_menu_default2))
                imageSlider.setImageList(listImage, ScaleTypes.CENTER_CROP)
            }
            tvDetailInfo.text = content.name
            if (content.discountRate != null) {
                val rate = content.discountRate
                tvDiscountRate.text =
                    "${if (rate == null) "" else "${if (rate.isInt()) rate.toInt() else rate}"}% OFF"
                tvDiscountRate.isVisible = true
            }
            if (MainDashboardFragment.CURRENT_USER?.getCurrentServiceChargePercentage() != null && MainDashboardFragment.CURRENT_USER?.getCurrentServiceChargePercentage() != 0 && content.serviceChargeWhitelisting != true && diningStyle != DiningStyleEnum.TAKE_AWAY.id && !isTakeOut
            ) {
                tvServiceChargePercentage.text = getString(
                    R.string.need_pay_for_service,
                    "${MainDashboardFragment.CURRENT_USER?.getCurrentServiceChargePercentage() ?: 0}%"
                )
                tvServiceChargePercentage.isVisible = true
            }

            if (content.isHasProcessed()) {
                val isShowVipPrice = content.isShowVipPrice()
                val isHasDiscountPrice = content.isHasDiscountPrice()
                Timber.e("isShowVipPrice :${isShowVipPrice}    isHasDiscountPrice:${isHasDiscountPrice}")
                tvVipPrice.isVisible = isShowVipPrice
                val sellPrice = content.getCalculateDiscountPrice()

                tvTotal.text = FoundationHelper.getPriceStrByUnit(
                    FoundationHelper.useConversionRatio,
                    sellPrice,
                    FoundationHelper.isKrh
                )

                if (isShowVipPrice) {
                    tvVipPrice.text =
                        "${content.getCalculateVipPrice()?.priceFormatTwoDigitZero2()}"
//                    tvOriginalPrice.isVisible = false
                } else {
//                    tvOriginalPrice.isVisible = isHasDiscountPrice == true
//                    tvOriginalPrice.text =
//                        "${content.getCalculateSellPrice()?.priceFormatTwoDigitZero2()}"
                }
                if (content.isTimePriceGood()) {
                    //显示时价标识
                    tvTimePriceSign.isVisible = true
                }
            } else {
                if (content.isToBeWeighed()) {
                    //称重菜
                    tvTotal.text = getString(R.string.to_be_weighed)
//                    tvOriginalPrice.isVisible = false
                    binding?.tvVipPrice?.isVisible = false
                }
                if (content.isTimePriceGood()) {
                    //时价菜
                    if (!content.isHasCompletePricing()) {
                        /**
                         *  还未设置价格显示时价
                         */
                        tvTotal.text = getString(R.string.time_price)
                    } else if (!content.isHasCompleteWeight()) {
                        /**
                         *  已设置价格还未称重 显示时价标签
                         */
                        tvTimePriceSign.isVisible = true
                    }
                }
            }


            val activityLabel = content.activityLabels?.firstOrNull()
            if (activityLabel != null) {
                tvDiscountActivity.setStrokeAndColor(color = activityLabel.color.toColorInt())
                tvDiscountActivity.setTextColor(activityLabel.color.toColorInt())
                tvDiscountActivity.text = activityLabel.name
                tvDiscountActivity.isVisible = true
            } else {
                tvDiscountActivity.isVisible = false
            }


            getSecondScreen()?.initData(
                listImage,
                content,
                tvServiceChargePercentage.text.toString(),
                tvDiscountRate.text.toString()
            )


            /**
             * 套餐展示
             */
            if (content.isMealSet()) {
                tvDetailInfo.addMealSetTag(requireContext())
            }

            if (isTakeOut) {
//                tvOriginalPrice.isVisible = false
                llDesc.isVisible = false
            }
        }
    }

    private fun getSecondScreen(): OrderFoodDetailDialog? {
        if (isTakeOut) {
            return null
        }
        return menuOrderScreen?.getMenuOrderDetailDialog()
    }


    private fun checkingShowingReachLayout(
        good: Goods,
        context: Context,
        cartCount: Int,
        isShowLimitLayout: Boolean? = null
    ) {
        binding?.apply {
            errorMsg = ""

//            //判断一下填的数量是不是大于 对应规格的数量剩余可点数量

            val record = good.id.let { GoodsHelper.get(it, diningStyle) }
            var maxNum = GOOD_MAX_NUM

            var canAddNum = maxNum - (record?.num ?: 0)
            if (diningStyle == DiningStyleEnum.PRE_ORDER.id) {
                //预定数量限制
                val restrictNum = (good.restrictNum
                    ?: 0)
                //如果有预定数量限制
                if (restrictNum > 0) {
                    maxNum = restrictNum
                    canAddNum = restrictNum - (record?.num ?: 0)
                }
            }

            Timber.e("购物车里有相同的商品 剩余可加数量  cartCount:${cartCount}  canAddNum:${canAddNum}  ")

            //如果加的数量 大于可加的数量
            imgPlus.setImageDrawable(
                ContextCompat.getDrawable(
                    context,
                    if (cartCount < canAddNum) R.drawable.ic_add else R.drawable.ic_add_disable
                )
            )

            layoutReachLimitPurchase.root.isVisible = false
            imgPlus.setEnableWithAlpha(true)

            if (cartCount > canAddNum) {
                //如果需要加购的数量大于可加购的数量 +号 禁用
                layoutReachLimitPurchase.tvReachLimit.text = getString(
                    R.string.you_have_reached_the_maximum_quantity_limit_of,
                    maxNum
                )
                layoutReachLimitPurchase.root.isVisible =
                    true
                imgPlus.setEnableWithAlpha(false)
            } else if (cartCount == canAddNum) {
                //如果需要加购的数量等于可加购的数量 点击+号的时候给上限提示
                errorMsg = getString(
                    R.string.you_have_reached_the_maximum_quantity_limit_of,
                    maxNum
                )
            } else {
                imgPlus.setEnableWithAlpha(!content.isToBeWeighed())
            }

            if (content.isMealSet()) {
                imgPlus.setEnableWithAlpha(!content.isContainWeightGoods())
                //如果加购数量没超过可加数量,切必选的都选了   按钮可点击
                btnAdd.setEnableWithAlpha(cartCount <= canAddNum && isSetMealSelectedAllMust() && isMealSetHasChooseDish())
            } else {
                //如果加购数量没超过可加数量  按钮可点击
                btnAdd.setEnableWithAlpha(cartCount <= canAddNum)
            }
        }
    }

    private fun update() {
        binding?.apply {
            if (!content.isToBeWeighed()) {

            }
        }
    }


    private fun initListener() {
        binding?.apply {

            btnClose.setOnClickListener {
                dismissAllowingStateLoss()
                //second
//                menuOrderScreen?.dismissDialog()
            }
            imgMinus.setOnClickListener {
                if (menuOrderViewModel.goodsReserve.value == ApiResponse.Loading) {
                    return@setOnClickListener
                }
                if (cartCount > 1)
                    cartCount--
                setValueAndHideMinusButton()
                calculateTotalPrice()
                context?.let { it1 -> checkingShowingReachLayout(content, it1, cartCount, true) }
            }
            imgPlus.setOnClickListener {
                if (menuOrderViewModel.goodsReserve.value == ApiResponse.Loading) {
                    return@setOnClickListener
                }
                if (errorMsg.isNotEmpty()) {
                    Toast.makeText(requireContext(), errorMsg, Toast.LENGTH_SHORT).show()
                    return@setOnClickListener
                }

                cartCount++
                context?.let { it1 ->
                    checkingShowingReachLayout(
                        content,
                        it1,
                        cartCount,
                        true
                    )
                }
                setValueAndHideMinusButton()
                calculateTotalPrice()

            }
            btnAdd.setOnClickListener {
                if (menuOrderViewModel.goodsReserve.value == ApiResponse.Loading) {
                    return@setOnClickListener
                }
//                Log.d("", "cartCount ${cartCount}")
                val orderMealSetGoodList = mutableListOf<OrderMealSetGood>()
                content.mealSetInfo?.mealSetGroupList?.forEach { mealSetGroup ->
                    mealSetGroup.mealSetGoodsList?.forEach { mealSetGood ->
                        mealSetGood.selectItems.forEach { item ->
                            orderMealSetGoodList.add(
                                OrderHelper.mealSetGoodsToOrderMealSetGood(
                                    mealSetGroup.id!!,
                                    mealSetGood,
                                    item,
                                    mealSetGood.pricingMethod,
                                    mealSetGood.weight,
                                    mealSetGood.weighingCompleted
                                )
                            )
                        }
                    }
                }
                orderButtonListener?.invoke(
                    cartCount,
                    toppingsAdapter?.getSelectFeed(),
                    specificationSubItemAdapter?.getSelectGoodsTag(),
                    orderMealSetGoodList
                )
                dismissAllowingStateLoss()

                //second
//                menuOrderScreen?.dismissDialog()
            }

            tvQTY.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    //称重菜不可以直接编辑数量
                    val isToBeWeighed = if (content.isMealSet()) {
                        content.isContainWeightGoods()
                    } else {
                        content.isToBeWeighed()
                    }
                    if (isToBeWeighed) {
                        return@isFastDoubleClick
                    }
                    val num = if (tvQTY.text.isEmpty()) 0 else tvQTY.text.toString().toInt()
                    val record = GoodsHelper.get(content.id, diningStyle)

                    var maxNum = GOOD_MAX_NUM
                    if (diningStyle == DiningStyleEnum.PRE_ORDER.id) {
                        //预定数量限制
                        if ((content.restrictNum ?: 0) > 0) {
                            maxNum = (content.restrictNum
                                ?: 0)
                        }
                    }
                    val remainingNum = maxNum - (record?.num ?: 0)

                    EditGoodNumDialog(requireContext()).showDialog(
                        num = num,
                        remainingNum = remainingNum,
                        zeroEnable = false,
                        isSoldOut = content.isSoldOut(),
                        isPreOrder = diningStyle == DiningStyleEnum.PRE_ORDER.id
                    ) {
                        Timber.e("== $it")
                        cartCount = it.toInt()

                        setValueAndHideMinusButton()
                        context?.let { it1 ->
                            checkingShowingReachLayout(
                                content,
                                it1,
                                cartCount,
                                true
                            )
                        }
                        calculateTotalPrice()
                    }
                }
            }

            nestedScrollView.setOnScrollChangeListener(NestedScrollView.OnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
                getSecondScreen()
                    ?.updateNestedScrollView(scrollX, scrollY)
            })
        }
    }

    private fun updateUIGoodTag(feeds: List<GoodsTag>) {
        val goodsTag = feeds.filter {
            !it.goodsTagItems.isNullOrEmpty()
        }

        for (resource in goodsTag) {
            when (resource.type) {
                SpecificationTypeEnum.FEATURE.id, SpecificationTypeEnum.SPECIFICATION.id -> resource.goodsTagItems?.firstOrNull()?.isCheck =
                    true
            }
        }

        binding?.apply {
            recyclerSpecification.adapter =
                context?.let { it ->
                    specificationSubItemAdapter = MainSpecificationAdapter(goodsTag, it) {
                        calculateGoodTagsPrice(goodsTag)
                        calculateTotalPrice()

                        //update second
                        getSecondScreen()
                            ?.updateSpecificationAdapter(it)
                    }
                    specificationSubItemAdapter
                }
            calculateGoodTagsPrice(goodsTag)
            calculateTotalPrice()

            getSecondScreen()?.initGoodsTag(goodsTag)
        }
    }

    private fun calculateGoodTagsPrice(goodsTag: List<GoodsTag>) {
        totalPriceTags = 0.0
        for (tag in goodsTag) {
            var totalEachPrice = 0.0
            for (tagItem in tag.goodsTagItems!!) {
                if (tagItem.isCheck == true) {
                    totalEachPrice = tagItem.price!!
                    totalPriceTags += totalEachPrice
                }
            }
        }
    }

    private fun updateUIFeed(feeds: List<Feed>) {
        binding?.apply {
            tvToppingsTitle.setVisibleGone(feeds.isNotEmpty())
            val chipsLayoutManager = ChipsLayoutManager.newBuilder(context).build()
            chipsLayoutManager.isAutoMeasureEnabled = true
            recyclerTopping.layoutManager = chipsLayoutManager
            recyclerTopping.adapter = context?.let { it ->
                toppingsAdapter = ToppingsAdapter(feeds, it) {
                    totalPriceFeed = 0.0
                    for (feed in feeds) {
                        var totalEachPrice = 0.0
                        if (feed.alreadyNum!! > 0) {
                            totalEachPrice = feed.alreadyNum!! * feed.sum!!
                        }
                        totalPriceFeed += totalEachPrice
                    }
                    calculateTotalPrice()

                    //update second
                    getSecondScreen()?.updateToppingAdapter(it)
                }

                toppingsAdapter
            }

            //update secondary
            getSecondScreen()?.initFeeds(feeds)
        }
    }


    private fun updateMealSet() {
        binding?.apply {
            rvMealSet.isVisible = true
            if (content.isFixedMealSet()) {
                //如果是必选套餐,这边默认把必选的东西选中
                content.mealSetInfo?.mealSetGroupList?.forEach { mealSetGroup ->
                    mealSetGroup.mealSetGoodsList?.forEach { mealSetGood ->
                        if ((mealSetGood.optionalSpec == false && mealSetGroup.repeatSelect == false) || (mealSetGood.optionalSpec == true && mealSetGood.tags.isNullOrEmpty())) {
                            //商户配置选中的才默认选中
                            if (!mealSetGood.isSoldOutOrTakeDown(content.mealSetInfo?.removalDoesNotAffectSale)) {
                                if (!mealSetGood.isToBeWeighed()) {
                                    mealSetGood.selectFixedTag(
                                        "modify",
                                    )
                                }
                            }
                        }
                    }
                }
            }

            rvMealSet.adapter =
                context?.let { it ->
                    mealSetAdapter =
                        MealSetAdapter(
                            content.mealSetInfo?.showGoodsPic,
                            content.mealSetInfo?.mealSetGroupList ?: listOf(),
                            content,
                            it,
                            onAddCallback = { mealSetGroup, groupIndex, goodIndex ->
                                MealSetTagSelectDialog.showDialog(
                                    parentFragmentManager,
                                    mealSetGroup,
                                    mealSetGroup.mealSetGoodsList!![goodIndex],
                                    mealSetGroup.repeatSelect  //false
                                ) {
                                    Timber.e("nestedScrollView ")
                                    mealSetAdapter?.notifyItemChanged(groupIndex)
                                    getSecondScreen()?.updateMealSet(groupIndex)
                                    calculateTotalPrice()
                                    context?.let { it1 ->
                                        checkingShowingReachLayout(
                                            content,
                                            it1,
                                            cartCount,
                                            true
                                        )
                                    }
                                }
                            },
                            onSubCallback = { mealSetGroup, groupIndex, goodIndex ->
                                MealSetTagDeleteDialog.showDialog(
                                    parentFragmentManager,
                                    mealSetGroup,
                                    mealSetGroup.mealSetGoodsList!![goodIndex],
                                    mealSetGroup.repeatSelect  //false
                                ) {
                                    mealSetAdapter?.notifyItemChanged(groupIndex)
                                    getSecondScreen()
                                        ?.updateMealSet(groupIndex)
                                    calculateTotalPrice()
                                    context?.let { it1 ->
                                        checkingShowingReachLayout(
                                            content,
                                            it1,
                                            cartCount,
                                            true
                                        )
                                    }
                                }
                            },
                            onUpdateCallback = { mealSetGroup, groupIndex, goodIndex ->
//                                    val scrollY = nestedScrollView.scrollY
                                // 更新 RecyclerView 的数据
                                mealSetAdapter?.notifyItemChanged(groupIndex)
//                                    // 滚动回原来的位置
//                                    nestedScrollView.postDelayed( {
//                                        nestedScrollView .scrollTo(0, scrollY)
//                                    },0)

                                getSecondScreen()?.updateMealSet(groupIndex)
                                calculateTotalPrice()
                                context?.let { it1 ->
                                    checkingShowingReachLayout(
                                        content,
                                        it1,
                                        cartCount,
                                        true
                                    )
                                }
                            },
                            onShowSelectedTag = { mealSetGroup, groupIndex, goodIndex, view ->
                                showSelectedTagPopWindow(
                                    mealSetGroup,
                                    groupIndex,
                                    goodIndex,
                                    view
                                )
                            })
                    mealSetAdapter
                }
            calculateTotalPrice()
            context?.let { it1 -> checkingShowingReachLayout(content, it1, cartCount, true) }
        }
    }

    private fun showSelectedTagPopWindow(
        mealSetGroup: MealSetGroup,
        groupIndex: Int,
        goodIndex: Int,
        view: View
    ) {
        val popupView = DialogSelectedTagPopWindowBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupView.root,
            view.measuredWidth,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        PopupWindowHelper.addPopupWindow(popupWindow)
        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.setOnDismissListener {
            PopupWindowHelper.deletePopupWindow(popupWindow)
            mealSetAdapter?.setCurrentGoodSelectIndex(-1)
            mealSetAdapter?.notifyItemChanged(groupIndex)
            getSecondScreen()?.updateMealSet(groupIndex)
            getSecondScreen()?.hideSelectedTagPopWindow()
        }
        popupWindow.contentView.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED)
        popupView.rvSelectedTag.adapter = SelectedTagPopWindowAdapter(
            mealSetGroup.mealSetGoodsList!![goodIndex],
            requireContext()
        ) {
            if (mealSetGroup.mealSetGoodsList[goodIndex].selectItems.isEmpty()) {
                popupWindow.dismiss()
            } else {
                mealSetAdapter?.setCurrentGoodSelectIndex(goodIndex)
            }

            mealSetAdapter?.notifyItemChanged(groupIndex)
            getSecondScreen()?.updateMealSet(groupIndex)
            getSecondScreen()?.hideSelectedTagPopWindow()
            getSecondScreen()?.setCurrentGood(goodIndex)
            Handler(Looper.getMainLooper()).postDelayed({
                if (isAdded && !isDetached) { // 确保Fragment仍然附加到Activity
                    getSecondScreen()?.showSelectedTagPopWindow(mealSetGroup, groupIndex, goodIndex)
                }
            }, 500)

            calculateTotalPrice()
            context?.let { it1 -> checkingShowingReachLayout(content, it1, cartCount, true) }

        }
        popupWindow.showAsDropDown(
            view,
        )
        getSecondScreen()?.setCurrentGood(goodIndex)

        Handler(Looper.getMainLooper()).postDelayed({
            if (isAdded && !isDetached) { // 确保Fragment仍然附加到Activity
                getSecondScreen()?.showSelectedTagPopWindow(mealSetGroup, groupIndex, goodIndex)
            }
        }, 500)
    }

    /**
     *  计算套餐金额
     *  套餐的最后价格=套餐基础价格+（子商品1的加价*子商品单位总量+子商品1的规格加价*子商品1配置的数量）*用户选择的子商品1数量+（子商品2的加价+子商品2的规格加价*子商品配置的数量）*用户选择的子商品2数量+以此类推
     */
    private fun calculateMealSetPrice() {
        //加价金额
        var addPrice = 0L
        content.mealSetInfo?.mealSetGroupList?.forEach { mealSetGroup ->
            mealSetGroup.mealSetGoodsList?.forEach { mealSetGood ->
                mealSetGood.selectItems.forEach { item ->
                    //子商品的加价
                    var priceMarkup = mealSetGood.priceMarkup ?: 0
                    if (mealSetGood.isToBeWeighed()) {
                        priceMarkup = priceMarkup.times(mealSetGood.weight ?: 0.0).toLong()
                    }
                    //子商品配置的数量
                    val num = mealSetGood.num ?: 0
                    //选择的子商品的规格加
                    var tagPrice = 0.0
                    item.selectTag.forEach { tag ->
                        if ((tag.price ?: 0.0) > 0.0) {
                            tagPrice += (tag.price ?: 0.0)
                        }
                    }
                    val price = (priceMarkup + tagPrice.times(num)).times(item.selectNum)
                    addPrice += (price.toLong())
                    Timber.e("mealSetGood:${mealSetGood.name} 单个子商品的加价  price:${price}")
                }
            }
        }
        Timber.e("套餐总的加价 --->${addPrice / 100}")
        val totalPrice =
            ((content.getCalculateDiscountPrice() ?: 0L) + addPrice).times(cartCount)
        val vipPrice = ((content.getCalculateVipPrice() ?: 0) + addPrice).times(cartCount)
//        val originalPrice = ((content.getCalculateSellPrice() ?: 0) + addPrice).times(cartCount)
        binding?.apply {
            tvTotal.text = FoundationHelper.getPriceStrByUnit(
                FoundationHelper.useConversionRatio,
                totalPrice,
                FoundationHelper.isKrh
            )
            tvVipPrice.text = vipPrice.priceFormatTwoDigitZero2()
//            tvOriginalPrice.text = originalPrice.priceFormatTwoDigitZero2()
            tvVipPrice.isVisible = false
//            tvOriginalPrice.isVisible = false
            if (content.isShowVipPrice()) {
                tvVipPrice.isVisible = true
            } else {
//                tvOriginalPrice.isVisible = content.isHasDiscountPrice()
            }

            if (isTakeOut) {
//                tvOriginalPrice.isVisible = false
            }
        }
    }


    @SuppressLint("SetTextI18n")
    private fun calculateTotalPrice() {
        content?.apply {
            if (content.isHasProcessed()) {
                binding?.apply {
                    if (content.isMealSet()) {
                        calculateMealSetPrice()
                    } else {
                        val isShowVipPrice = content.isShowVipPrice()
                        val isHasDiscountPrice = content.isHasDiscountPrice()
                        val totalPrice =
                            ((getCalculateSellPrice()?.plus(totalPriceFeed)
                                ?: 0.0) + totalPriceTags) * cartCount
                        val totalVipPrice =
                            ((getCalculateVipPrice()?.plus(totalPriceFeed)
                                ?: 0.0) + totalPriceTags) * cartCount
                        val totalDiscountPrice =
                            if (discountPrice != null) ((getCalculateDiscountPrice()?.plus(
                                totalPriceFeed
                            )
                                ?: 0.0) + totalPriceTags) * cartCount else null

                        tvVipPrice.isVisible = isShowVipPrice
                        val sellPrice =
                            (if (isHasDiscountPrice) totalDiscountPrice else totalPrice) ?: 0.0
                        if (isShowVipPrice) {
                            tvTotal.text = FoundationHelper.getPriceStrByUnit(
                                FoundationHelper.useConversionRatio,
                                sellPrice.toLong(),
                                FoundationHelper.isKrh
                            )
                            tvVipPrice.text =
                                "$${totalVipPrice?.priceDecimalFormatTwoDigitZero()}"
                        } else {
                            tvTotal.text = FoundationHelper.getPriceStrByUnit(
                                FoundationHelper.useConversionRatio,
                                sellPrice.toLong(),
                                FoundationHelper.isKrh
                            )
//                            tvOriginalPrice.text =
//                                "$${totalPrice?.priceDecimalFormatTwoDigitZero()}"
                        }
                    }
                }
            } else {
                if (content.isToBeWeighed()) {
                    //称重菜
                    binding?.apply {
                        tvVipPrice.isVisible = false
//                        tvOriginalPrice.isVisible = false
                        tvTotal.text = getString(R.string.to_be_weighed)
                    }

                }
                if (content.isTimePriceGood()) {
                    if (!content.isHasCompletePricing()) {
                        //时价菜
                        binding?.tvTotal?.text = getString(R.string.time_price)
                    } else if (!content.isHasCompleteWeight()) {
                        binding?.tvTimePriceSign?.isVisible = true
                    }
                }
            }

        }
    }

    /**
     * 套餐是否所有的必选都选了
     *
     */
    private fun isSetMealSelectedAllMust(): Boolean {
        var allSelect = true
        content.mealSetInfo?.mealSetGroupList?.forEach { mealSetGroup ->
            if (mealSetGroup.isRequiredSelect() == true && !mealSetGroup.isReachMinNum()) {
                allSelect = false
            }
        }
        return allSelect
    }

    /**
     * 套餐是否已经有选择
     *
     */
    private fun isMealSetHasChooseDish(): Boolean {
        var isChoose = false
        content.mealSetInfo?.mealSetGroupList?.forEach { mealSetGroup ->
            if (mealSetGroup.getSelectNum() > 0) {
                isChoose = true
            }
        }
        return isChoose
    }


    private fun setValueAndHideMinusButton() {
        binding?.apply {
            tvQTY.text = cartCount.toString()
            imgMinus.setVisibleInvisible(cartCount > 1)
        }
    }


    @Subscribe(threadMode = ThreadMode.ASYNC)
    fun onSimpleEventASYNC(event: SimpleEvent<Any>) {
        Timber.e("onSimpleEventASYNC  => ${event.eventType}")
        when (event.eventType) {
            SimpleEventType.TIME_PRICE_GOOD_CHANGE_PRICE -> {
                //查到菜品
                val data = event.data as? CartInfoResponse
                val good = data?.goodsList?.firstOrNull { it?.id == content.id }
                Timber.e("good =>${good?.name}")
                if (good != null) {
                    content.sellPrice = good?.sellPrice
                    content.vipPrice = good?.vipPrice
                    content.isProcessed = good?.isProcessed
                    content.pricingCompleted = good?.pricingCompleted
                    content.weighingCompleted = good?.weighingCompleted
                    viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) { calculateTotalPrice() }
                }

            }

            else -> {}
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        menuOrderScreen?.dismissDialog()
    }


    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    companion object {
        private const val AVAILABLE_TABLE_LIST = "AVAILABLE_TABLE_LIST"
        private const val CONTENT = "CONTENT"


        // start - viettran1 - AM -133 - 18/07/2023
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        // end - viettran1 - AM -133 - 18/07/2023
        fun showDialog(
            fragmentManager: FragmentManager,
            content: String? = null,
            diningStyleEnum: Int,
            orderButtonListener: ((Int, ArrayList<Feed>?, ArrayList<GoodsTagItem>?, List<OrderMealSetGood>?) -> Unit),
            reserveButtonListener: (() -> Unit),
            menuOrderScreen: SecondaryScreenUI? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(AVAILABLE_TABLE_LIST)
            if (fragment != null) return
            fragment = newInstance(
                diningStyleEnum = diningStyleEnum,
                iOrderListener = orderButtonListener,
                iCancelListener = reserveButtonListener,
                content = content,
                menuOrderScreen = menuOrderScreen
            )
            fragment.show(fragmentManager, AVAILABLE_TABLE_LIST)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(AVAILABLE_TABLE_LIST) as? FoodDetailDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            diningStyleEnum: Int,
            iCancelListener: (() -> Unit),
            iOrderListener: ((Int, ArrayList<Feed>?, ArrayList<GoodsTagItem>?, List<OrderMealSetGood>?) -> Unit),
            content: String? = null,
            menuOrderScreen: SecondaryScreenUI? = null
        ): FoodDetailDialog {
            val args = Bundle()
            args.putString(CONTENT, content)
            val fragment = FoodDetailDialog()
            fragment.reserveButtonListener = iCancelListener
            fragment.orderButtonListener = iOrderListener
            fragment.diningStyle = diningStyleEnum
            fragment.arguments = args
            fragment.menuOrderScreen = menuOrderScreen
            return fragment
        }
    }
}
