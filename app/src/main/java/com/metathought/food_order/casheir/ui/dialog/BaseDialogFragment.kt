package com.metathought.food_order.casheir.ui.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.content.DialogInterface
import android.hardware.display.DisplayManager
import android.util.DisplayMetrics
import android.view.Display
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.lifecycle.lifecycleScope
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.hideKeyboard2
import kotlinx.coroutines.launch
import net.yslibrary.android.keyboardvisibilityevent.KeyboardVisibilityEvent
import timber.log.Timber


/**
 *<AUTHOR>
 *@time  2024/10/28
 *@desc
 **/

open class BaseDialogFragment : DialogFragment() {

    private var isKeyboardOpen = false
    private var isCanClose = true

    fun delayInit() {
        isCanClose = false
        viewLifecycleOwner.lifecycleScope.launch {
            view?.postDelayed({
                Timber.e("可以关闭了")
                isCanClose = true
            }, 700)
        }
    }

    fun isShowKeyBoard(): Boolean {
        return isKeyboardOpen
    }


    fun openKeyBoardListener() {
        KeyboardVisibilityEvent.setEventListener(
            requireActivity(),
            viewLifecycleOwner,
        ) {
            if (it) {
                isKeyboardOpen = true
            } else {
                isKeyboardOpen = false
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    fun onTouchOutSide(baseView: View? = null, onKeyBoardOpenCallBack: (() -> Unit)? = null) {
        dialog?.window?.decorView?.setOnTouchListener { _, ev ->
            Timber.e("点点点  :${isKeyboardOpen}")
            if (isKeyboardOpen) {
//                ev?.hideKeyboard(baseView)
                onKeyBoardOpenCallBack?.invoke()
                ev?.hideKeyboard(dialog?.currentFocus)
                true
            } else {
                if (ev?.action == MotionEvent.ACTION_DOWN) {
                    if (baseView != null) {
                        val x = ev?.x ?: 0f
                        val y = ev?.y ?: 0f
                        val buttonLocation = IntArray(2)
                        baseView?.getLocationOnScreen(buttonLocation)
                        val buttonLeft = 0
                        val buttonTop = 0
                        val buttonRight = 0 + baseView.width
                        val buttonBottom = 0 + baseView.height

                        if (x < buttonLeft || x > buttonRight || y < buttonTop || y > buttonBottom) {
                            if (isCanClose) {
                                dismiss()

                            }
                        } else {
                            // 触摸点在按钮里面
                            true
                        }
                    }
                }
            }
            true
        }
    }

    fun showToast(buttonName: String) {
        Timber.e("#${buttonName}#")
        if (buttonName.isNotEmpty())
            Toast.makeText(requireActivity(), buttonName, Toast.LENGTH_LONG).show()
    }


    fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }

    fun hideKeyboard() {
        try {
//            val inputMethodManager =
//                requireActivity().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
//            inputMethodManager.hideSoftInputFromWindow(view?.windowToken, 0)
            val inputMethodManager =
                requireActivity().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            val view = dialog?.window?.decorView?.rootView
            if (view != null) {
                inputMethodManager.hideSoftInputFromWindow(view.windowToken, 0)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Timber.e(e, "隐藏键盘失败")
        }

    }

    /**
     * 关闭弹窗并隐藏键盘
     *
     */
    fun dismissCurrentDialog() {
        hideKeyboard2()
        hideKeyboard()
        dismissAllowingStateLoss()
    }

    override fun onDismiss(dialog: DialogInterface) {
        hideKeyboard2()
        super.onDismiss(dialog)
    }

    override fun onDestroy() {
        super.onDestroy()
    }
}