package com.metathought.food_order.casheir.ui.dialog

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.databinding.DialogPackPriceDetailBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.ui.adapter.PackPriceDetailItemAdapter
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PackPriceDetailDialog : BaseDialogFragment() {
    private var binding: DialogPackPriceDetailBinding? = null
    private var goods: List<Goods?>? = null
    private var conversionRatio: Long? = null
    private var takeOutPlatformModel: TakeOutPlatformModel? = null
    private var adapter: PackPriceDetailItemAdapter? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogPackPriceDetailBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
        initObserver()
    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.6).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.5).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    private fun initObserver() {

    }


    @SuppressLint("SetTextI18n")
    private fun initData() {
        binding?.apply {
            context?.let {

                goods?.let { it1 ->
                    val filterList = it1.filter { (it?.getCalculatePackingFee() ?: 0) > 0 }
                    adapter = PackPriceDetailItemAdapter(
                        ArrayList(filterList),
                        takeOutPlatformModel,
                        conversionRatio ?: FoundationHelper.useConversionRatio,
                    )
                    recyclerOrderedFood.adapter = adapter
//                    tvTotalPackPrice.text = "$${countTotalPrice().priceFormatTwoDigitZero()}"
                    tvTotalPackPrice.text = FoundationHelper.getPriceStrByUnit(
                        conversionRatio ?: FoundationHelper.useConversionRatio,
                        countTotalPrice().toLong(),
                        takeOutPlatformModel?.isKhr() == true
                    )
                }

            }
        }
    }

    fun countTotalPrice(): Int {
        var totalPrice = 0
        goods?.forEach {
            if ((it?.getCalculatePackingFee() ?: 0) > 0) {
                totalPrice += (it?.getCalculatePackingFee() ?: 0) * (it?.totalCount ?: 0)
            }
        }
        return totalPrice
    }


    private fun initListener() {
        binding?.apply {
            btnClose.setOnClickListener() {
                dismissAllowingStateLoss()
            }

        }
    }

    companion object {
        private const val TAG = "PackPriceDetailDialog"

        fun showDialog(
            fragmentManager: FragmentManager, goods: List<Goods?>?,
            conversionRatio: Long? = null,
            takeOutPlatformModel: TakeOutPlatformModel? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return

            fragment = newInstance(
                goods = goods,
                conversionRatio = conversionRatio,
                takeOutPlatformModel = takeOutPlatformModel
            )
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? PackPriceDetailDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            goods: List<Goods?>?, conversionRatio: Long? = null,
            takeOutPlatformModel: TakeOutPlatformModel? = null
        ): PackPriceDetailDialog {
            val args = Bundle()
            val fragment = PackPriceDetailDialog()
            fragment.goods = goods
            fragment.conversionRatio = conversionRatio
            fragment.takeOutPlatformModel = takeOutPlatformModel
            fragment.arguments = args
            return fragment
        }
    }

}
