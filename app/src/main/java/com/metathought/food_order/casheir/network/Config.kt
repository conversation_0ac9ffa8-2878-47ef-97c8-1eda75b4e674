package com.metathought.food_order.casheir.network

//API request timeout
const val TIME_OUT = 60000L

const val INVALID_PHONE_NUMBER_STATUS = "INVALID_PHONE_NUMBER_STATUS"
const val INVALID_EMAIL_STATUS = "INVALID_EMAIL_STATUS"
const val INVALID_LOAD_ID = "INVALID_LOAD_ID"
const val CUSTOMER_INFO_REQUIRE = 4001
const val TABLE_INFO_REQUIRE = 4002
const val SESSION_EXPIRE = 401

//菜品下架 带id的
const val GOODS_OFF_SHELF_2 = 7187

//优惠券已过期
const val COUPON_EXPIRED = 7197

//优惠券已被使用
const val COUPON_USED = 7198

//优惠券被其他订单锁定
const val COUPON_LOCKED = 7199

//优惠卷不可以
const val COUPON_UNEABLE = 7209

//订单信息变更
const val ORDER_INFO_CHANGE = 7261

const val GOODS_OFF_SHELF = 7173

const val ORDER_STATUS_ERROR = 7177

//分组上限
const val CLASSIFICATION_LIMIT_MIN_ERROR = 7619

//分组下限
const val CLASSIFICATION_LIMIT_MAX_ERROR = 7620

//当前订单已被合并或拆单
const val GOODS_HAS_BEEN_MERGE_OR_SPLIT = 7265

//外卖平台不存在
const val TAKE_OUT_PLATFORM_NOT_EXIST = 7626

/**
 * 菜品（单个规格小料等）最大可点数量
 */
const val GOOD_MAX_NUM = 999