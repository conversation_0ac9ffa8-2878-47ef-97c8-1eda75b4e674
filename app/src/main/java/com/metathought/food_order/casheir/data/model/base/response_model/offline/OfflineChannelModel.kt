package com.metathought.food_order.casheir.data.model.base.response_model.offline

import android.content.Context
import android.os.Parcelable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OfflinePaymentChannelEnum
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize
import java.util.Objects

@JsonClass(generateAdapter = true)
data class PaymentChannelResponse(
    @SerializedName("CASH_PAY")
    var cashPay: List<OfflineChannelModel>? = null,
    @SerializedName("ONLINE_PAY")
    var onlinePay: List<OfflineChannelModel>? = null,
    @SerializedName("STORE_BALANCE_PAY")
    var storeBalancePay: List<OfflineChannelModel>? = null
)

@Parcelize
data class OfflineChannelModel(
    @SerializedName("channelsName")
    var channelsName: String? = null,
    @SerializedName("conversionRatio")
    var conversionRatio: Int? = null,
    @SerializedName("id")
    var id: Int,
    @SerializedName("logoUrl")
    var logoUrl: String? = null,

    //是否显示 ,后台勾选
    @SerializedName("selected")
    var selected: Boolean? = false,

    ) : Parcelable {
    fun isCash(): Boolean {
        return id == OfflinePaymentChannelEnum.CASH.id
    }

    override fun hashCode(): Int {
        return Objects.hash(id)
    }

    override fun equals(other: Any?): Boolean {
        if (other === this) return true
        if (other !is OfflineChannelModel) return false
        return other.id == this.id
    }

    fun getRatio(): String {
        return "$1 = KHR${conversionRatio?.decimalFormatZeroDigit()}"
    }

    fun getSpannableStringBuilder(
        context: Context,
        color1: Int,
        color2: Int
    ): SpannableStringBuilder {
        var startStr = SpannableString("")
        var endStr = SpannableString("")
        if (id == OfflinePaymentChannelEnum.CASH.id) {
            startStr = SpannableString(context.getString(R.string.pay_by_cash))

        } else if (id == OfflinePaymentChannelEnum.ACCOUNTS_RECEIVABLE.id) {
            startStr =
                SpannableString(context.getString(R.string.accounts_receivable))
            endStr =
                SpannableString("(${context.getString(R.string.offline_payments)})")
        } else {
            startStr = SpannableString(channelsName)
            endStr =
                SpannableString("(${context.getString(R.string.offline_payments)})")
        }
        startStr.setSpan(
            ForegroundColorSpan(color1),
            0,
            startStr.length,
            SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        val secondSizeSpan = AbsoluteSizeSpan(12, true)
        endStr.setSpan(
            ForegroundColorSpan(color2),
            0,
            endStr.length,
            SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        endStr.setSpan(
            secondSizeSpan,
            0,
            endStr.length,
            SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        val spannableStringBuilder = SpannableStringBuilder()
        spannableStringBuilder.append(startStr)
        spannableStringBuilder.append(endStr)
        return spannableStringBuilder
    }
}
