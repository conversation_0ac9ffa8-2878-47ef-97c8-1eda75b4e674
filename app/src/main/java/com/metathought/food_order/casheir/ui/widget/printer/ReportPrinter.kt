package com.metathought.food_order.casheir.ui.widget.printer

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import androidx.core.view.isVisible
import com.github.alexzhirkevich.customqrgenerator.QrData
import com.github.alexzhirkevich.customqrgenerator.vector.QrCodeDrawable
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OfflinePaymentChannelEnum
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.ShiftReportPrint
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.RepaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterConfigInfo
import com.metathought.food_order.casheir.data.model.base.response_model.report.PaymentMethodReportResponse
import com.metathought.food_order.casheir.data.model.base.response_model.report.ProductReportResponse
import com.metathought.food_order.casheir.databinding.ClosingReportPrinterBinding
import com.metathought.food_order.casheir.databinding.CreditRecordPrinterBinding
import com.metathought.food_order.casheir.databinding.PaymentMethodPrinterBinding
import com.metathought.food_order.casheir.databinding.ProductReportPrinterBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.getStringByLocale
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero3
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero4
import com.metathought.food_order.casheir.extension.priceFormatZeroDigit
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.ui.adapter.PrintShiftHandoverOfflineRecordsAdapter
import com.metathought.food_order.casheir.ui.adapter.PrinterCreditOrderItemAdapter
import com.metathought.food_order.casheir.ui.adapter.PrinterPaymentMethodReportAdapter
import com.metathought.food_order.casheir.ui.adapter.PrinterProductReportAdapter
import com.metathought.food_order.casheir.ui.adapter.SalesItemOrdersDetailAdapter
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.app_dashbord.selectPrinter
import com.metathought.food_order.casheir.utils.BitmapUtil
import com.sunmi.printerx.enums.PrinterInfo
import timber.log.Timber
import java.math.BigDecimal
import java.nio.charset.Charset
import java.util.Locale


/**
 *<AUTHOR>
 *@time  2024/12/4
 *@desc
 **/

object ReportPrinter {
    private fun getPrinterEightyWidth(): Int {
        return if (Printer.isXPrinter()) {
            575
        } else {
            try {
                Timber.e(
                    "111111 ${
                        selectPrinter?.queryApi()?.getInfo(PrinterInfo.PAPER)
                    }"
                )
                selectPrinter?.queryApi()?.getInfo(PrinterInfo.PAPER)?.toInt() ?: 380
            } catch (e: Exception) {
                Timber.e(
                    "22222 ${
                        selectPrinter?.queryApi()?.getInfo(PrinterInfo.PAPER)
                    }"
                )
                380
            }
        }
    }

    private fun getPrinterWidth(): Int {
        return if (Printer.isXPrinter()) {
            380
        } else {
            try {
                selectPrinter?.queryApi()?.getInfo(PrinterInfo.PAPER)?.toInt() ?: 380
            } catch (e: Exception) {
                380
            }
        }
    }

    /**
     * 闭店报表
     *
     * @param context
     * @param printerConfigInfo
     * @param productReport
     * @return
     */
    fun initClosingReportTickerBitmap(
        context: Context,
        printerConfigInfo: PrinterConfigInfo?,
        shiftReport: ShiftReportPrint,
        printTamplateResponseItem: PrintTamplateResponseItem?,
    ): Bitmap? {
        var createBitmap: Bitmap? = null
        val isEightyWidth = printerConfigInfo?.isEightyWidth()
        var width = getPrinterWidth()
        if (isEightyWidth == true) {
            width = getPrinterEightyWidth()
        }
        //商品报表
        val binding = ClosingReportPrinterBinding.inflate(LayoutInflater.from(context))
        binding.apply {
            Timber.e("Locale.getDefault() ${Locale.getDefault()}")

            val lang = printTamplateResponseItem?.getLangList()?.firstOrNull() ?: "en"
            val language = Locale(lang.uppercase())
            binding.tvStoreName.text =
                MainDashboardFragment.CURRENT_USER?.getStoreNameByLan(language)

            binding.tvTitle.text = context.getStringByLocale(R.string.closing_report, language)
            binding.printTime.text = context.getStringByLocale(R.string.print_time, language)
            binding.staff.text = context.getStringByLocale(R.string.staff, language)
            binding.classStartTime.text =
                context.getStringByLocale(R.string.class_start_time, language)
            binding.classCloseTime.text =
                context.getStringByLocale(R.string.class_close_time, language)
            binding.itemInformation.text =
                context.getStringByLocale(R.string.print_product_info, language)
            binding.item.text = context.getStringByLocale(R.string.print_title_item_name, language)
            binding.itemQty.text = context.getStringByLocale(R.string.print_title_qty, language)
            binding.itemTotal.text =
                context.getStringByLocale(R.string.print_title_item_total_with_unit, language)
            binding.goodsTotalNum.text =
                context.getStringByLocale(R.string.printer_title_goods_total_num, language)
            binding.subtotal.text =
                context.getStringByLocale(R.string.print_title_subtotal, language)
            binding.discountAmount.text =
                context.getStringByLocale(R.string.print_report_discount_amount, language)
            binding.total.text = context.getStringByLocale(R.string.print_title_total, language)
            binding.onlinePayment.text =
                context.getStringByLocale(R.string.online_payment_amount, language)
            binding.offlinePayment.text =
                context.getStringByLocale(R.string.offline_payment_amount, language)
            binding.balancePayment.text =
                context.getStringByLocale(R.string.balance_receipts, language)
            binding.creditPayment.text =
                context.getStringByLocale(R.string.received_credit, language)

            binding.handoverCash.text = context.getStringByLocale(R.string.handover_cash, language)

            binding.openingCash.text = context.getStringByLocale(R.string.reserve_fund, language)
            binding.shiftExpenses.text =
                context.getStringByLocale(R.string.shift_expenses, language)

            binding.differenceAmount.text =
                context.getStringByLocale(R.string.difference_amount, language)

            binding.shiftBalance.text = context.getStringByLocale(R.string.shift_balance, language)
            binding.remark.text = context.getStringByLocale(R.string.remark, language)



            binding.tvPrintTime.text = shiftReport.printTime

            //员工名字
            binding.tvStaffName.text = shiftReport.employeeName
            //开班时间
            binding.tvClassStartTime.text = shiftReport.startTime
            //交班时间
            binding.tvClassCloseTime.text = shiftReport.endTime

            //商品列表
            val list = shiftReport.saleItemData?.salesItemOrdersDetailList ?: listOf()
            Timber.e("≈?.showGoodsReport : ${printTamplateResponseItem?.informationShow?.isShowGoodsReport()}")
            llItemInfo.isVisible =
                printTamplateResponseItem?.informationShow?.isShowGoodsReport() == true
            llGoodInfoTitle.isVisible =
                list.isNotEmpty() && printTamplateResponseItem?.informationShow?.isShowGoodsReport() == true
            recyclerView.isVisible =
                printTamplateResponseItem?.informationShow?.isShowGoodsReport() == true

            binding.recyclerView.adapter = SalesItemOrdersDetailAdapter(
                ArrayList(
                    list
                )
            )


            val totalGoodsNum = list.fold(0) { acc, element ->
                acc + (element.itemNum ?: 0)
            }

            binding.tvGoodsTotalNum.text = "$totalGoodsNum"

//            val totalAmount =
//                shiftReport.saleItemData?.totalAmount?.toBigDecimalOrNull() ?: BigDecimal.ZERO
//            val totalReceiveAmount =
//                shiftReport.saleItemData?.totalReceiveAmount?.toBigDecimalOrNull()
//                    ?: BigDecimal.ZERO
//            val discount = totalAmount - totalReceiveAmount
            //小计
            binding.tvSubtotal.text = "${shiftReport.subTotal?.toBigDecimalOrNull() ?: BigDecimal.ZERO}"
            //折扣金额
            val discount = shiftReport.discountAmount?.toBigDecimalOrNull() ?: BigDecimal.ZERO
            binding.tvDiscount.text = "${discount}"
            binding.llDiscount.isVisible = discount > BigDecimal.ZERO

            binding.llSubtotal.isVisible = binding.llDiscount.isVisible
            //总计
            binding.tvTotalPrice.text = "${shiftReport.grandTotal?.toBigDecimalOrNull() ?: BigDecimal.ZERO}"

            //线上收款
            val onlinePaidPrice = shiftReport.onlinePaidPrice
            binding.tvOnlinePaymentAmount.text = "$onlinePaidPrice"
            //线下收款
            val offlinePaidPrice = shiftReport.offlinePaidPrice
            binding.tvOfflinePaymentAmount.text = "$offlinePaidPrice"

            rvOfflineChannel.adapter =
                PrintShiftHandoverOfflineRecordsAdapter(
                    shiftReport.offlinePayMethodData ?: ArrayList()
                )

//            //现金收款
//            val cashPaidPrice = shiftReport.cashPaidPrice
//            binding.tvCashReceipts.text = "$cashPaidPrice"
            //余额收款
            val balancePaidPrice = shiftReport.balancePaidPrice
            binding.tvBalanceReceipts.text = "$balancePaidPrice"

            //挂账收款
            val creditPaidPrice = shiftReport.creditPaidPrice
            binding.tvCreditReceipts.text = "$creditPaidPrice"

            val handoverCash = shiftReport.usdAmount?.toBigDecimalOrNull() ?: BigDecimal.ZERO
            binding.tvHandoverCash.text = handoverCash.priceFormatTwoDigitZero2(
                "$"
            )
            val handoverCashKhr =
                (shiftReport.khrAmount?.toBigDecimalOrNull() ?: BigDecimal.ZERO).halfUp(0)

            binding.tvHandoverCashKHR.text = handoverCashKhr.priceFormatZeroDigit("៛")

            //备用金
            val openingCashUsd = shiftReport.openingCashUsd?.toBigDecimalOrNull() ?: BigDecimal.ZERO
            binding.tvReserveFundUSD.text = openingCashUsd.priceFormatTwoDigitZero2(
                "$"
            )
            val openingCashKhr =
                (shiftReport.openingCashKhr?.toBigDecimalOrNull() ?: BigDecimal.ZERO).halfUp(0)
            binding.tvReserveFundKHR.text = openingCashKhr.priceFormatZeroDigit("៛")

            //当班支出
            val amountPaidUsd = shiftReport.amountPaidUsd?.toBigDecimalOrNull() ?: BigDecimal.ZERO
            binding.tvShiftExpensesUSD.text = amountPaidUsd.priceFormatTwoDigitZero2(
                "$"
            )
            val amountPaidKhr =
                (shiftReport.amountPaidKhr?.toBigDecimalOrNull() ?: BigDecimal.ZERO).halfUp(0)
            binding.tvShiftExpensesKHR.text = amountPaidKhr.priceFormatZeroDigit("៛")

            //相差金额
            val discrepancyPrice =
                shiftReport.discrepancyPrice?.toBigDecimalOrNull() ?: BigDecimal.ZERO
            binding.tvDifferenceAmountUsd.text = discrepancyPrice.priceFormatTwoDigitZero2(
                "$"
            )

            //当班余额
            val balance = shiftReport.balance?.toBigDecimalOrNull() ?: BigDecimal.ZERO
            binding.tvShiftBalance.text = balance.priceFormatTwoDigitZero2(
                "$"
            )

            //备注
            binding.tvRemark.text = "${shiftReport.remark}"
            binding.llRemark.isVisible = !shiftReport.remark.isNullOrEmpty()

            if (isEightyWidth == true) {
                vFooter.thankYou.text = context.getString(R.string.print_footer_thank_you_eigthy)
            } else {
                vFooter.thankYou.text = context.getString(R.string.print_footer_thank_you)
                vFooter.root.setPadding(0, 0, 0, 80)
            }

            //380 58mm
            //80mm 575
            root.measure(
                View.MeasureSpec.makeMeasureSpec(
                    width,
                    View.MeasureSpec.EXACTLY
                ), View.MeasureSpec.makeMeasureSpec(
                    0,
                    View.MeasureSpec.UNSPECIFIED
                )
            )
            root.layout(0, 0, root.measuredWidth, root.measuredHeight)

            createBitmap =
                Bitmap.createBitmap(
                    root.measuredWidth,
                    root.measuredHeight,
                    Bitmap.Config.RGB_565
                )
            val canvas = Canvas(createBitmap!!).apply {
                drawColor(Color.WHITE)
            }
            root.draw(canvas)
        }
        return createBitmap
    }

    /**
     * 商品报表
     *
     * @param context
     * @param printerConfigInfo
     * @param productReport
     * @return
     */
    fun initProductReportTickerBitmap(
        context: Context,
        printerConfigInfo: PrinterConfigInfo?,
        productReport: ProductReportResponse?
    ): Bitmap? {
        var createBitmap: Bitmap? = null
        val isEightyWidth = printerConfigInfo?.isEightyWidth()
        var width = getPrinterWidth()
        if (isEightyWidth == true) {
            width = getPrinterEightyWidth()
        }
        //商品报表
        val binding = ProductReportPrinterBinding.inflate(LayoutInflater.from(context))
        binding.apply {
            Timber.e("Locale.getDefault() ${Locale.getDefault()}")
            binding.tvStoreName.text =
                MainDashboardFragment.CURRENT_USER?.getStoreNameByLan(Locale.getDefault())

            val startTime = productReport?.startTime?.split(" ") ?: listOf()
            val endTime = productReport?.endTime?.split(" ") ?: listOf()
            var time =
                "${productReport?.startTime} ${if (isEightyWidth == true) "" else "\n"}-${productReport?.endTime}"
            if (startTime.size > 1 && endTime.size > 1) {
                if (startTime.first() == endTime.first()) {
                    time =
                        "${startTime.first()} ${startTime.get(1)}${if (isEightyWidth == true) "" else "\n"}-${
                            endTime.get(1)
                        }"
                }
            }
            binding.tvTime.text = time

            binding.tvPrintTime.text = productReport?.printTime

            val list = productReport?.data?.salesItemOrdersDetailList ?: listOf()
            binding.recyclerView.adapter = PrinterProductReportAdapter(
                ArrayList(
                    list
                )
            )
            val totalGoodsNum = list.fold(0) { acc, element ->
                acc + (element.itemNum ?: 0)
            }

            val totalOrderNum = productReport?.data?.totalOrderNum ?: 0

            val totalAmount =
                productReport?.data?.totalAmount?.toBigDecimalOrNull() ?: BigDecimal.ZERO
            val totalReceiveAmount =
                productReport?.data?.totalReceiveAmount?.toBigDecimalOrNull() ?: BigDecimal.ZERO
            val discount = totalAmount - totalReceiveAmount
            binding.tvSubtotal.text =
                "$totalAmount"


            binding.tvDiscount.text =
                "$discount"
            binding.llDiscount.isVisible = discount > BigDecimal.ZERO

            binding.llSubtotal.isVisible = binding.llDiscount.isVisible

            binding.tvGoodsTotalNum.text = "$totalGoodsNum"

            binding.tvTotalOrderNum.text = "$totalOrderNum"

            binding.tvTotalPrice.text = "$totalReceiveAmount"

            if (isEightyWidth == true) {
                vFooter.thankYou.text = context.getString(R.string.print_footer_thank_you_eigthy)
            } else {
                vFooter.thankYou.text = context.getString(R.string.print_footer_thank_you)
                vFooter.root.setPadding(0, 0, 0, 80)
            }

            //380 58mm
            //80mm 575
            root.measure(
                View.MeasureSpec.makeMeasureSpec(
                    width,
                    View.MeasureSpec.EXACTLY
                ), View.MeasureSpec.makeMeasureSpec(
                    0,
                    View.MeasureSpec.UNSPECIFIED
                )
            )
            root.layout(0, 0, root.measuredWidth, root.measuredHeight)

            createBitmap =
                Bitmap.createBitmap(
                    root.measuredWidth,
                    root.measuredHeight,
                    Bitmap.Config.RGB_565
                )
            val canvas = Canvas(createBitmap!!).apply {
                drawColor(Color.WHITE)
            }
            root.draw(canvas)
        }
        return createBitmap
    }


    /**
     * 支付渠道报表
     *
     * @param context
     * @param printerConfigInfo
     * @param productReport
     * @return
     */
    fun initPaymentTickerBitmap(
        context: Context,
        printerConfigInfo: PrinterConfigInfo?,
        productReport: PaymentMethodReportResponse?
    ): Bitmap? {
        var createBitmap: Bitmap? = null
        val isEightyWidth = printerConfigInfo?.isEightyWidth()
        var width = getPrinterWidth()
        if (isEightyWidth == true) {
            width = getPrinterEightyWidth()
        }

        val binding = PaymentMethodPrinterBinding.inflate(LayoutInflater.from(context))
        binding.apply {
            Timber.e("Locale.getDefault() ${Locale.getDefault()}")
            binding.tvStoreName.text =
                MainDashboardFragment.CURRENT_USER?.getStoreNameByLan(Locale.getDefault())

            val startTime = productReport?.startTime?.split(" ") ?: listOf()
            val endTime = productReport?.endTime?.split(" ") ?: listOf()
            var time =
                "${productReport?.startTime} ${if (isEightyWidth == true) "" else "\n"}-${productReport?.endTime}"
            if (startTime.size > 1 && endTime.size > 1) {
                if (startTime.first() == endTime.first()) {
                    time =
                        "${startTime.first()} ${startTime.get(1)}${if (isEightyWidth == true) "" else "\n"}-${
                            endTime.get(1)
                        }"
                }
            }
            binding.tvTime.text = time

            binding.tvPrintTime.text = productReport?.printTime


            binding.recyclerView.adapter = PrinterPaymentMethodReportAdapter(
                ArrayList(
                    productReport?.data?.salesPayMethodReportDetails ?: listOf()
                )
            )

            binding.tvNum.text = "${productReport?.data?.totalOrderNum ?: 0}"

//            binding.tvNum.text = "${
//                (productReport?.data?.salesPayMethodReportDetails ?: listOf()).fold(0L) { acc, element ->
//                    acc + (element.orderNum ?: 0)
//                }
//            }"
//            val totalOnlineAmount =
//                productReport?.data?.totalOnlineAmount?.toBigDecimalOrNull() ?: BigDecimal.ZERO
//            val totalOfflineAmount =
//                productReport?.data?.totalOfflineAmount?.toBigDecimalOrNull() ?: BigDecimal.ZERO
//            productReport?.data?.totalOrderAmount = "117000"
            val totalOrderAmount =
                productReport?.data?.totalOrderAmount?.toBigDecimalOrNull() ?: BigDecimal.ZERO
//            Timber.e("${productReport?.data?.totalOrderAmount}   ${totalOrderAmount.divide(BigDecimal.valueOf(100))}  ${totalOrderAmount.divide(BigDecimal.valueOf(100)).halfUp(2)} ")
            binding.tvTotalPrice.text =
                "${totalOrderAmount.divide(BigDecimal.valueOf(100)).halfUp(2)}"


            if (isEightyWidth == true) {
                vFooter.thankYou.text =
                    context.getString(R.string.print_footer_thank_you_eigthy)
            } else {
                vFooter.thankYou.text = context.getString(R.string.print_footer_thank_you)
                vFooter.root.setPadding(0, 0, 0, 80)
            }

            //380 58mm
            //80mm 575
            root.measure(
                View.MeasureSpec.makeMeasureSpec(
                    width,
                    View.MeasureSpec.EXACTLY
                ), View.MeasureSpec.makeMeasureSpec(
                    0,
                    View.MeasureSpec.UNSPECIFIED
                )
            )
            root.layout(0, 0, root.measuredWidth, root.measuredHeight)

            createBitmap =
                Bitmap.createBitmap(
                    root.measuredWidth,
                    root.measuredHeight,
                    Bitmap.Config.RGB_565
                )
            val canvas = Canvas(createBitmap!!).apply {
                drawColor(Color.WHITE)
            }
            root.draw(canvas)
        }
        return createBitmap
    }


    fun initCreditRecordBitmap(
        context: Context,
        printerConfigInfo: PrinterConfigInfo?,
        printerOrderStatus: OrderedStatusEnum,
        repaymentResponse: RepaymentResponse
    ): Bitmap? {
        var createBitmap: Bitmap? = null
        val isEightyWidth = printerConfigInfo?.isEightyWidth()
        var width = getPrinterWidth()
        if (isEightyWidth == true) {
            width = getPrinterEightyWidth()
        }

        val binding = CreditRecordPrinterBinding.inflate(LayoutInflater.from(context))
        binding.apply {
            val logoBase64 = PrinterDeviceHelper.getLogo()
            Timber.d("logoBase64 $logoBase64")
            var blackBitmap: Bitmap? = null
            if (!logoBase64.isNullOrEmpty()) {
                val logoBitmap = BitmapUtil.base64ToBitmap(logoBase64)
                if (logoBitmap != null) {
                    ivLogo.setImageBitmap(logoBitmap)
                } else {
                    ivLogo.isVisible = false
                }
            } else {
                ivLogo.isVisible = false
            }
            val isUnpaidOrder = printerOrderStatus == OrderedStatusEnum.CREDIT_UNPAID

            tvFirstStoreName.text = MainDashboardFragment.CURRENT_USER?.getStoreNameByLan()

            tvOrderStatus.text =
                if (isUnpaidOrder) context.getString(R.string.credit_unpaid) else context.getString(
                    R.string.credit_paid
                )
            llRepaymentTime.isVisible = !isUnpaidOrder
            if (repaymentResponse.repaymentDate != null) {
                tvRepaymentTime.text = repaymentResponse.repaymentDate!!.formatDate()
            }

            tvNickname.text = repaymentResponse.nickName
            tvAccount.text = repaymentResponse.telephone

            if (isUnpaidOrder) {
                //未还款
                llCreditAmount.isVisible = true
                tvCreditAmount.text = repaymentResponse.amount?.priceFormatTwoDigitZero4()
            } else {
                llRepaymentAmount.isVisible = true
                tvRepaymentAmount.text = repaymentResponse.amount?.priceFormatTwoDigitZero4()
//                //支付渠道
                llPayChannel.isVisible = true
                tvPayChannel0.text =
                    repaymentResponse.getPaymentMethod(context, Locale.getDefault())
                tvPayChannel.text = repaymentResponse.amount?.priceFormatTwoDigitZero4()

                if (repaymentResponse.payType == PayTypeEnum.USER_BALANCE.id) {
                    llAccountBalance.isVisible = true
                    //账户余额
                    tvAccountBalance.text =
                        repaymentResponse.surplusStoreBalance?.priceFormatTwoDigitZero4()
                } else if (repaymentResponse.payType == PayTypeEnum.MIXED_PAYMENT.id) {
                    llPayChannel.isVisible = false
                    //组合支付
                    llPaidByBalance.isVisible = true
                    tvPaidByBalance.text =
                        repaymentResponse.balancePayAmount?.priceFormatTwoDigitZero4()
                    //账户余额
                    llAccountBalance.isVisible = true
                    tvAccountBalance.text =
                        repaymentResponse.surplusStoreBalance?.priceFormatTwoDigitZero4()

                    if (repaymentResponse.offlinePaymentChannelsId == OfflinePaymentChannelEnum.CASH.id.toString()) {
                        llPayByCash.isVisible = true
                        tvPayByCash0.text = repaymentResponse.getOfflinePayMethodByLocal(
                            context,
                            Locale.getDefault()
                        )
                        tvPayByCash.text = repaymentResponse.getReceiveAmountToTicket()
                        llChangeAmount.isVisible = true
                        tvChangeAmount.text = repaymentResponse.getChangeAmountToTicket()
                    } else {
                        llMixedPayment.isVisible = true
                        tvMixedPayChannel.text = repaymentResponse.getOfflinePayMethodByLocal(
                            context,
                            Locale.getDefault()
                        )
                        tvMixedPayAmount.text =
                            repaymentResponse.offlinePayAmount?.priceFormatTwoDigitZero4()
                    }
                } else {
                    if (repaymentResponse.payType == PayTypeEnum.CASH_PAYMENT.id
                        && repaymentResponse.offlinePaymentChannelsId == OfflinePaymentChannelEnum.CASH.id.toString()
                    ) {
                        llPayChannel.isVisible = false
                        llPayByCash.isVisible = true
                        tvPayByCash0.text =
                            repaymentResponse.getPaymentMethod(context, Locale.getDefault())
                        tvPayByCash.text = repaymentResponse.getReceiveAmountToTicket()
                        llChangeAmount.isVisible = true
                        tvChangeAmount.text = repaymentResponse.getChangeAmountToTicket()
                    }
                }
            }

            tvOrderNum.text = repaymentResponse.orderNum?.toString()
            recyclerView.adapter =
                PrinterCreditOrderItemAdapter(repaymentResponse.orderDetailList ?: emptyList())

            if (isUnpaidOrder) {
                llPaymentQrCode.isVisible = true
                val khqrCode = repaymentResponse.khqrCode
                if (!khqrCode?.qrcode.isNullOrEmpty()) {
                    ivOrderQr.setImageDrawable(
                        QrCodeDrawable(
                            QrData.Url(khqrCode?.qrcode ?: ""),
                            charset = Charset.forName("UTF-8")
                        )
                    )
                }
            }

            if (isEightyWidth == true) {
                vFooter.thankYou.text =
                    context.getString(R.string.print_footer_thank_you_eigthy)
            } else {
                vFooter.thankYou.text = context.getString(R.string.print_footer_thank_you)
                vFooter.root.setPadding(0, 0, 0, 80)
            }

            //380 58mm
            //80mm 575
            root.measure(
                View.MeasureSpec.makeMeasureSpec(
                    width,
                    View.MeasureSpec.EXACTLY
                ), View.MeasureSpec.makeMeasureSpec(
                    0,
                    View.MeasureSpec.UNSPECIFIED
                )
            )
            root.layout(0, 0, root.measuredWidth, root.measuredHeight)

            createBitmap =
                Bitmap.createBitmap(
                    root.measuredWidth,
                    root.measuredHeight,
                    Bitmap.Config.RGB_565
                )
            val canvas = Canvas(createBitmap!!).apply {
                drawColor(Color.WHITE)
            }
            root.draw(canvas)



            if (blackBitmap?.isRecycled == false) {
                blackBitmap?.recycle()
            }
        }
        return createBitmap
    }
}