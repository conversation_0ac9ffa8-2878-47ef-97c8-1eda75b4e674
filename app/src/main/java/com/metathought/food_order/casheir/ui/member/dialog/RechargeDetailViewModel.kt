package com.metathought.food_order.casheir.ui.member.dialog

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.member.ConsumerRechargeInfo
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.ui.ordered.coupon.CouponListViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class RechargeDetailViewModel
@Inject
constructor(private val repository: Repository) : ViewModel() {
    private val _uiState = MutableLiveData<UIModel>()

    val uiState get() = _uiState


     fun getOrderDetailInfo(id: String?) {
        viewModelScope.launch {
            emitUiState(ApiResponse.Loading)
            try {
                val response = repository.getOrderDetailInfo(id)
                emitUiState(response)
            } catch (e: Exception) {
                emitUiState(ApiResponse.Error(""))
            }
        }
    }


    private fun emitUiState(
        success: ApiResponse<ConsumerRechargeInfo>? = null,
    ) {
        val uiModel = UIModel(success)
        _uiState.postValue(uiModel)
    }

    data class UIModel(
        val response: ApiResponse<ConsumerRechargeInfo>?,
    )
}
