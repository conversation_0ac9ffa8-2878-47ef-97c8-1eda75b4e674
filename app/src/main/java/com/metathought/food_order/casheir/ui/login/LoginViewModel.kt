package com.metathought.food_order.casheir.ui.login

import android.content.Context
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.google.gson.Gson
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.data.model.base.request_model.UserLoginRequest
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.data.model.base.response_model.version.VersionCheckResponse
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.helper.VersionHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.utils.DisplayUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class LoginViewModel
@Inject
constructor(private val repository: Repository) : ViewModel() {
    private val _userLoginResponse = MutableLiveData<UserLoginResponse>()
    val userLoginResponse get() = _userLoginResponse
    private val _uiState = MutableLiveData<UIModel>()
    private val _versionResponse = MutableLiveData<ApiResponse<VersionCheckResponse>>()

    val uiState get() = _uiState
    val versionResponse get() = _versionResponse
    fun login(userName: String, password: String, isRememberPwd: Boolean) {
        viewModelScope.launch {
            emitUiState(ApiResponse.Loading)
            try {

                val userModel = UserLoginRequest(
                    username = userName,
                    password = password,
                )

                val userSession = repository.login(userModel)
                if (userSession is ApiResponse.Success) {

                    PreferenceDataStoreHelper.getInstance().apply {
                        putPreference(
                            PreferenceDataStoreConstants.DATA_STORE_KEY_LOGIN_ACCOUNT,
                            if (isRememberPwd) userName else ""
                        )
                    }
                    PreferenceDataStoreHelper.getInstance().apply {
                        putPreference(
                            PreferenceDataStoreConstants.DATA_STORE_KEY_LOGIN_PASSWORD,
                            if (isRememberPwd) password else ""
                        )
                    }
                    PreferenceDataStoreHelper.getInstance().apply {
                        putPreference(
                            PreferenceDataStoreConstants.DATA_STORE_KEY_LOGIN_REMEMBER,
                            isRememberPwd
                        )
                    }
                    PreferenceHelper.setCurrentTabId(-1)
                    PreferenceHelper.setCartDataVersion(0)

                    userSession.data.url?.let {
                        if (it.isNotEmpty()) {
                            val dp40 = DisplayUtils.dp2px(MyApplication.myAppInstance, 40F)
                            Glide.with(MyApplication.myAppInstance).load(it).diskCacheStrategy(
                                DiskCacheStrategy.ALL
                            ).downloadOnly(dp40, dp40)

                        }
                    }
                }
                emitUiState(userSession)
            } catch (e: Exception) {
                emitUiState(ApiResponse.Error(e.message))
            }
        }
    }

    fun checkVersion(context: Context) {
        viewModelScope.launch {
            try {
                val versionName = VersionHelper.getLocalVersionName(context)
                val response = repository.checkVersion(versionName, 1)
                if (response is ApiResponse.Success) {
                    _versionResponse.postValue(response)
                }
            } catch (e: PackageManager.NameNotFoundException) {
            }
        }
    }


    fun emitUiState(
        result: ApiResponse<UserLoginResponse>? = null
    ) {
        val uiModel = UIModel(result)
        _uiState.value = uiModel
    }

    data class UIModel(
        val result: ApiResponse<UserLoginResponse>?,
    )


}