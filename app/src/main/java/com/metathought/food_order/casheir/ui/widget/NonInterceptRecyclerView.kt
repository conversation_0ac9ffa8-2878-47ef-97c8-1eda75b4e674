package com.metathought.food_order.casheir.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.recyclerview.widget.RecyclerView

/**
 * 不拦截触摸事件的RecyclerView，允许事件传递给父视图
 */
class NonInterceptRecyclerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    override fun onInterceptTouchEvent(e: MotionEvent): <PERSON><PERSON>an {
        // 不拦截触摸事件，让事件传递给父视图
        return false
    }

    override fun onTouchEvent(e: MotionEvent): Boolean {
        // 不处理触摸事件
        return false
    }
}