package com.metathought.food_order.casheir.data.model.base.request_model.ordered


import com.google.gson.annotations.SerializedName
import java.math.BigDecimal

data class RepaymentRequest(
    @SerializedName("consumerId")
    var consumerId: Long? = null, //消费者id,也就是还款人
    @SerializedName("payType")
    var payType: Int? = null, //支付类型:1-线上支付;2-现金支付;3-用户余额支付;4-其他支付(外卖使用);5-组合支付
    @SerializedName("offlinePayChannelsId")
    var offlinePayChannelsId: Int? = null, //线下收款渠道id，线下支付
    @SerializedName("collectCash")
    var collectCash: Long? = null,    //收取现金 现金支付时使用,瑞尔
    @SerializedName("changeAmount")
    var changeAmount: Long? = null, //找零金额 现金支付时使用，瑞尔
    @SerializedName("collectCashDollar")
    var collectCashDollar: BigDecimal? = null,//收取现金 现金支付时使用,美元
    @SerializedName("changeAmountDollar")
    var changeAmountDollar: BigDecimal? = null,//找要金额 现金支付时使用，美元
    @SerializedName("balancePayAmount")
    var balancePayAmount: BigDecimal? = null,//v2.14版本，组合支付功能新增字段:余额支付金额
    @SerializedName("accountId")
    var accountId: String? = null, //员工、收银余额支付或者挂账时使用
    @SerializedName("paymentGateway")
    var paymentGateway: String? = null, //	支付网关，支付类型是线上支付时传递，默认是U-PAY 0 U-PAY 1 ABA,可用值:UPAY,ABA
    @SerializedName("isPosPrint")
    var isPosPrint: Boolean = true, //是否打印POS机票据，POS机支付时使用   false是要服务端打云打印
    @SerializedName("isRepayment")
    var isRepayment: Boolean = true, //当前操作是否还款
)