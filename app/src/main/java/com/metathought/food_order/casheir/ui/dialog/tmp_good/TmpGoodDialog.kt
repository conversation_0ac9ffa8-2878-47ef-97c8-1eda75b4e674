package com.metathought.food_order.casheir.ui.dialog.tmp_good

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.GoodTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.databinding.DialogTmpGoodBinding
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.extension.setVisibleInvisible
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.GOOD_MAX_NUM
import com.metathought.food_order.casheir.ui.adapter.TmpMenuAdapter
import com.metathought.food_order.casheir.ui.adapter.TmpMenuOrderFoodAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.dialog.ConfirmDialog
import com.metathought.food_order.casheir.ui.order.change_num.EditGoodNumDialog
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * Tmp good dialog
 *
 * @constructor 临时商品
 */
@AndroidEntryPoint
class TmpGoodDialog : BaseDialogFragment() {

    private var binding: DialogTmpGoodBinding? = null
    private var positiveButtonListener: ((List<Goods>) -> Unit)? = null

    private val tmpGoodViewModel: TmpGoodViewModel by viewModels()
    private var tmpMenuAdapter: TmpMenuAdapter? = null
    private var tmpMenuOrderFoodAdapter: TmpMenuOrderFoodAdapter? = null


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogTmpGoodBinding.inflate(layoutInflater)
        return binding?.root
    }

//    private fun initEventBus() {
//        if (!EventBus.getDefault().isRegistered(this)) {
//            EventBus.getDefault().register(this)
//        }
//    }
//
//    override fun onDestroy() {
//        EventBus.getDefault().unregister(this)
//        super.onDestroy()
//    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
//        initEventBus()
        initListener()
        initObserver()
        initView()

    }

    private fun initView() {
        binding?.apply {
            tmpMenuAdapter = TmpMenuAdapter(
                list = ArrayList(),
                context = requireContext(),
                onClickCallback = { goods, i ->
                    tmpGoodViewModel.plus(requireContext(), goods)
                    tmpMenuAdapter?.updateTotalCount(
                        goods,
                        tmpGoodViewModel.goodsList.toList()
                    )
                },
                onPlusCallback = { goods ->
                    tmpGoodViewModel.plus(requireContext(), goods)
                    tmpMenuAdapter?.updateTotalCount(
                        goods,
                        tmpGoodViewModel.goodsList.toList()
                    )
                    updateBtnStatus()
                },
                onEditCallback = { goods ->
                    EditTmpGoodDialog.showDialog(parentFragmentManager, goods) {
                        tmpGoodViewModel.getTmpGoodList()
                    }
                })
            recyclerViewMenu.adapter = tmpMenuAdapter


            tmpMenuOrderFoodAdapter =
                TmpMenuOrderFoodAdapter(arrayListOf(), context = requireContext()) { selectItem ->
                    updateAddGoodNumView(selectItem)
                }
            recyclerOrderedFood.adapter = tmpMenuOrderFoodAdapter
        }

        tmpGoodViewModel.getTmpGoodList()
        updateBtnStatus()
    }

    private fun initObserver() {
        tmpGoodViewModel.uiListState.observe(viewLifecycleOwner) { state ->
            if (state.response is ApiResponse.Success) {
                binding?.apply {
                    val list = state.response.data
                    layoutEmptyFood.root.isVisible = list.isEmpty()

                    list.forEach {
                        it.goodsType = GoodTypeEnum.TEMPORARY.id
                    }

                    tmpMenuAdapter?.updateItems(ArrayList(list))

                    tmpGoodViewModel.updateCartGoodInfo(list)

                    EventBus.getDefault()
                        .post(SimpleEvent(SimpleEventType.UPDATE_TMP_GOOD_INFO, list))
                }
            }
        }

        tmpGoodViewModel.uiCartModel.observe(viewLifecycleOwner) {
            tmpMenuOrderFoodAdapter?.updateItems((it.goodsList ?: mutableListOf()).toList())
            val index =
                it.goodsList?.indexOfFirst { it.id == tmpMenuOrderFoodAdapter?.getSelectItem()?.id }
                    ?: -1
            tmpMenuAdapter?.setCartGoods(goodsList = (it.goodsList ?: mutableListOf()).toList())
            //更新临时菜单上 红点数量
            if (tmpMenuOrderFoodAdapter?.getSelectItem() != null) {
                tmpMenuAdapter?.updateTotalCount(
                    tmpMenuOrderFoodAdapter?.getSelectItem()!!,
                    (it.goodsList ?: mutableListOf()).toList()
                )
            }
            if (index == -1) {
                tmpMenuOrderFoodAdapter?.updateSelectItem(null)
                updateAddGoodNumView(null)
            } else {
                tmpMenuOrderFoodAdapter?.updateSelectItem(it.goodsList!![index])
                updateAddGoodNumView(it.goodsList!![index])
            }

            if (it.goodsList.isNullOrEmpty()) {
                tmpMenuAdapter?.notifyDataSetChanged()
            }
            binding?.apply {
                tvDeleteAll.setCompoundDrawablesWithIntrinsicBounds(
                    if (it.goodsList.isNullOrEmpty()) 0 else R.drawable.ic_trash,
                    0,
                    0,
                    0
                )
            }

        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.85).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.85).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }


    private fun initListener() {
        binding?.apply {

            topBar.getCloseBtn()?.setOnClickListener {
                dismissCurrentDialog()
            }

            tvDeleteAll.setOnClickListener {
                if (tmpGoodViewModel.goodsList.isNotEmpty()) {
                    ConfirmDialog.showDialog(
                        parentFragmentManager,
                        content = getString(
                            R.string.remove_num_items,
                            tmpGoodViewModel.goodsList.size
                        ),
                        positiveButtonTitle = getString(R.string.remove),
                        negativeButtonTitle = getString(R.string.no),
                    ) {
                        tmpGoodViewModel.removeAllGood()
                    }
                }
            }

            btnYes.setOnClickListener {
                positiveButtonListener?.invoke(tmpGoodViewModel.goodsList)
//                dismissAllowingStateLoss()
            }

            btnAddTmpGood.setOnClickListener {
                EditTmpGoodDialog.showDialog(parentFragmentManager) {
                    tmpGoodViewModel.getTmpGoodList()
                }
            }

            btnAdd.setOnClickListener {
                SingleClickUtils.isFastDoubleClick(200) {
                    if (tmpMenuOrderFoodAdapter?.getSelectItem() != null) {
                        tmpGoodViewModel.plus(
                            requireContext(),
                            tmpMenuOrderFoodAdapter!!.getSelectItem()!!
                        )
                    }
                }
            }

            btnReduce.setOnClickListener {
                SingleClickUtils.isFastDoubleClick(200) {
                    if (tmpMenuOrderFoodAdapter?.getSelectItem() != null) {
                        tmpGoodViewModel.sub(tmpMenuOrderFoodAdapter!!.getSelectItem()!!)
                    }
                }
            }

            tvGoodNum.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    val selectGood = tmpMenuOrderFoodAdapter?.getSelectItem()
                    if (selectGood != null) {
                        modifyGoodNum(selectGood)
                    }

                }
            }
        }
        openKeyBoardListener()
        onTouchOutSide(binding?.root)
    }

    private fun modifyGoodNum(good: Goods) {

        val maxNum = GOOD_MAX_NUM

        //剩余最多可加数量
        val remainingNum = maxNum

        EditGoodNumDialog(requireContext()).showDialog(
            num = good.totalCount ?: 0,
            remainingNum = remainingNum,
            zeroEnable = true,
            isSoldOut = good?.isSoldOut() == true
        ) { num ->
            tmpGoodViewModel.updateGoodsNum(good, num)
        }
    }

    private fun updateBtnStatus() {
        binding?.apply {
            btnYes.setEnableWithAlpha(!tmpGoodViewModel.goodsList.isNullOrEmpty())

            if (tmpGoodViewModel.goodsList.isEmpty()) {
                btnAdd.isEnabled = false
                btnReduce.isEnabled = false
                tvGoodNum.isEnabled = false
                tvGoodNum.text = ""
            } else {
                val item = tmpMenuOrderFoodAdapter?.getSelectItem()
                btnAdd.isEnabled = false
                btnReduce.isEnabled = false
                tvGoodNum.isEnabled = false
                if (item != null) {
                    val maxNum = GOOD_MAX_NUM
                    val canAddNum = maxNum - (item.totalCount ?: 0)
                    btnAdd.isEnabled = canAddNum > 0
                    btnReduce.isEnabled = (item.totalCount ?: 0) > 0
                    tvGoodNum.isEnabled = true
                }
            }

            layoutHeader.setVisibleInvisible(tmpGoodViewModel.goodsList.isNotEmpty())
            layoutEmpty.let {
                it.tvEmptyText.text = getString(R.string.please_select_item)
                it.root.setVisibleInvisible(tmpGoodViewModel.goodsList.isEmpty())
                it.imgError.setImageDrawable(context?.let { it1 ->
                    ContextCompat.getDrawable(
                        it1, R.drawable.ic_empty_food_order
                    )
                })
            }

            vTopLine.isVisible = tmpGoodViewModel.goodsList.isNotEmpty()
            layoutMainOrdered.setBackgroundResource(if (tmpGoodViewModel.goodsList.isEmpty()) R.drawable.background_round_top_trasparent else R.drawable.background_round_top_white)
            recyclerOrderedFood.setVisibleInvisible(tmpGoodViewModel.goodsList.isNotEmpty())
        }
    }

    private fun updateAddGoodNumView(good: Goods?) {
        binding?.apply {
            tvGoodNum.text = if (good != null) "${good.totalCount}" else ""
        }
        updateBtnStatus()
    }

//    @Subscribe(threadMode = ThreadMode.ASYNC)
//    fun onSimpleEventASYNC(event: SimpleEvent<Any>) {
//        when (event.eventType) {
//            SimpleEventType.UPDATE_TMP_GOOD_INFO -> {
//                requireActivity().runOnUiThread {
//
//
//                }
//            }
//
//            else -> {
//
//            }
//        }
//    }

    companion object {
        private const val TAG = "TmpGoodDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            positiveButtonListener: ((List<Goods>) -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(positiveButtonListener)
            fragment.show(fragmentManager, TAG)
        }

        fun getCurrentTmpGoodDialog(fragmentManager: FragmentManager): TmpGoodDialog? {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? TmpGoodDialog
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? TmpGoodDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            positiveButtonListener: ((List<Goods>) -> Unit),
        ): TmpGoodDialog {
            val args = Bundle()
            val fragment = TmpGoodDialog()
            fragment.arguments = args
            fragment.positiveButtonListener = positiveButtonListener
            return fragment
        }
    }

}
