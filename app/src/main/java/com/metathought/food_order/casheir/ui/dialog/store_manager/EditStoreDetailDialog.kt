package com.metathought.food_order.casheir.ui.dialog.store_manager

import android.Manifest
import android.R.attr
import android.app.Activity.RESULT_OK
import android.content.ContentUris
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.DocumentsContract
import android.provider.MediaStore
import android.text.InputFilter
import android.text.Spanned
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup

import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.login.StoreInfoResponse
import com.metathought.food_order.casheir.databinding.DialogEditStoreDetailBinding
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.extension.hideKeyboard2
import com.metathought.food_order.casheir.extension.setNumberRange
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.utils.FileUtil
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import org.greenrobot.eventbus.EventBus
import timber.log.Timber
import java.io.File


/**
 *  门店信息编辑
 *
 * @constructor Create empty Edit store detail dialog
 */
@AndroidEntryPoint
class EditStoreDetailDialog : BaseDialogFragment() {

    companion object {
        private const val TAG = "EditStoreDetailDialog"
        private const val REQUEST_CODE_PICK_IMAGE = 1001
        private const val REQUEST_CODE_READ_PHOTO = 1002


        fun showDialog(
            fragmentManager: FragmentManager,
            storeInfoResponse: StoreInfoResponse? = null,
            refresh: (() -> Unit)? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(storeInfoResponse)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? EditStoreDetailDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            storeInfoResponse: StoreInfoResponse? = null,
            refresh: (() -> Unit)? = null
        ): EditStoreDetailDialog {
            val args = Bundle()
            val fragment = EditStoreDetailDialog()
            fragment.storeInfoResponse = storeInfoResponse?.copy()
            fragment.refresh = refresh
            fragment.arguments = args
            return fragment
        }
    }

    private var storeInfoResponse: StoreInfoResponse? = null

    private var binding: DialogEditStoreDetailBinding? = null

    private val storeManagerViewModel: StoreManagerViewModel by viewModels()

    //    private var dateFormat: String? = null
//    private var acceptTime: Int? = null
    private var refresh: (() -> Unit)? = null
    private val searchRunnable = Runnable {
        try{
            binding?.apply {
                if (!edtLatitude.text.isNullOrEmpty() && !edLongitude.text.isNullOrEmpty()) {
                    storeManagerViewModel.requestGeocodeDataWithRetrofit(
                        "${edLongitude.text},${edtLatitude.text}"
                    )
                }
            }
        }catch (e:Exception){

        }

    }

    private fun postSearch(duration: Int) {
        binding?.apply {
            edtLocation.removeCallbacks(searchRunnable)
            if (duration <= 0) {
                searchRunnable.run()
            } else {
                edtLocation.postDelayed(searchRunnable, duration.toLong())
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogEditStoreDetailBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        initView()
        initListener()
        initObserver()
        openKeyBoardListener()
        onTouchOutSide(binding?.root)
    }

    private fun initView() {
        binding?.apply {
            storeManagerViewModel.headUrl = storeInfoResponse?.url
            updateHeadView()

            edtName.setText(storeInfoResponse?.name)
            edtName.setSelection(edtName.length())

            edtIntro.setText(storeInfoResponse?.description)
            edtIntro.setSelection(edtIntro.length())

            edtPhone.setText(storeInfoResponse?.telephons)
            edtPhone.setSelection(edtPhone.length())

            edLongitude.setText("${storeInfoResponse?.lng ?: ""}")

            edtLatitude.setText("${storeInfoResponse?.lat ?: ""}")

            edtLocation.setText(storeInfoResponse?.address)

            edtLocation.setText(storeInfoResponse?.address)
            edtLocation.setSelection(edtLocation.length())

            switchDistance.setOpen(storeInfoResponse?.limitOrderDistance)
            llLocationDistance.isVisible = storeInfoResponse?.limitOrderDistance ?: false


            edtLocationDistance.setText("${storeInfoResponse?.effectiveDistance ?: "200"}")
            edtLocationDistance.setSelection(edtLocationDistance.length())




            postSearch(500)
        }
    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissCurrentDialog()
            }
            ivImgClose.setOnClickListener {

                storeManagerViewModel.headUrl = ""
                updateHeadView()
            }

            ivLogo.setOnClickListener {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    // 当前设备运行在Android 13 (API level 33) 或以上版本
                    // 在这里执行针对Android 13的代码
                    pickImageFromGallery()
                } else {
                    // 当前设备运行在Android 13以下版本
                    // 在这里执行针对旧版本的代码
//                    rxPermissions?.requestEachCombined(
//                        Manifest.permission.READ_EXTERNAL_STORAGE,
//                        Manifest.permission.WRITE_EXTERNAL_STORAGE,
//                    )?.subscribe({ permissions ->
//                        if (permissions.granted) {
//                            pickImageFromGallery()
//                        }
//                    }, {})

                    if (ContextCompat.checkSelfPermission(
                            requireContext(),
                            Manifest.permission.READ_EXTERNAL_STORAGE
                        )
                        != PackageManager.PERMISSION_GRANTED
                    ) {
                        // 如果没有权限，请求权限
                        ActivityCompat.requestPermissions(
                            requireActivity(),
                            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE),
                            REQUEST_CODE_READ_PHOTO
                        );
                    } else {
                        // 已有权限，继续下一步操作
                        pickImageFromGallery();
                    }
                }
            }

            switchDistance.setSwitchListener {
                hideKeyBoardAndRemoveFocus()
                llLocationDistance.isVisible = it
                if (it) {
                    if (edLongitude.text.isNullOrEmpty() || edtLatitude.text.isNullOrEmpty()) {
                        Toast.makeText(
                            context,
                            "${getString(R.string.please_input_lon_lat)}",
                            Toast.LENGTH_SHORT
                        ).show()
                        llLocationDistance.isVisible = false
                        switchDistance.setOpen(false)
                    }
                }
            }

            edtName.addTextChangedListener {
                checkStoreName()
            }

            edtPhone.addTextChangedListener {
//                checkStorePhone()
            }
            val inputFilter = object : InputFilter {
                override fun filter(
                    source: CharSequence,
                    start: Int,
                    end: Int,
                    dest: Spanned,
                    dstart: Int,
                    dend: Int
                ): CharSequence? {
                    val allowedChars = "-0123456789."
                    val builder = StringBuilder()
                    for (i in 0 until source.length) {
                        if (allowedChars.contains(source[i])) {
                            builder.append(source[i])
                        }
                    }
                    return if (builder.toString() == source.toString()) {
                        null
                    } else {
                        ""
                    }
                }
            }

            edLongitude.filters = arrayOf(inputFilter)
            edLongitude.addTextChangedListener {
                postSearch(500)
            }

            edtLatitude.filters = arrayOf(inputFilter)
            edtLatitude.addTextChangedListener {
                postSearch(500)
            }

            edtLocation.addTextChangedListener {
                checkStoreLocation()
            }

            edtLocationDistance.setNumberRange(0, 3000)
            edtLocationDistance.addTextChangedListener {
                checkLocationDistance()
            }


            btnDone.setOnClickListener {
                if (storeInfoResponse == null) {
                    return@setOnClickListener
                }
                SingleClickUtils.isFastDoubleClick {

                    if (!checkStoreName() || !checkHead() || !checkLocationDistance() || !checkStoreLocation()) {
                        Timber.e("没通过校验")
                        return@isFastDoubleClick
                    }
                    if (switchDistance.isSwitchOpen() && (edLongitude.text.isNullOrEmpty() || edtLatitude.text.isNullOrEmpty())) {
                        Toast.makeText(
                            context,
                            getString(R.string.please_input_lon_lat),
                            Toast.LENGTH_SHORT
                        ).show()
                        return@isFastDoubleClick
                    }
                    Timber.e("通过校验")
//                    return@isFastDoubleClick

                    storeInfoResponse?.url = storeManagerViewModel.headUrl
                    storeInfoResponse?.name = edtName.text.toString()
                    storeInfoResponse?.description = edtIntro.text.toString()
                    storeInfoResponse?.telephons = edtPhone.text.toString()
                    storeInfoResponse?.address = edtLocation.text.toString()


                    storeInfoResponse?.limitOrderDistance = switchDistance.isSwitchOpen()
                    if (storeInfoResponse?.limitOrderDistance == true) {
                        if (edtLocationDistance.text.isNullOrEmpty()) {
                            return@isFastDoubleClick
                        }
                        storeInfoResponse?.effectiveDistance =
                            edtLocationDistance.text.toString().toInt()
                    }


                    storeInfoResponse?.lng = edLongitude.text.toString().toDoubleOrNull()
                    storeInfoResponse?.lat = edtLatitude.text.toString().toDoubleOrNull()


                    storeManagerViewModel.saveStoreInfo(storeInfoResponse!!)
                }
            }
        }
    }

    private fun hideKeyBoardAndRemoveFocus() {
        binding?.apply {
            hideKeyboard2()
            rootView.requestFocus()
        }
    }


    private fun initObserver() {
        storeManagerViewModel.uiAddressState.observe(viewLifecycleOwner) { address ->
            binding?.apply {
                tvLocation.text = address
                tvLocation.isVisible = address.isNotEmpty()
            }

        }
        storeManagerViewModel.uiSaveState.observe(viewLifecycleOwner) { state ->
            if (state is ApiResponse.Success) {
                EventBus.getDefault().post(SimpleEvent(SimpleEventType.UPDATE_STORE, null))
                dismissCurrentDialog()
            } else if (state is ApiResponse.Error) {
                if (!state.message.isNullOrEmpty()) {
                    Toast.makeText(context, "${state.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
        storeManagerViewModel.uiUpLoadState.observe(viewLifecycleOwner) { data ->
            if (data != null) {
                binding?.apply {
                    pbUpload.isVisible = data.isUploading
                }
                updateHeadView()
            }
        }
    }

    //更新头像
    private fun updateHeadView() {
        binding?.apply {
            if (storeManagerViewModel.headUrl.isNullOrEmpty()) {
                ivLogo.setImageResource(R.drawable.ic_photo_add)
                ivImgClose.isVisible = false
            } else {
                Glide.with(requireActivity()).load(storeManagerViewModel.headUrl)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .placeholder(R.color.color_e5e5e5)
                    .transition(DrawableTransitionOptions.withCrossFade())
                    .error(R.color.color_e5e5e5)
                    .into(ivLogo)
                ivImgClose.isVisible = true
            }
        }
    }

    private fun checkHead(): Boolean {

        return !storeManagerViewModel.headUrl.isNullOrEmpty()
    }

    /**
     * 校验店名
     *
     * @return
     */
    private fun checkStoreName(): Boolean {
        var isPassCheck = false
        binding?.apply {
            textInputLayoutName.error =
                if (edtName.text.isNullOrEmpty()) getString(R.string.please_input_store_name) else ""
            isPassCheck = textInputLayoutName.error.isNullOrEmpty()
        }
        return isPassCheck
    }

    /**
     * 校验电话
     *
     * @return
     */
    private fun checkStorePhone(): Boolean {
        var isPassCheck = false
        binding?.apply {
            textInputLayoutPhone.error =
                if (edtPhone.text.isNullOrEmpty()) getString(R.string.please_input_store_phone) else ""
            isPassCheck = textInputLayoutPhone.error.isNullOrEmpty()
        }
        return isPassCheck
    }

    /**
     * 位置
     *
     * @return
     */
    private fun checkStoreLocation(): Boolean {
        var isPassCheck = false
        binding?.apply {
            textInputLayoutLocation.error =
                if (edtLocation.text.isNullOrEmpty()) getString(R.string.please_input_store_location) else ""
            isPassCheck = textInputLayoutLocation.error.isNullOrEmpty()

            if (switchDistance.isSwitchOpen()) {

            }
        }
        return isPassCheck
    }


    private fun checkLocationDistance(): Boolean {
        var isPassCheck = false
        binding?.apply {
            if (switchDistance.isSwitchOpen()) {
                val distance = edtLocationDistance.text.toString().toIntOrNull() ?: 0
                if (distance < 50) {
                    textInputLayoutLocationDistance.error =
                        getString(R.string.the_closest_distance_is_50_meters)
                } else {
                    textInputLayoutLocationDistance.error = ""
                }
                isPassCheck = textInputLayoutLocationDistance.error.isNullOrEmpty()
            } else {
                isPassCheck = true
            }
        }
        return isPassCheck
    }

    private fun pickImageFromGallery() {
        try {
            val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
            intent.setType("image/*")
            if (intent.resolveActivity(requireContext().packageManager) != null) {
                startActivityForResult(intent, REQUEST_CODE_PICK_IMAGE)
            } else {
                val intent =
                    Intent(
                        Intent.ACTION_OPEN_DOCUMENT,
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI
                    )
                intent.addCategory(Intent.CATEGORY_OPENABLE);
                intent.setType("image/*");
                startActivityForResult(intent, REQUEST_CODE_PICK_IMAGE)
            }

        } catch (e: Exception) {

        }

    }

    // 处理权限请求结果
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        when (requestCode) {
            REQUEST_CODE_READ_PHOTO -> {
                if ((grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED)) {
                    // 权限被授予，可以执行上传操作
                    pickImageFromGallery()
                } else {
                    // 权限被拒绝，需要引导用户去设置中开启权限或者说明为什么需要这个权限
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode === REQUEST_CODE_PICK_IMAGE && resultCode === RESULT_OK && attr.data != null) {
            val selectedImageUri = data?.data
            if (selectedImageUri == null) {
                return
            }

            // 使用selectedImageUri处理图片，例如显示在ImageView中
            val path = getRealPathFromUri(
                requireContext(),
                selectedImageUri
            ) ?: ""
            if (!FileUtil.isFileSizeGreaterThan2MB(path) && FileUtil.isImageFile(File(path))) {
                binding?.apply {
                    ivLogo.setImageURI(selectedImageUri)
                    storeManagerViewModel.getUploadToken(
                        path
                    )
                }
            } else {
                Toast.makeText(
                    requireContext(),
                    requireContext().getString(
                        R.string.upload_image_cue,
                    ), Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

//    // 从Uri获取实际路径的方法
//    fun getRealPathFromUri(context: Context, contentUri: Uri?): String? {
//        var cursor: Cursor? = null
//        try {
//            val proj = arrayOf(MediaStore.Images.Media.DATA)
//            cursor = context.contentResolver.query(contentUri!!, proj, null, null, null)
//            val column_index =
//                cursor?.getColumnIndexOrThrow(MediaStore.Images.Media.DATA) ?: return ""
//            cursor.moveToFirst()
//            return cursor.getString(column_index)
//        } finally {
//            run {
//                cursor?.close()
//            }
//        }
//    }

    fun getRealPathFromUri(context: Context, uri: Uri): String? {
        var filePath: String? = ""
        if (DocumentsContract.isDocumentUri(context, uri)) {
            if (isExternalStorageDocument(uri)) {
                val docId = DocumentsContract.getDocumentId(uri)
                val split = docId.split(":".toRegex()).dropLastWhile { it.isEmpty() }
                    .toTypedArray()
                val type = split[0]

                if ("primary".equals(type, ignoreCase = true)) {
                    filePath = Environment.getExternalStorageDirectory().toString() + "/" + split[1]
                }
            } else if (isDownloadsDocument(uri)) {
                val id = DocumentsContract.getDocumentId(uri)
                val contentUri = ContentUris.withAppendedId(
                    Uri.parse("content://downloads/public_downloads"), id.toLong()
                )

                filePath = getDataColumn(context, contentUri, null, null)
            } else if (isMediaDocument(uri)) {
                val docId = DocumentsContract.getDocumentId(uri)
                val split = docId.split(":".toRegex()).dropLastWhile { it.isEmpty() }
                    .toTypedArray()
                val type = split[0]

                var contentUri: Uri? = null
                if ("image" == type) {
                    contentUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
                } else if ("video" == type) {
                    contentUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI
                } else if ("audio" == type) {
                    contentUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
                }

                val selection = "_id=?"
                val selectionArgs = arrayOf(split[1])

                filePath = getDataColumn(context, contentUri, selection, selectionArgs)
            }
        } else if ("content".equals(uri.scheme, ignoreCase = true)) {
            filePath = getDataColumn(context, uri, null, null)
        } else if ("file".equals(uri.scheme, ignoreCase = true)) {
            filePath = uri.path
        }
        return filePath
    }

    private fun getDataColumn(
        context: Context,
        uri: Uri?,
        selection: String?,
        selectionArgs: Array<String>?
    ): String {
        var cursor: Cursor? = null
        val column = "_data"
        val projection = arrayOf(column)

        try {
            cursor =
                context.contentResolver.query(uri!!, projection, selection, selectionArgs, null)
            if (cursor != null && cursor.moveToFirst()) {
                val index = cursor.getColumnIndexOrThrow(column)
                return cursor.getString(index)
            }
        } finally {
            cursor?.close()
        }
        return ""
    }

    private fun isExternalStorageDocument(uri: Uri): Boolean {
        return "com.android.externalstorage.documents" == uri.authority
    }

    private fun isDownloadsDocument(uri: Uri): Boolean {
        return "com.android.providers.downloads.documents" == uri.authority
    }

    private fun isMediaDocument(uri: Uri): Boolean {
        return "com.android.providers.media.documents" == uri.authority
    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight =
                (displayMetrics.heightPixels * 0.9).toInt()
            val screenWidth =
                (displayMetrics.widthPixels * 0.45).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

}

