package com.metathought.food_order.casheir.ui.dialog

import android.content.Context
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.interfaces.SimpleCallback
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.FORMAT_DATE
import com.metathought.food_order.casheir.constant.FORMAT_DATE_M
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_SHOW_WITH_SECOND
import com.metathought.food_order.casheir.databinding.DialogYearMonthPickerBinding
import com.metathought.food_order.casheir.extension.formatDateStr
import com.metathought.food_order.casheir.extension.parseDate
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.helper.XpopHelper
import com.metathought.food_order.casheir.ui.widget.wheel_picker.NumberWheelAdapter
import com.metathought.food_order.casheir.ui.widget.wheel_picker.OnValueChangeListener
import com.metathought.food_order.casheir.ui.widget.wheel_picker.WheelPicker
import timber.log.Timber
import java.util.Calendar
import java.util.Date

/**
 * 年月选择弹窗
 */
class YearMonthPickerDialog(private val act: Context?) : BaseCenterDialog(act),
    OnValueChangeListener {

    private var binding: DialogYearMonthPickerBinding? = null

    private var onConfirmClickListener: ((Date, Date) -> Unit)? = null

    private var defYears = 2021
    private var defMonths = 12

    private var curSYears = 2021
    private var curSMonths = 12
    private var curEYears = 2021
    private var curEMonths = 12
    fun showDialog(
        onConfirmClickListener: ((Date, Date) -> Unit),
    ): YearMonthPickerDialog {
        this.onConfirmClickListener = onConfirmClickListener
        XPopup.Builder(act)
            .dismissOnTouchOutside(false)
            .setPopupCallback(object : SimpleCallback() {
                override fun onClickOutside(popupView: BasePopupView?) {
                    dismissOrHideSoftInput()
                    super.onClickOutside(popupView)
                }

                override fun onDismiss(popupView: BasePopupView?) {
                    XpopHelper.removeToMap(popupView)
                    super.onDismiss(popupView)
                }
            })
            .asCustom(this)
            .show()
        XpopHelper.addToMap(this)
        return this
    }

    override fun getImplLayoutId(): Int {
        return R.layout.dialog_year_month_picker
    }

    override fun onCreate() {
        super.onCreate()
        binding = DialogYearMonthPickerBinding.bind(popupImplView)
        binding?.apply {
            tvStartDate.setOnClickListener {
                tvStartDate.isSelected = true
                tvEndDate.isSelected = false
                npYears.setValue(curSYears.toString())
                npMonths.setValue(curSMonths.toString())
                if (tvStartDate.text.isNullOrEmpty()) {
                    tvStartDate.text = "$curSYears/$curSMonths"
                    refreshConfirmEnable()
                }
            }
            tvEndDate.setOnClickListener {
                tvStartDate.isSelected = false
                tvEndDate.isSelected = true
                npYears.setValue(curEYears.toString())
                npMonths.setValue(curEMonths.toString())
                if (tvEndDate.text.isNullOrEmpty()) {
                    tvEndDate.text = "$curEYears/$curEMonths"
                    refreshConfirmEnable()
                }
            }
            btnCancel.setOnClickListener {
                resetValues()
            }

            btnConfirm.setEnableWithAlpha(false)
            btnConfirm.setOnClickListener {
                val s =
                    "$curSYears/$curSMonths/1 00:00:00".parseDate(FORMAT_DATE_TIME_SHOW_WITH_SECOND)
                val endCal = Calendar.getInstance()
                endCal[Calendar.YEAR] = curEYears
                endCal[Calendar.MONTH] = curEMonths - 1
                endCal[Calendar.HOUR_OF_DAY] = 23
                endCal[Calendar.MINUTE] = 59
                endCal[Calendar.SECOND] = 59
                endCal[Calendar.MILLISECOND] = 999
                //当前月份的最后一天
                endCal[Calendar.DAY_OF_MONTH] = endCal.getActualMaximum(Calendar.DAY_OF_MONTH)
                val e = endCal.time
                Timber.d(
                    "${s?.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND)} -> ${
                        e.formatDateStr(
                            FORMAT_DATE_TIME_SHOW_WITH_SECOND
                        )
                    }"
                )
                if (s == null) {
                    return@setOnClickListener
                }
                onConfirmClickListener?.invoke(s, e)
                dismiss()
            }

            Calendar.getInstance().apply {
                defYears = get(Calendar.YEAR)
                defMonths = get(Calendar.MONTH) + 1
                curSYears = defYears
                curSMonths = defMonths
                curEYears = defYears
                curEMonths = defMonths
            }

            // 初始化NumberPicker
            initNumberPicker(npYears, 1970, defYears, defYears)
            initNumberPicker(npMonths, 1, 12, defMonths)

            //开始时间默认选中
            tvStartDate.isSelected = true
            tvEndDate.isSelected = false
            npYears.setValue(curSYears.toString())
            npMonths.setValue(curSMonths.toString())
            tvStartDate.text = "$curSYears/$curSMonths"
            refreshConfirmEnable()

        }
    }


    private fun initNumberPicker(numberPicker: WheelPicker, min: Int, max: Int, initValue: Int) {
        numberPicker.setMinValue(min)
        numberPicker.setMaxValue(max)
        numberPicker.setOnValueChangedListener(this)
        numberPicker.setAdapter(NumberWheelAdapter(min, max))
        numberPicker.setValue(initValue.toString())
    }


    override fun onValueChange(picker: WheelPicker, oldVal: String, newVal: String) {
        binding?.apply {
            when (picker) {
                npYears -> {
                    curSelectDateCallback(s = {
                        curSYears = newVal.toInt()
                        tvStartDate.text = "$curSYears/$curSMonths"
                    }, e = {
                        curEYears = newVal.toInt()
                        tvEndDate.text = "$curEYears/$curEMonths"
                    })
                }

                npMonths -> {
                    curSelectDateCallback(s = {
                        curSMonths = newVal.toInt()
                        tvStartDate.text = "$curSYears/$curSMonths"
                    }, e = {
                        curEMonths = newVal.toInt()
                        tvEndDate.text = "$curEYears/$curEMonths"
                    })
                }
            }
            refreshConfirmEnable()
        }
    }

    private fun refreshConfirmEnable() {
        var isEnable = false
        if (binding?.tvStartDate?.text.toString()
                .isNotEmpty() && binding?.tvEndDate?.text.toString().isNotEmpty()
        ) {
            if (curSYears < curEYears) {
                isEnable = true
            } else if (curSYears == curEYears) {
                isEnable = curSMonths <= curEMonths
            }
        }
        binding?.btnConfirm?.setEnableWithAlpha(isEnable)
    }

    private fun curSelectDateCallback(s: () -> Unit, e: () -> Unit) {
        if (binding?.tvStartDate?.isSelected == true) {
            s()
        } else if (binding?.tvEndDate?.isSelected == true) {
            e()
        }
    }

    private fun resetValues() {
        binding?.apply {
            tvStartDate.isSelected = false
            tvEndDate.isSelected = false
            tvStartDate.text = ""
            tvEndDate.text = ""
            curSYears = defYears
            curSMonths = defMonths
            curEYears = defYears
            curEMonths = defMonths
            npYears.setValue(defYears.toString())
            npMonths.setValue(defMonths.toString())
            refreshConfirmEnable()
        }
    }


}