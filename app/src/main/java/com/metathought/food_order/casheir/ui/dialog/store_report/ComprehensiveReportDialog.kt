package com.metathought.food_order.casheir.ui.dialog.store_report

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.fragment.app.FragmentManager
import com.google.android.material.datepicker.MaterialDatePicker
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.FORMAT_DATE
import com.metathought.food_order.casheir.constant.MergeType
import com.metathought.food_order.casheir.constant.QuickTimeType
import com.metathought.food_order.casheir.data.model.base.request_model.ComprehensiveReportRequest
import com.metathought.food_order.casheir.data.model.base.response_model.ComprehensiveReportData
import com.metathought.food_order.casheir.data.model.base.response_model.Header
import com.metathought.food_order.casheir.data.model.base.response_model.MasterReportData
import com.metathought.food_order.casheir.databinding.DialogComprehensiveReportBinding
import com.metathought.food_order.casheir.databinding.DialogFilterTimeSlotBinding
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.ComprehensiveReportContentAdapter
import com.metathought.food_order.casheir.ui.adapter.ComprehensiveReportStoreAdapter
import com.metathought.food_order.casheir.ui.adapter.MergeTypeAdapter
import com.metathought.food_order.casheir.ui.adapter.OrderTypeAdapter
import com.metathought.food_order.casheir.ui.adapter.QuickTimeAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.widget.SyncScrollRecyclerView
import com.metathought.food_order.casheir.ui.widget.RecyclerViewSyncManager
import com.metathought.food_order.casheir.utils.DisplayUtils
import com.metathought.food_order.casheir.utils.getDisplayMetrics
import dagger.hilt.android.AndroidEntryPoint
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 综合报表弹窗
 */
@AndroidEntryPoint
class ComprehensiveReportDialog : BaseDialogFragment() {

    private val viewModel: ComprehensiveReportViewModel by viewModels()

    private var binding: DialogComprehensiveReportBinding? = null
    private var orderTypePopupWindow: PopupWindow? = null
    private var mergeTypePopupWindow: PopupWindow? = null
    private var quickTimePopupWindow: PopupWindow? = null
    private var selectedOrderType: DiningStyleEnum? = null
    private var selectedMergeType = MergeType.MERGED_TIME
    private var selectedQuickTime = QuickTimeType.TODAY
    private var startDate: Date? = null
    private var endDate: Date? = null

    // 表格相关
    private var responseData: ComprehensiveReportData? = null
    private val syncManager = RecyclerViewSyncManager()
    private val tableColW = 100f // 列宽度

    // 适配器
    private var storeAdapter = ComprehensiveReportStoreAdapter(ArrayList())
    private lateinit var contentAdapter: ComprehensiveReportContentAdapter


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DialogComprehensiveReportBinding.inflate(inflater, container, false)
        return binding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        openKeyBoardListener()
        onTouchOutSide(binding?.root)

        initView()
        initObserver()
        initClickListeners()

        val request = ComprehensiveReportRequest(
            // 传入选中的订单类型列表
            orderTypeList = getOrderTypeItems().map { it.id },
            // 传入合并类型的 code 值
            mergeType = selectedMergeType?.code,
            // 传入快捷时间的 code 值
            timeType = selectedQuickTime?.code
        )
        // 调用 ViewModel 的方法发起请求
        viewModel.getReportFormPage(request)
    }

    private fun initView() {
        binding?.apply {
            // 初始化适配器
            contentAdapter = ComprehensiveReportContentAdapter(ArrayList(), requireContext())

            rvStoreName.adapter = storeAdapter
            rvContent.adapter = contentAdapter

            // 设置同步滚动
            syncManager.addRecyclerView(rvStoreName)
            syncManager.addRecyclerView(rvContent)

            // 设置表头与列表项的水平同步滑动
            (rvContent as? SyncScrollRecyclerView)?.setHeaderScrollViews(
                listOf(mainTopScrollView)
            )
        }
    }

    private fun initObserver() {
        viewModel.reportResponse.observe(viewLifecycleOwner) { response ->
            when (response) {
                is ApiResponse.Loading -> {
                    binding?.apply {
                        pbLoading.isVisible = true
                        llContentList.isVisible = false
                        mainTopScrollView.isVisible = false
                        layoutEmpty.root.isVisible = false
                    }
                }

                is ApiResponse.Success -> {
                    binding?.apply {
                        responseData = response.data
                        pbLoading.isVisible = false
                        initData()
                    }
                }

                is ApiResponse.Error -> {
                    binding?.apply {
                        pbLoading.isVisible = false
                        llContentList.isVisible = false
                        mainTopScrollView.isVisible = false
                        layoutEmpty.root.isVisible = true
                    }
                }

                else -> {
                    // 处理其他状态
                }
            }
        }
    }

    private fun initData() {
        binding?.apply {
            if (responseData?.masterReportDataList.isNullOrEmpty()) {
                llContentList.isVisible = false
                mainTopScrollView.isVisible = false
                layoutEmpty.root.isVisible = true
            } else {
                llContentList.isVisible = true
                mainTopScrollView.isVisible = true
                layoutEmpty.root.isVisible = false

                val data = responseData?.masterReportDataList ?: emptyList()
                val headers = responseData?.headerList ?: emptyList()

                // 设置表头
                setupTableHeader(headers)

                // 设置适配器数据
                contentAdapter.setupHeaderList(headers)
                contentAdapter.replaceData(ArrayList(data))
                storeAdapter.replaceData(ArrayList(data))

                // 重置滚动位置
                mainTopScrollView.scrollX = 0
            }
        }
    }

    private fun setupTableHeader(headers: List<Header>) {
        binding?.llDynamicHeader?.removeAllViews()

        headers.forEach { header ->
            val textView = TextView(context).apply {
                layoutParams = LinearLayout.LayoutParams(
                    DisplayUtils.dp2px(context, tableColW),
                    LinearLayout.LayoutParams.MATCH_PARENT
                ).apply {
                    marginEnd = DisplayUtils.dp2px(context, 10f)
                }

                maxLines = 2
                gravity = android.view.Gravity.CENTER
                setTextColor(ContextCompat.getColor(context, R.color.black))
                textSize = 14f
                text = header.value
                setTextAppearance(R.style.FontLocalization)
                setTypeface(null, android.graphics.Typeface.BOLD)
                setBackgroundColor(ContextCompat.getColor(context, R.color.gray_f5))
            }
            binding?.llDynamicHeader?.addView(textView)
        }
    }

    private fun initClickListeners() {
        // 订单类型点击事件
        binding?.dropdownOrderType?.setOnClickListener {
            showOrderTypePopupWindow(it)
        }

        // 合并类型点击事件
        binding?.dropdownMergeType?.setOnClickListener {
            showMergeTypePopupWindow(it)
        }

        // 快捷时间点击事件
        binding?.dropdownQuickTime?.setOnClickListener {
            showQuickTimePopupWindow(it)
        }

        binding?.tvCalendar?.setOnClickListener {
            // 处理日期选择逻辑
            datePickerDialog()
        }

        // 搜索按钮点击事件
        binding?.btnSearch?.setOnClickListener {
            val request = ComprehensiveReportRequest(
                // 传入选中的订单类型列表
                orderTypeList = selectedOrderType?.let { listOf(it.id) },
                // 传入合并类型的 code 值
                mergeType = selectedMergeType?.code,
                // 传入快捷时间的 code 值
                timeType = selectedQuickTime?.code
            )
            // 调用 ViewModel 的方法发起请求
            viewModel.getReportFormPage(request)
        }

        // 打印按钮点击事件
        binding?.tvPrint?.setOnClickListener {
            // 处理打印逻辑
        }

        // 导出按钮点击事件
        binding?.tvExport?.setOnClickListener {
            // 处理导出逻辑
        }
    }

    private fun showOrderTypePopupWindow(anchorView: View) {
        val popupViewTable = DialogFilterTimeSlotBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupViewTable.root,
            anchorView.width,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        binding?.arrowOrderType?.animate()?.rotation(180f)?.setDuration(200)
        // 显示弹窗时将图标设置为下拉箭头
        binding?.arrowOrderType?.setImageResource(R.drawable.ic_dropdown)

        popupWindow.elevation = 20f
        popupWindow.animationStyle = R.style.PopupAnimation
        // 显示弹窗时设置背景
        anchorView.setBackgroundResource(R.drawable.background_spinner_top)
        popupWindow.showAsDropDown(anchorView)

        popupWindow.setOnDismissListener {
            binding?.arrowOrderType?.animate()?.rotation(0f)?.setDuration(200)
            // 弹窗关闭时恢复原背景
            anchorView.setBackgroundResource(R.drawable.background_white_border_black12_radius_100)
            // 若有选择，将图标设置为删除图标
            if (selectedOrderType != null) {
                binding?.arrowOrderType?.setImageResource(R.drawable.icon_input_delete)
                // 为删除图标添加点击事件
                binding?.arrowOrderType?.setOnClickListener {
                    clearOrderTypeSelection()
                }
            } else {
                // 若无选择，恢复下拉箭头图标
                binding?.arrowOrderType?.setImageResource(R.drawable.ic_dropdown)
                // 移除点击事件
                binding?.arrowOrderType?.setOnClickListener(null)
            }
        }

        val adapter = OrderTypeAdapter(
            getOrderTypeItems(),
            selectedOrderType,
            requireContext()
        )
        adapter.onItemClickCallback = { selected ->
            selectedOrderType = selected
            binding?.tvOrderType?.text = getDisplayName(selected)
            // 设置字体颜色为黑色
            binding?.tvOrderType?.setTextColor(resources.getColor(R.color.black, null))
            popupWindow.dismiss()
        }
        popupViewTable.recyclerViewTable.adapter = adapter
        orderTypePopupWindow = popupWindow
    }

    /**
     * 清空订单类型选择
     */
    private fun clearOrderTypeSelection() {
        selectedOrderType = null
        // 恢复默认文本
        binding?.tvOrderType?.text = getString(R.string.order_type)
        binding?.tvOrderType?.setTextColor(resources.getColor(R.color.black20, null))
        // 恢复下拉箭头图标
        binding?.arrowOrderType?.setImageResource(R.drawable.ic_dropdown)
        // 移除点击事件
        binding?.arrowOrderType?.setOnClickListener(null)
    }


    private fun showMergeTypePopupWindow(anchorView: View) {
        val popupViewTable = DialogFilterTimeSlotBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupViewTable.root,
            anchorView.width,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        binding?.arrowMergeType?.animate()?.rotation(180f)?.setDuration(200)
        popupWindow.elevation = 20f
        popupWindow.animationStyle = R.style.PopupAnimation
        // 显示弹窗时设置背景
        anchorView.setBackgroundResource(R.drawable.background_spinner_top)
        popupWindow.showAsDropDown(anchorView)

        popupWindow.setOnDismissListener {
            binding?.arrowMergeType?.animate()?.rotation(0f)?.setDuration(200)
            // 弹窗关闭时恢复原背景
            anchorView.setBackgroundResource(R.drawable.background_white_border_black12_radius_100)
        }

        val adapter = MergeTypeAdapter(
            getMergeTypeItems(),
            selectedMergeType,
            requireContext()
        )

        adapter.onItemClickCallback = { selected ->
            selectedMergeType = selected
            // 根据枚举值设置显示文本
            binding?.tvMergeType?.text = when (selected) {
                MergeType.MERGED_TIME -> "合并时间"
                MergeType.INDEPENDENT_TIME -> "独立时间"
            }
            popupWindow.dismiss()
        }
        popupViewTable.recyclerViewTable.adapter = adapter
        mergeTypePopupWindow = popupWindow
    }

    private fun showQuickTimePopupWindow(anchorView: View) {
        val popupViewTable = DialogFilterTimeSlotBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupViewTable.root,
            anchorView.width,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        binding?.arrowQuickTime?.animate()?.rotation(180f)?.setDuration(200)
        popupWindow.elevation = 20f
        popupWindow.animationStyle = R.style.PopupAnimation
        // 显示弹窗时设置背景
        anchorView.setBackgroundResource(R.drawable.background_spinner_top)
        popupWindow.showAsDropDown(anchorView)

        popupWindow.setOnDismissListener {
            binding?.arrowQuickTime?.animate()?.rotation(0f)?.setDuration(200)
            // 弹窗关闭时恢复原背景
            anchorView.setBackgroundResource(R.drawable.background_white_border_black12_radius_100)
        }

        val adapter = QuickTimeAdapter(
            getQuickTimeItems(),
            selectedQuickTime,
            requireContext()
        )
        adapter.onItemClickCallback = { selected ->
            selectedQuickTime = selected
            // 根据枚举值设置显示文本
            binding?.tvQuickTime?.text = when (selected) {
                QuickTimeType.TODAY -> "今天"
                QuickTimeType.YESTERDAY -> "昨天"
                QuickTimeType.SEVEN_DAYS -> "7 天"
                QuickTimeType.THIRTY_DAYS -> "30 天"
            }
            popupWindow.dismiss()
        }
        popupViewTable.recyclerViewTable.adapter = adapter
        quickTimePopupWindow = popupWindow
    }

    private fun datePickerDialog() {

        val builder = MaterialDatePicker.Builder.dateRangePicker()
        val datePicker = builder.build()
        datePicker.addOnPositiveButtonClickListener { selection ->
//            timeSlot = null
//            binding?.tvType?.text = getTimeSlotString(timeSlot)

            startDate = Date(selection.first)
            endDate = Date(selection.second)
            val showSdf = SimpleDateFormat(FORMAT_DATE, Locale.US)
            binding?.tvCalendar?.text =
                "${showSdf.format(startDate)} - ${showSdf.format(endDate)}"
            binding?.tvCalendar?.updateCalendarColor()

//            postSearch()
        }
        datePicker.show(parentFragmentManager, "DATE_PICKER")

    }

    private fun getOrderTypeItems(): List<DiningStyleEnum> {
        return DiningStyleEnum.entries
    }

    private fun getDisplayName(style: DiningStyleEnum): String {
        return when (style) {
            DiningStyleEnum.DINE_IN -> "堂食"
            DiningStyleEnum.TAKE_AWAY -> "外带"
            DiningStyleEnum.PRE_ORDER -> "预定"
            DiningStyleEnum.TAKE_OUT -> "外卖"
        }
    }


    private fun getMergeTypeItems(): List<MergeType> {
        return MergeType.entries
    }

    private fun getQuickTimeItems(): List<QuickTimeType> {
        return QuickTimeType.entries
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.85).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.95).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    companion object {
        private const val TAG = "ComprehensiveReportDialog"

        /**
         * 显示综合报表弹窗
         * @param fragmentManager FragmentManager 实例，用于显示弹窗
         */
        fun showDialog(
            fragmentManager: FragmentManager,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance()
            fragment.show(fragmentManager, TAG)
        }

        /**
         * 关闭综合报表弹窗
         * @param fragmentManager FragmentManager 实例，用于查找并关闭弹窗
         */
        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? ComprehensiveReportDialog
            fragment?.dismissAllowingStateLoss()
        }

        /**
         * 创建综合报表弹窗实例
         * @return ComprehensiveReportDialog 实例
         */
        private fun newInstance(): ComprehensiveReportDialog {
            val args = Bundle()
            val fragment = ComprehensiveReportDialog()
            fragment.arguments = args
            return fragment
        }
    }
}