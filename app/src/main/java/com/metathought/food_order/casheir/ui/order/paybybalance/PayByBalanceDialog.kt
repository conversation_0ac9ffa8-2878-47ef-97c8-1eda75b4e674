package com.metathought.food_order.casheir.ui.order.paybybalance

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.hbb20.CountryCodePicker
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.databinding.DialogPayByBalanceBinding
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.extension.setVisibleInvisible
import com.metathought.food_order.casheir.helper.LocaleHelper
import com.metathought.food_order.casheir.network.ApiResponse
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch


@AndroidEntryPoint
class PayByBalanceDialog : DialogFragment(), CountryCodePicker.DialogEventsListener {
    private var binding: DialogPayByBalanceBinding? = null
    private var callBackPayClick: ((CustomerMemberResponse) -> Unit)? = null
    private var toPayClick: ((String?) -> Unit)? = null
    private val payByBalanceViewModel: PayByBalanceViewModel by viewModels()
    private var totalPrice: Long = 0
    private var isBalanceInsufficient: Boolean = false
    private var accountId: String? = null
    private var customerMemberResponse: CustomerMemberResponse? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogPayByBalanceBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initObserver()
        initListener()
    }

    private fun initObserver() {
        payByBalanceViewModel.uiState.observe(viewLifecycleOwner) {
            it.memberAccountResult?.let { res ->
                binding?.apply {
                    when (res) {
                        is ApiResponse.Loading -> {
                            btnPay.setEnable(false)
                            pbLoadMember.setVisibleGone(true)
                            layoutInfor.setVisibleInvisible(false)
                            tvNotFound.setVisibleGone(false)
                        }

                        is ApiResponse.Error -> {
                            btnPay.setEnable(false)
                            pbLoadMember.setVisibleGone(false)
                            tvNotFound.setVisibleGone(true)
                            layoutInfor.setVisibleInvisible(false)
                        }

                        is ApiResponse.Success -> {
                            customerMemberResponse = res.data
                            pbLoadMember.setVisibleGone(false)
                            if (customerMemberResponse != null) {
                                btnPay.setEnable(true)
                                tvNotFound.setVisibleGone(false)
                                layoutInfor.setVisibleInvisible(true)
                                tvMemberName.text = res.data?.nickName
                                isBalanceInsufficient = totalPrice > res.data?.balance ?: 0
                                accountId = res.data?.accountId
                                tvBalance.text = "$${res.data?.balance?.priceFormatTwoDigitZero()}"
                                tvTotal.text = "$${totalPrice?.priceFormatTwoDigitZero()}"
                            } else {
                                btnPay.setEnable(false)
                                pbLoadMember.setVisibleGone(false)
                                tvNotFound.setVisibleGone(true)
                                layoutInfor.setVisibleInvisible(false)
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
//        context?.let {
//            val displayMetrics = getDisplayMetrics(it)
//            val screenHeight = (displayMetrics.heightPixels * PERCENT_85).toInt()
//            val screenWidth = (displayMetrics.widthPixels * PERCENT_85).toInt()
//            dialog?.window?.setLayout(screenWidth, screenHeight)
//        }
    }

    private fun initData() {
        arguments?.let {
            totalPrice = it.getLong(CONTENT, 0)

            val countryCode = it.getString(COUNTRY_CODE, null)
            val phone = it.getString(PHONE, null)
            binding?.apply {
                if (!countryCode.isNullOrEmpty() && !phone.isNullOrEmpty()) {
                    countryCodeHolder.setDefaultCountryUsingPhoneCode(
                        countryCode.replace(" ", "").toInt()
                    )
                    countryCodeHolder.setCountryForPhoneCode(countryCode.replace(" ", "").toInt())
                }
                edtPhoneNumber.setText(phone)
                edtPhoneNumber.setSelection(edtPhoneNumber.text?.length ?: 0)
                if (edtPhoneNumber.text.toString().isNotEmpty()) {
                    payByBalanceViewModel.getMemberAccount(
                        countryCodeHolder.selectedCountryCode.plus(
                            edtPhoneNumber.text.toString()
                        )
                    )
                }
            }
        }
        binding?.apply {
            btnPay.setEnable(false)
            this.btnSearch.setEnable(false)
            val lang = LocaleHelper.getLang(MyApplication.myAppInstance)
            if (lang.startsWith("zh")) {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.CHINESE_SIMPLIFIED)
            } else if (lang.startsWith("km")) {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.KHMER)
            } else {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.ENGLISH)
            }
            countryCodeHolder.supportFragementManager = activity?.supportFragmentManager

            lifecycleScope.launch {
                // removed base on ticket https://chandao.metathought.co/bug-view-1930.html
//                PreferenceDataStoreHelper.getInstance().apply {
//                    val dingingStyle = getFirstPreference(
//                        PreferenceDataStoreConstants.DATA_STORE_KEY_CURRENT_DINING_STYLE,
//                        -1
//                    )
//                    val record = ShoppingHelper.get(dingingStyle)
//                    record?.mobile?.let {
//                        withContext(Dispatchers.Main) {
//                            edtPhoneNumber.setText(it)
//                            payByBalanceViewModel.getMemberAccount(
//                                countryCodeHolder.selectedCountryCode.plus(
//                                    edtPhoneNumber.text.toString()
//                                )
//                            )
//                            activity?.hideKeyboard(edtPhoneNumber)
//                        }
//                    }
//                }
            }
        }
    }

    private fun initListener() {
        binding?.apply {
            countryCodeHolder.setDialogEventsListener(this@PayByBalanceDialog)
            countryCodeHolder.setOnCountryChangeListener {
                edtPhoneNumber.text?.isNotEmpty()?.let { btnSearch.setEnable(it) }
            }
            btnClose.setOnClickListener {
                dismissAllowingStateLoss()
            }
            btnSearch.setOnClickListener {
                payByBalanceViewModel.getMemberAccount(
                    countryCodeHolder.selectedCountryCode.plus(
                        edtPhoneNumber.text.toString()
                    )
                )
                activity?.hideKeyboard(edtPhoneNumber)
            }
            edtPhoneNumber.addTextChangedListener { text ->
                text?.isNotEmpty()?.let { btnSearch.setEnable(it) }
            }
            btnPay.setOnClickListener {
                if (isBalanceInsufficient) {
                    customerMemberResponse?.let { it1 -> callBackPayClick?.invoke(it1) }
                } else if (accountId != null) {
                    //call Pay API
                    toPayClick?.invoke(accountId)
                }
                dismissAllowingStateLoss()
            }
        }
    }

    companion object {
        const val PAY_BY_BALANCE = "PAY_BY_BALANCE"
        private const val CONTENT = "CONTENT"
        private const val COUNTRY_CODE = "COUNTRY_CODE"
        private const val PHONE = "PHONE"

        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        fun showDialog(
            fragmentManager: FragmentManager,
            content: Long? = null,
            countryCode: String? = null,
            phone: String? = null,
            callBackClickListener: ((CustomerMemberResponse) -> Unit)? = null,
            toPayClick: ((String?) -> Unit)? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(PAY_BY_BALANCE)
            if (fragment != null) return
            fragment = newInstance(
                content = content,
                countryCode = countryCode,
                phone = phone,
                callBackClickListener,
                toPayClick
            )
            fragment.show(fragmentManager, PAY_BY_BALANCE)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            var fragment =
                fragmentManager.findFragmentByTag(PAY_BY_BALANCE) as? PayByBalanceDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            content: Long? = null,
            countryCode: String? = null,
            phone: String? = null,
            callBackClickListener: ((CustomerMemberResponse) -> Unit)? = null,
            toPayClick: ((String?) -> Unit)? = null
        ): PayByBalanceDialog {
            val args = Bundle()
            content?.let {
                args.putLong(CONTENT, it)
            }
            args.putString(COUNTRY_CODE, countryCode)
            args.putString(PHONE, phone)
            val fragment = PayByBalanceDialog()
            fragment.arguments = args
            fragment.callBackPayClick = callBackClickListener
            fragment.toPayClick = toPayClick
            return fragment
        }
    }


    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }

    override fun onCcpDialogOpen(dialog: Dialog?) {

    }

    override fun onCcpDialogDismiss(dialogInterface: DialogInterface?) {
        binding?.let {
            it.edtPhoneNumber.isFocusable = true
        }
    }

    override fun onCcpDialogCancel(dialogInterface: DialogInterface?) {

    }


}
