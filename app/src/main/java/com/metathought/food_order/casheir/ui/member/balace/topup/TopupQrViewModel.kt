package com.metathought.food_order.casheir.ui.member.balace.topup

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargePaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeQRStatusResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeResponse
import com.metathought.food_order.casheir.data.model.base.response_model.order.PaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Timer
import java.util.TimerTask
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2024/3/2214:03
 * @description
 */
@HiltViewModel
class TopupQrViewModel
@Inject
constructor(private val repository: Repository) : ViewModel() {


    private val _uiState = MutableLiveData<UIModel>()

    val uiState get() = _uiState

    private lateinit var qrData: RechargePaymentResponse
    fun initQrData(qrData: RechargePaymentResponse) {
        this.qrData = qrData
//        startExpiredTimer()
        checkPaymentStatus()
    }

    private fun checkPaymentStatus() {
        viewModelScope.launch {
            val result = repository.getRechargeQRStatus(qrData.orderId)
            if (result is ApiResponse.Success) {
                if (result.data.isOrderSuccess()) {
                    emitUiState(success = result.data)
                } else if (result.data.isOrderExpire()) {
                    timer?.cancel()
                    emitUiState(isExpire = true)
                } else {
                    delay(2000)
                    checkPaymentStatus()
                }

            } else if (result is ApiResponse.Error) {
                emitUiState(error = result.message)
            }
        }
    }

    private var timer: Timer? = null
//    private fun startExpiredTimer() {
//        timer = Timer()
//        timer?.schedule(object : TimerTask() {
//            override fun run() {
//                if (isExpired(qrData)) {
//                    timer?.cancel()
//                    emitUiState(isExpire = true)
//                } else {
//                    emitUiState(time = getTimerTime(qrData))
//                }
//            }
//        }, 1000, 1000)
//    }

    private fun isExpired(qrData: RechargePaymentResponse): Boolean {
        val expiredTime = qrData.getExpiredTimestamp() ?: 0L
        val time = expiredTime - System.currentTimeMillis()
        return time <= 0
    }

    private val formatter = SimpleDateFormat("mm:ss")

    private fun getTimerTime(qrData: RechargePaymentResponse): String {
        val expiredTime = qrData.getExpiredTimestamp() ?: 0L
        val time = expiredTime - System.currentTimeMillis()
        return formatter.format(time)
    }

    private fun emitUiState(
        time: String? = null,
        isExpire: Boolean? = null,
        error: String? = null,
        success: RechargeQRStatusResponse? = null,
    ) {
        val uiModel = UIModel(time, isExpire, error, success)
        _uiState.postValue(uiModel)
    }

    data class UIModel(
        val time: String?,
        val isExpire: Boolean?,
        val error: String?,
        val success: RechargeQRStatusResponse?,
    )
}
