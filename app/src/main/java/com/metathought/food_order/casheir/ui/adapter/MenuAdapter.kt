package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import cn.bingoogolapple.badgeview.BGABadgeViewHelper
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.HeaderGoods
import com.metathought.food_order.casheir.database.dao.GoodsHelper
import com.metathought.food_order.casheir.databinding.NewMenuItemBinding
import com.metathought.food_order.casheir.databinding.StickerHeaderItemBinding
import com.metathought.food_order.casheir.extension.addMealSetTag
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.network.GOOD_MAX_NUM
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.utils.DisplayUtils
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.DiffUtil
import com.metathought.food_order.casheir.utils.SingleClickUtils


class MenuAdapter(
    val list: ArrayList<BaseGoods>,
    val context: Context,
    var nameLine: Int = 3,
    val onClickCallback: (Goods, Int) -> Unit,
    val onPlusCallback: (Goods) -> Unit,
    val onSubCallback: (Goods) -> Unit,
    val onMealSetCallback: (Goods) -> Unit,
) : RecyclerView.Adapter<MenuAdapter.Companion.BaseViewHolder<*>>() {

    private lateinit var headerBinding: StickerHeaderItemBinding
    private lateinit var contentBinding: NewMenuItemBinding
    var localDiningStyle: Int = 0
    var padding = 0

    init {
        padding = DisplayUtils.dp2px(context, 12f)
    }

    inner class MenuViewHolder(val binding: NewMenuItemBinding) :
        BaseViewHolder<NewMenuItemBinding>(binding.root) {

        fun bind(resource: Goods?, indexBind: Int) {
            resource?.let { _ ->
                val record = GoodsHelper.get(resource.id, localDiningStyle)
                if (record != null) {
                    resource.sellCount = record.num
                } else {
                    resource.sellCount = 0
                }

//                resource.cartsId = record?.cardsId

                binding.apply {

                    tvGoodName.setLines(nameLine)
                    tvGoodName.text = resource.name
                    if (resource.isMealSet()) {
                        tvGoodName.addMealSetTag(context)
                    }

                    updateActivityTag(resource)
                    updatePrice(resource)
                    updateSoldOut(resource)
                    updateImage(resource, indexBind)
                    updateBadge(resource.sellCount)

                    root.setOnClickListener {
                        if (resource.soldOut == true) {
                            return@setOnClickListener
                        }
                        if (resource.isMealSet()) {
                            SingleClickUtils.isFastDoubleClick {
                                onMealSetCallback.invoke(resource)
                            }
                        } else if (resource.withSpecifications == true || !resource.feeds.isNullOrEmpty()) {
                            SingleClickUtils.isFastDoubleClick {
                                onClickCallback.invoke(resource, indexBind)
                            }
                        } else {
                            val record = GoodsHelper.get(resource.id, localDiningStyle)
                            var maxNum = GOOD_MAX_NUM
                            var canAddNum = maxNum - (record?.num ?: 0)
                            if (localDiningStyle == DiningStyleEnum.PRE_ORDER.id) {
                                //预定数量限制
                                val restrictNum = (resource.restrictNum ?: 0)
                                //如果有预定数量限制
                                if (restrictNum > 0) {
                                    maxNum = restrictNum
                                    canAddNum = maxNum - (record?.num ?: 0)
                                }
                            }
                            if (canAddNum <= 0) {
                                Toast.makeText(
                                    context,
                                    context.getString(
                                        R.string.you_have_reached_the_maximum_quantity_limit_of,
                                        maxNum
                                    ), Toast.LENGTH_SHORT
                                ).show()
                                return@setOnClickListener
                            }
                            onPlusCallback.invoke(resource.apply { position = indexBind })
                        }

                    }
                }
            }
        }

        /**
         * 售罄状态
         */
        private fun updateSoldOut(resource: Goods) {
            binding.apply {

                tvSoldOut.isVisible = false
                if (resource.soldOut == true) {
                    tvSoldOut.isVisible = true
                    tvPrice.setTextColor(context.getColor(R.color.black40))
                } else {
                    tvPrice.setTextColor(context.getColor(R.color.primaryColor))
                }
            }
        }

        /**
         * 更新价格
         *
         * @param resource
         */
        @SuppressLint("SetTextI18n")
        private fun updatePrice(resource: Goods) {
            binding.apply {
//                val isHasDiscountPrice = resource.isHasDiscountPrice()
                val sellPrice = resource.getCalculateDiscountPrice()
//                    if (isHasDiscountPrice) resource.getCalculateDiscountPrice() else resource.getCalculateSellPrice()
                val sellPriceStr = FoundationHelper.getPriceStrByUnit(
                    FoundationHelper.useConversionRatio,
                    sellPrice ?: 0L,
                    FoundationHelper.isKrh
                )
                if (resource.isTimePriceGood()) {
                    //只要是时价菜 在菜单就显示时价
                    tvPrice.text = context.getString(R.string.time_price)
                } else if (resource.isToBeWeighed() && !resource.isMealSet()) {
                    //如果非时价菜
                    val unit = resource.getWeightUnit()
                    tvPrice.text =
                        "${sellPriceStr}/${unit}"
                } else {
                    tvPrice.text = sellPriceStr
                }
            }
        }

        /**
         * 更新活动标签
         *
         */
        private fun updateActivityTag(resource: Goods) {
            binding?.apply {
                tvCouponActivity.isVisible = false
                llCouponActivityWithNoImage.isVisible = false
                val activityLabel = resource.activityLabels?.firstOrNull()
                val giftLabel = resource.giftLabels?.firstOrNull()
                if (activityLabel != null) {
                    val gradientDrawable = GradientDrawable()
                    gradientDrawable.setColor(activityLabel.color.toColorInt())
                    val radius = padding.toFloat()
                    if (MainDashboardFragment.CURRENT_USER?.cashierShowPic == true) {
                        gradientDrawable.cornerRadii = floatArrayOf(
                            0f, 0f,  // 左上角
                            radius, radius,  // 右上角
                            radius, radius,  // 右下角
                            0f, 0f,// 左下角
                        )
                        tvCouponActivity.background = gradientDrawable
                        tvCouponActivity.text = activityLabel.name
                        tvCouponActivity.isVisible = true
                    } else {
                        gradientDrawable.cornerRadii = floatArrayOf(
                            radius, radius,  // 左上角
                            0f, 0f,  // 右上角
                            0f, 0f,  // 右下角
                            radius, radius,// 左下角
                        )
                        tvCouponActivityWithNoImage.background = gradientDrawable
                        tvCouponActivityWithNoImage.text = activityLabel.name
                        llCouponActivityWithNoImage.isVisible = true
                    }
                } else if (giftLabel != null) {
                    val gradientDrawable = GradientDrawable()
                    gradientDrawable.setColor(giftLabel.color.toColorInt())
                    val radius = padding.toFloat()
                    if (MainDashboardFragment.CURRENT_USER?.cashierShowPic == true) {
                        gradientDrawable.cornerRadii = floatArrayOf(
                            0f, 0f,  // 左上角
                            radius, radius,  // 右上角
                            radius, radius,  // 右下角
                            0f, 0f,// 左下角
                        )
                        tvCouponActivity.background = gradientDrawable
                        tvCouponActivity.text = giftLabel.name
                        tvCouponActivity.isVisible = true
                    } else {
                        gradientDrawable.cornerRadii = floatArrayOf(
                            radius, radius,  // 左上角
                            0f, 0f,  // 右上角
                            0f, 0f,  // 右下角
                            radius, radius,// 左下角
                        )
                        tvCouponActivityWithNoImage.background = gradientDrawable
                        tvCouponActivityWithNoImage.text = giftLabel.name
                        llCouponActivityWithNoImage.isVisible = true
                    }
                }
            }
        }

        /**
         * 更新红点
         *
         * @param count
         */
        private fun updateBadge(count: Int?) {
            binding.rootView.apply {
                badgeViewHelper.setBadgeGravity(BGABadgeViewHelper.BadgeGravity.RightTop)
                badgeViewHelper.setBadgeTextSizeSp(14)
                badgeViewHelper.setBadgePaddingDp(4)
                if (count != null && count > 0) showTextBadge("$count")
                else hiddenBadge()
            }
        }

        //更新图片
        private fun updateImage(resource: Goods, position: Int) {

            if (MainDashboardFragment.CURRENT_USER?.cashierShowPic == true) {
                binding.imgImage.isVisible = true
                Glide.with(context).load(resource.picUrl)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .placeholder(R.drawable.icon_menu_default2)
                    .transition(DrawableTransitionOptions.withCrossFade())
                    .centerCrop()
                    .error(R.drawable.icon_menu_default2)
                    .into(binding.imgImage)

            } else {
                binding.imgImage.isVisible = false
            }
        }

    }

    override fun onViewRecycled(holder: BaseViewHolder<*>) {
        when (holder) {
            is HeaderViewHolder -> {

            }

            is MenuViewHolder -> {
            }
        }
        super.onViewRecycled(holder)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder<*> {
        return when (viewType) {
            ViewType.Header.ordinal -> {
                headerBinding = StickerHeaderItemBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                HeaderViewHolder(headerBinding)
            }

            ViewType.Content.ordinal -> {
                contentBinding =
                    NewMenuItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                MenuViewHolder(contentBinding)
            }

            else -> throw IllegalArgumentException("Not a layout")
        }
    }

    private var menuWidth = 0;

    override fun onViewAttachedToWindow(holder: BaseViewHolder<*>) {
        super.onViewAttachedToWindow(holder)
        if (holder is MenuViewHolder) {
            if (holder.itemView.width > menuWidth) {
                menuWidth = holder.itemView.width
            }
//            println("Item 宽度: $menuWidth")
        }
    }

    override fun onBindViewHolder(holder: BaseViewHolder<*>, position: Int) {
        when (holder) {
            is HeaderViewHolder -> {
                holder.binding.tvValue.text = (list[position] as HeaderGoods).name
                holder.binding.tvValue.setPadding(
                    0,
                    if (position == 0) 0 else padding,
                    0,
                    padding
                )
//                if ((list[position] as HeaderGoods).id == highLightGroupId) {
//                    holder.binding.rootView.setBackgroundColor(context.getColor(R.color.primaryColor10))
//                } else {
//                    holder.binding.rootView.setBackgroundColor(context.getColor(R.color.transparent))
//                }
            }

            is MenuViewHolder -> {
                (list[position] is Goods).let {
                    holder.bind(list[position] as Goods, position)
                }

            }
        }
    }


    override fun onBindViewHolder(
        holder: BaseViewHolder<*>,
        position: Int,
        payloads: MutableList<Any>
    ) {
        if (payloads.isEmpty()) {
            super.onBindViewHolder(holder, position, payloads)
        } else {
            when (holder) {
                is HeaderViewHolder -> {
                    holder.binding.tvValue.text = (list[position] as HeaderGoods).name
                    holder.binding.tvValue.setPadding(
                        0,
                        if (position == 0) 0 else padding,
                        0,
                        padding
                    )
                }

                is MenuViewHolder -> {
                    holder.binding.apply {
                        (list[position] as Goods).let {
                            val record = GoodsHelper.get(it.id, localDiningStyle)

                            if (record != null) {
                                it.sellCount = record.num
                            } else {
                                it.sellCount = 0
                            }
//                            it.cartsId = record?.cardsId

                            if ((it.sellCount ?: 0) > 0) {
                                rootView.showTextBadge("${it.sellCount ?: 0}")
                            } else {
                                rootView.hiddenBadge()
                            }
                        }
                    }
                }
            }
        }
    }


    override fun getItemViewType(position: Int): Int {
        return when (list[position].header) {
            true -> ViewType.Header.ordinal
            false -> ViewType.Content.ordinal
            else -> {
                ViewType.Content.ordinal
            }
        }
    }

    override fun getItemCount(): Int {
        return list.size
    }

    //是否有优惠活动
    private var isHasCouponActivity = false
    fun updateItems(newItems: ArrayList<BaseGoods>) {
        isHasCouponActivity =
            newItems.firstOrNull { (it is Goods) && !it.activityLabels.isNullOrEmpty() } != null
        list.clear()
        list.addAll(newItems)
        notifyDataSetChanged()
    }

    fun updateItem(goods: Goods) {
        list.forEachIndexed { index, baseGoods ->
            if (baseGoods is Goods && baseGoods.id == goods.id) {
                notifyItemChanged(index)
            }
        }

    }

    fun getHeaderPosition(groupId: String): Int {
        for (i in 0..<list.size) {
            if (list[i] is HeaderGoods) {
                if ((list[i] as HeaderGoods).id == groupId)
                    return i
            }
        }
        return 0
    }

    inner class HeaderViewHolder(val binding: StickerHeaderItemBinding) :
        BaseViewHolder<StickerHeaderItemBinding>(binding.root) {

    }

    // 添加DiffUtil优化数据更新
//    fun updateItemsDiff(newItems: List<BaseGoods>) {
//        val diffResult = DiffUtil.calculateDiff(MenuDiffCallback(list, newItems))
//        list.clear()
//        list.addAll(newItems)
//        diffResult.dispatchUpdatesTo(this)
//    }

//    private class MenuDiffCallback(
//        private val oldList: List<BaseGoods>,
//        private val newList: List<BaseGoods>
//    ) : DiffUtil.Callback() {
//        override fun getOldListSize() = oldList.size
//        override fun getNewListSize() = newList.size
//
//        override fun areItemsTheSame(oldPos: Int, newPos: Int) =
//            oldList[oldPos].i == newList[newPos].id
//
//        override fun areContentsTheSame(oldPos: Int, newPos: Int) =
//            oldList[oldPos] == newList[newPos]
//    }

    companion object {
        abstract class BaseViewHolder<T>(view: View) : RecyclerView.ViewHolder(view)
    }

    enum class ViewType {
        Header,
        Content
    }
}