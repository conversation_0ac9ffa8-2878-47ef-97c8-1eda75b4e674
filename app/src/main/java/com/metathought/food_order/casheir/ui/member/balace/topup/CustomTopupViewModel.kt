package com.metathought.food_order.casheir.ui.member.balace.topup

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.RechargeTierPageResponse
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class CustomTopupViewModel
@Inject
constructor(private val repository: Repository) : ViewModel() {
    private var pagNo = 1
    private val pageSize = 20
    private val _uiListState = MutableLiveData<UIListModel>()
    val uiListState get() = _uiListState

    fun getRechargeGiftCouponsPage(isRefresh: <PERSON>olean? = null) {
        viewModelScope.launch {
            if (isRefresh != false) {
                pagNo = 1
                if (isRefresh == null) {
                    emitUIListState(showLoading = true)
                }
            }
            try {
                val response =
                    repository.getRechargeTierPage(pagNo, pageSize, null, null, null, true)
                withContext(Dispatchers.Main) {
                    if (response is ApiResponse.Success) {
                        val res = response.data
                        if (res.records.isNullOrEmpty()) {
                            emitUIListState(
                                showEnd = true,
                                isRefresh = isRefresh,
                                showLoading = false
                            )
                            return@withContext
                        }
                        pagNo++
                        emitUIListState(
                            showSuccess = res,
                            isRefresh = isRefresh,
                            showLoading = false
                        )
                    } else if (response is ApiResponse.Error) {
                        emitUIListState(showError = response.message, showLoading = false)
                    }
                }
            } catch (e: Exception) {
                emitUIListState(showError = e.message, showLoading = false)
            }
        }
    }


    private suspend fun emitUIListState(
        showLoading: Boolean? = null,
        showError: String? = null,
        showSuccess: RechargeTierPageResponse? = null,
        showEnd: Boolean = false,
        isRefresh: Boolean? = null
    ) {
        val uiModel = UIListModel(
            showLoading,
            showError,
            showSuccess,
            showEnd,
            isRefresh
        )
        withContext(Dispatchers.Main) {
            _uiListState.value = uiModel
        }
    }

    data class UIListModel(
        val showLoading: Boolean?,
        val showError: String?,
        val showSuccess: RechargeTierPageResponse?,
        val showEnd: Boolean,
        val isRefresh: Boolean?,
    )
}