package com.metathought.food_order.casheir.ui.widget

import android.content.Context
import android.text.InputType

import android.util.AttributeSet
import android.view.ActionMode
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem

import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.LinearLayout
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import com.google.android.material.textfield.TextInputEditText
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.ViewCustomNumberKeyBoardBinding
import com.metathought.food_order.casheir.databinding.ViewCustomSearchBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.isZero
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.utils.SingleClickUtils
import timber.log.Timber
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.math.min


/**
 * 自定义数字键盘
 *
 * @constructor
 *
 * @param context
 * @param attrs
 */
class CustomNumberKeyBoardView(context: Context, attrs: AttributeSet? = null) :
    LinearLayout(context, attrs) {

    companion object {
        const val KEYBOARD_CLEAR = "KEYBOARD_CLEAR"
        const val KEYBOARD_DELETE = "KEYBOARD_DELETE"
        const val KEYBOARD_POINT = "."
    }

    private var _binding: ViewCustomNumberKeyBoardBinding? = null

    private var onItemClickListener: ((String, EditText?) -> Unit)? = null
    private var currentEditText: EditText? = null
    private var isPhone: Boolean = false
    private var phoneMaxLength = 12

    init {

        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.CustomNumberKeyBoardView)

        try {

            initView()

            _binding?.apply {

            }
        } catch (e: Exception) {

        } finally {
            // 最后需要回收数组
            typedArray.recycle()
        }
    }

    private fun initView() {
        _binding = ViewCustomNumberKeyBoardBinding.inflate(LayoutInflater.from(context), this, true)
        _binding?.apply {
            tvNumber0.setOnClickListener {
                afterInput("0")
            }

            tvNumber1.setOnClickListener {
                afterInput("1")
            }

            tvNumber2.setOnClickListener {
                afterInput("2")
            }

            tvNumber3.setOnClickListener {
                afterInput("3")
            }

            tvNumber4.setOnClickListener {
                afterInput("4")
            }

            tvNumber5.setOnClickListener {
                afterInput("5")
            }

            tvNumber6.setOnClickListener {
                afterInput("6")
            }

            tvNumber7.setOnClickListener {
                afterInput("7")
            }

            tvNumber8.setOnClickListener {
                afterInput("8")
            }

            tvNumber9.setOnClickListener {
                afterInput("9")
            }

            tvPoint.setOnClickListener {
                afterInput(KEYBOARD_POINT)
            }

            tvDelete.setOnClickListener {
                afterInput(KEYBOARD_DELETE)
            }

            tvClear.setOnClickListener {
                if (onClearClickListener != null) {
                    onClearClickListener?.invoke()
                } else {
                    afterInput(KEYBOARD_CLEAR)
                }

            }
        }
    }

    /**
     * 是否整数
     */
    private var isInt: Boolean = true

    /**
     * 最小值
     */
    private var minNum: BigDecimal = BigDecimal(0.0)

    /**
     * 最大值
     */
    private var maxNum: BigDecimal = BigDecimal(100000000.00)

    /**
     * 设置范围
     *
     * @param minNum
     * @param maxNum
     */
    fun setRange(minNum: BigDecimal, maxNum: BigDecimal) {
        this.minNum = minNum.halfUp(2)
        this.maxNum = maxNum.halfUp(2)
    }

    fun setIsPhone(isPhone: Boolean) {
        this.isPhone = isPhone
    }

    fun setIsInit(isInt: Boolean) {
        this.isInt = isInt
        _binding?.apply {
            tvPoint.setEnableWithAlpha(!isInt)
        }
    }

    fun setClearEnable(enable: Boolean) {
        _binding?.apply {
            tvClear.setEnableWithAlpha(enable)
        }
    }

    fun setConfirm(enable: Boolean) {
        _binding?.apply {
            tvConfirm.setEnableWithAlpha(enable)
        }
    }

    private fun afterInput(str: String) {
        if (currentEditText != null) {
            val text = currentEditText?.text ?: ""
            if (str == KEYBOARD_CLEAR) {
                currentEditText?.setText("")
            } else if (str == KEYBOARD_DELETE) {
                if (text.isNotEmpty()) {
                    currentEditText?.setText(text.dropLast(1))
                }
            } else if (str == KEYBOARD_POINT) {
                if (!isInt) {
                    if (text.isNullOrEmpty()) {
                        currentEditText?.setText("0${str}")
                    } else {
                        if (!text.contains(KEYBOARD_POINT)) {
                            currentEditText?.setText("${text}${str}")
                        }
                    }
                }
            } else {
                Timber.e("isPhone:${isPhone}")
                if (isPhone) {
                    val newStr = "${currentEditText?.text ?: ""}${str}"
                    //
                    if (newStr.length <= phoneMaxLength) {
                        currentEditText?.setText(newStr)
                    }
                } else {
                    val number = "${currentEditText?.text ?: ""}${str}".toBigDecimalOrNull()
                    if (number != null && number <= maxNum) {
                        if (number.isZero() && !(currentEditText?.text
                                ?: "").startsWith("0.")
                        ) {
                            currentEditText?.setText("0")
                        } else {
                            if (isInt) {
                                currentEditText?.setText("${number.toLong()}")
                            } else {
                                val numberStr = "${currentEditText?.text ?: ""}${str}"
                                val numberList = numberStr.split(KEYBOARD_POINT)
                                if (numberList.size > 1 && numberList.get(1).length > 2) {
                                    currentEditText?.setText(
                                        number.setScale(
                                            2,
                                            RoundingMode.DOWN
                                        ).toString()
                                    )
                                } else {
                                    val num = "${currentEditText?.text ?: ""}".toBigDecimalOrNull()
                                    if (num?.isZero() == true && !(currentEditText?.text
                                            ?: "").startsWith("0.")
                                    ) {
                                        currentEditText?.setText("${str}")
                                    } else {
                                        currentEditText?.setText("${currentEditText?.text ?: ""}${str}")
                                    }

                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private var onClearClickListener: (() -> Unit)? = null

    fun setClearTitle(title: String) {
        _binding?.apply {
            tvClear.text = title
            tvClear.textSize = 20f
        }
    }

    fun setConfirmTitle(title: String) {
        _binding?.apply {
            tvConfirm.text = title
            tvConfirm.textSize = 20f
        }
    }


    fun setOnClearClick(onItemClickListener: (() -> Unit)? = null) {
        onClearClickListener = onItemClickListener
    }

    fun setonConfirmClick(onItemClickListener: (() -> Unit)? = null) {
        _binding?.apply {
            tvConfirm.setOnClickListener {
                onItemClickListener?.invoke()
            }
        }
    }

    fun setOnItemClickListener(onItemClickListener: ((String, EditText?) -> Unit)? = null) {
        this.onItemClickListener = onItemClickListener
    }

    fun setCurrentEditText(editText: EditText?) {
        currentEditText = editText
        currentEditText?.isCursorVisible = false
        currentEditText?.inputType = InputType.TYPE_NULL
        currentEditText?.customSelectionActionModeCallback = object : ActionMode.Callback {
            override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                return false
            }


            override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                return false
            }


            override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                return false
            }


            override fun onDestroyActionMode(mode: ActionMode?) {
            }
        }
        currentEditText?.setTextIsSelectable(false)
        currentEditText?.isLongClickable = false
    }

}