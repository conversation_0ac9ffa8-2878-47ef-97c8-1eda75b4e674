package com.metathought.food_order.casheir.ui.dialog.take_out

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.databinding.DialogModifyTakeOutIdBinding
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint

/**
 *
 * 修改外卖单号
 *
 * **/

@AndroidEntryPoint
class ModifyTakeOutIdDialog : BaseDialogFragment() {
    private var binding: DialogModifyTakeOutIdBinding? = null

    private var onConfirmClickListener: ((orderId: String) -> Unit)? =
        null

    private var takeOutOrderId: String? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogModifyTakeOutIdBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)


        initListener()
        initObserver()
        checkBtnStatus()
        initData()
    }

    private fun initObserver() {

    }

    private fun initData() {
        binding?.apply {
            edtId.setText(takeOutOrderId)
            edtId.setSelection(edtId.length())
            btnYes.setEnableWithAlpha(false)
        }

    }

    private fun initListener() {
        binding?.apply {

            topBar.getCloseBtn()?.setOnClickListener {
                dismissCurrentDialog()
            }

            btnYes.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {

                    if (edtId.text.toString().isNullOrEmpty()) {
                        return@isFastDoubleClick
                    }

                    onConfirmClickListener?.invoke(
                        edtId.text.toString()
                    )
                    dismissCurrentDialog()
                }
            }
            edtId.addTextChangedListener {
                checkBtnStatus()
            }
        }
    }

    private fun checkBtnStatus() {
        binding?.apply {
            if (edtId.text.trim().isEmpty()) {
                btnYes.setEnableWithAlpha(false)
            } else {
                if (edtId.text.toString() == takeOutOrderId) {
                    btnYes.setEnableWithAlpha(false)
                } else {
                    btnYes.setEnableWithAlpha(true)
                }
            }
        }
    }

    companion object {

        private const val TAG = "ModifyTakeOutIdDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            takeOutOrderId: String? = null,
            onConfirmClickListener: ((takeOutOrderId: String) -> Unit)? =
                null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(takeOutOrderId, onConfirmClickListener)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? ModifyTakeOutIdDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            takeOutOrderId: String? = null,
            onConfirmClickListener: ((orderId: String) -> Unit)? =
                null
        ): ModifyTakeOutIdDialog {
            val args = Bundle()
            val fragment = ModifyTakeOutIdDialog()
            fragment.arguments = args
            fragment.onConfirmClickListener = onConfirmClickListener
            fragment.takeOutOrderId = takeOutOrderId
            return fragment
        }
    }
}
