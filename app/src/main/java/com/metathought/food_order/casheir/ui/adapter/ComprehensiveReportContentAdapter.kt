package com.metathought.food_order.casheir.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.Header
import com.metathought.food_order.casheir.data.model.base.response_model.MasterReportData
import com.metathought.food_order.casheir.databinding.ItemComprehensiveReportContentBinding
import com.metathought.food_order.casheir.utils.DisplayUtils
import com.metathought.food_order.casheir.utils.priceFormatTwoDigitZero2
import java.lang.reflect.Field

/**
 * 综合报表内容适配器
 */
class ComprehensiveReportContentAdapter(
    private var dataList: ArrayList<MasterReportData>,
    private val context: Context
) : RecyclerView.Adapter<ComprehensiveReportContentAdapter.ViewHolder>() {

    private var headerList: List<Header> = emptyList()
    private val tableColW = 100f // 列宽度

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemComprehensiveReportContentBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(dataList[position], headerList)
    }

    override fun getItemCount(): Int = dataList.size

    fun replaceData(newData: ArrayList<MasterReportData>) {
        dataList.clear()
        dataList.addAll(newData)
        notifyDataSetChanged()
    }

    fun setupHeaderList(headers: List<Header>) {
        headerList = headers
        notifyDataSetChanged()
    }

    inner class ViewHolder(private val binding: ItemComprehensiveReportContentBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(data: MasterReportData, headers: List<Header>) {
            // 清空之前的动态视图
            binding.llDynamicContent.removeAllViews()

            // 根据headerList动态创建TextView
            headers.forEach { header ->
                val textView = TextView(context).apply {
                    layoutParams = LinearLayout.LayoutParams(
                        DisplayUtils.dp2px(context, tableColW),
                        LinearLayout.LayoutParams.MATCH_PARENT
                    ).apply {
                        marginEnd = DisplayUtils.dp2px(context, 10f)
                    }

                    gravity = android.view.Gravity.CENTER
                    setTextColor(ContextCompat.getColor(context, R.color.black))
                    textSize = 14f
                    setTextAppearance(R.style.FontLocalization)

                    // 通过反射获取对应字段的值
                    val value = getFieldValue(data, header.key)
                    text = when (value) {
                        is Double -> value.priceFormatTwoDigitZero2("$")
                        is String -> value
                        else -> value?.toString() ?: "$0.00"
                    }
                }
                binding.llDynamicContent.addView(textView)
            }
        }

        /**
         * 通过反射获取字段值
         */
        private fun getFieldValue(data: MasterReportData, fieldName: String): Any? {
            return try {
                val field: Field = data::class.java.getDeclaredField(fieldName)
                field.isAccessible = true
                field.get(data)
            } catch (e: Exception) {
                null
            }
        }
    }
}
