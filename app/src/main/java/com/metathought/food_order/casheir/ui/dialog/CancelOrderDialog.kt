package com.metathought.food_order.casheir.ui.dialog


import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.DialogCancelOrderBinding

class CancelOrderDialog : BaseDialogFragment() {

    private var binding: DialogCancelOrderBinding? = null
    private var positiveButtonListener: ((String, autoInStock: Boolean) -> Unit)? = null

    private var currentOrderNo: String? = null

    fun getCurrentOrderNo(): String? {
        return currentOrderNo
    }

    /**
     * 将退还商品自动入库 true 是 false 否
     */
    private var autoInStock = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogCancelOrderBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)

        currentOrderNo = arguments?.getString(ORDER_NO)

        val title = arguments?.getString(TITLE) ?: ""
        if (title.isNotEmpty()) {
            binding?.apply {
                tvTitle.text = title
            }
        }
        initListener()
    }


    private fun initListener() {
        binding?.apply {

            if (arguments?.getBoolean(IS_SHOW_AUTO_IN_STORE) == true) {
                llAutoInStore.isVisible = true
                autoInStock = true
                updateSwitch()
            }


            ivSwitch.setOnClickListener {
                autoInStock = !autoInStock
                updateSwitch()
            }

            btnClose.setOnClickListener {
                dismissCurrentDialog()
            }

            btnYes.setOnClickListener {
                positiveButtonListener?.invoke(edtReason.text.toString(), autoInStock)
                dismissCurrentDialog()
            }
        }

    }

    private fun updateSwitch() {
        binding?.apply {
            ivSwitch.setImageResource(if (autoInStock) R.drawable.icon_switch_open else R.drawable.icon_switch_close)
        }
    }


    companion object {
        private const val TAG = "CancelOrderDialog"
        private const val ORDER_NO = "ORDER_NO"
        private const val IS_SHOW_AUTO_IN_STORE = "isShowAutoInStore"
        private const val TITLE = "TITLE"


        fun showDialog(
            fragmentManager: FragmentManager,
            orderNo: String?,
            title: String? = null,
            isShowAutoInStore: Boolean? = false,
            positiveButtonListener: ((String, Boolean) -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(orderNo, title, isShowAutoInStore, positiveButtonListener)
            fragment.show(fragmentManager, TAG)
        }

        fun getCurrentCancelOrderDialog(fragmentManager: FragmentManager): CancelOrderDialog? {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? CancelOrderDialog
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? CancelOrderDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            orderNo: String?,
            title: String? = null,
            isShowAutoInStore: Boolean? = false,
            positiveButtonListener: ((String, Boolean) -> Unit),
        ): CancelOrderDialog {
            val args = Bundle()

            val fragment = CancelOrderDialog()

            args.putBoolean(IS_SHOW_AUTO_IN_STORE, isShowAutoInStore ?: false)

            args.putString(ORDER_NO, orderNo)
            args.putString(TITLE, title)
            fragment.arguments = args
            fragment.positiveButtonListener = positiveButtonListener
            return fragment
        }
    }

}
