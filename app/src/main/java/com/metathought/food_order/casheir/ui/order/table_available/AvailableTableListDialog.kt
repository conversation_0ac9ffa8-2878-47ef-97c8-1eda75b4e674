package com.metathought.food_order.casheir.ui.order.table_available


import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.TableStatusEnum
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreConstants
import com.metathought.food_order.casheir.data.local.PreferenceDataStoreHelper
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.TableOrderStatus
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponse
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.database.dao.ShoppingHelper
import com.metathought.food_order.casheir.databinding.DialogAvailableTableListBinding
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.adapter.AvailableTableAdapter
import com.metathought.food_order.casheir.ui.adapter.FloorAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.order.MenuOrderViewModel
import com.metathought.food_order.casheir.ui.ordered.OrderedViewModel
import com.metathought.food_order.casheir.ui.ordered.tablelist.SwitchTableByOrderDialog
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber

@AndroidEntryPoint
class AvailableTableListDialog : BaseDialogFragment() {
    private var binding: DialogAvailableTableListBinding? = null
    private var itemClickListener: ((TableResponseItem?) -> Unit)? = null

    private var changeOrderLister: ((
        allInUnpaid: Boolean,
        newTable: TableResponseItem?,
        orderNo: String?
    ) -> Unit)? = null
    private val menuOrderViewModel: MenuOrderViewModel by viewModels()

    private val orderViewModel: OrderedViewModel by viewModels()

    private var currentOrderedInfo: OrderedInfoResponse? = null

    private var availableTableAdapter: AvailableTableAdapter? = null
    private var floorAdapter: FloorAdapter? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogAvailableTableListBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
        initObserver()
    }

    private fun showLoading() {
        binding?.run {
            progressBar.isVisible = true
        }
    }

    private fun hideLoading() {
        binding?.run {
            progressBar.isGone = true
        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.7).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.5).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    private var tableList = listOf<TableResponseItem>()

    private fun initObserver() {
        menuOrderViewModel.availableTableList.observe(viewLifecycleOwner) {
            when (it) {
                is ApiResponse.Loading -> {
                    showLoading()
                }

                is ApiResponse.Success -> {
                    hideLoading()

                    if (currentOrderedInfo != null) {
                        //如果是订单换桌  过滤掉当前订单的桌子   选择状态全部置位false
                        tableList = it.data.filter { tableItem ->
                            tableItem.uuid != currentOrderedInfo?.tableUuid
                        }.map { tableItem ->
                            tableItem.select = false
                            tableItem
                        }
                        tableList.toMutableList().sortBy { tableItem -> tableItem.status }

                    } else {
                        tableList = it.data
                    }
                    /**
                     * 排序一下
                     */
                    tableList = tableList.sortedBy { tableItem -> tableItem.status }

                    availableTableAdapter?.updateItems(tableList)
                    binding?.layoutEmptyTable?.root?.isVisible = it.data.isEmpty()

                    var floorArray: ArrayList<String> = arrayListOf()
                    floorArray.add(getString(R.string.all))
                    val tableList = it.data
                    val commontable =
                        tableList.firstOrNull { it.type == 2 && it.status == TableStatusEnum.AVAILABLE.id }
                    tableList.remove(commontable)
                    commontable?.let { it1 -> tableList.add(0, it1) }
                    for (table in tableList) {
                        if (!floorArray.contains(table.location)) {
                            floorArray.add(table.location ?: "")
                        }
                    }
                    floorAdapter = FloorAdapter(floorArray, requireContext(), 0) { floor ->
                        filterFloor(
                            if (floor == 0) "" else floorArray[floor],
                            TableStatusEnum.ALL.id
                        )
                    }
                    binding?.recyclerViewPage?.adapter = floorAdapter

//                    checkEnableBtnConfirm()
                }

                is ApiResponse.Error -> {
                    hideLoading()
                    Toast.makeText(context, "${it.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }

        orderViewModel.uiChangeTableByOrderState.observe(viewLifecycleOwner) { state ->
            state?.apiResponse.let {
                if (it is ApiResponse.Success) {
                    hideLoading()

                    dealOrderChangeTable(it.data, state.newTable)


                } else {
                    if (it is ApiResponse.Loading) {
                        showLoading()
                    } else if (it is ApiResponse.Error) {
                        hideLoading()
                    }
                }
            }
        }
    }

    private fun filterFloor(query: String, status: Int) {
        if (status == TableStatusEnum.ALL.id && query.isNotEmpty()) {
            val filteredList = tableList.filter { table ->
                table.location == query
            }
            val tableResponse = TableResponse()
            filteredList?.let { tableResponse.addAll(it) }
            availableTableAdapter?.updateItems(filteredList)
        } else {
            availableTableAdapter?.updateItems(tableList)
        }

    }


    private fun initData() {
        binding?.apply {
            context?.let {
                lifecycleScope.launch {
                    PreferenceDataStoreHelper.getInstance().apply {
                        val dingingStyle = getFirstPreference(
                            PreferenceDataStoreConstants.DATA_STORE_KEY_CURRENT_DINING_STYLE,
                            -1
                        )

                        withContext(Dispatchers.Main) {
                            availableTableAdapter = AvailableTableAdapter(TableResponse(), it) {
//                                checkEnableBtnConfirm()
                                if (currentOrderedInfo != null) {
                                    orderViewModel.uncompletedOrderCount(
                                        currentOrderedInfo?.tableUuid,
                                        availableTableAdapter?.getSelect(),
                                        currentOrderedInfo?.orderNo,
                                    )

                                } else {
                                    availableTableAdapter?.getSelect()?.let {
                                        itemClickListener?.invoke(availableTableAdapter?.getSelect())
                                    }

                                    dismissAllowingStateLoss()
                                }
                            }
                            recyclerViewTable.adapter = availableTableAdapter
                            if (currentOrderedInfo == null) {
                                val record = ShoppingHelper.get(dingingStyle)
                                availableTableAdapter?.customerPersonNum = record?.peopleNum ?: 0
                                menuOrderViewModel.electFreelist(dingingStyle, record)
                            } else {

                                menuOrderViewModel.electFreelist(
                                    currentOrderedInfo?.diningStyle ?: 0, null
                                )
                            }

                        }

                    }
                }
//                checkEnableBtnConfirm()
            }

//            if (currentOrderedInfo != null) {
//                checkEnableBtnConfirm()
//            }
        }
    }


    private fun initListener() {
        binding?.apply {
            topBar.setOnClickListener {
                dismissAllowingStateLoss()
            }
//            btnCancel.setOnClickListener {
//                dismissAllowingStateLoss()
//            }
//            btnConfirm.setOnClickListener {
//                if (currentOrderedInfo != null) {
//                    orderViewModel.uncompletedOrderCount(
//                        currentOrderedInfo?.tableUuid,
//                        availableTableAdapter?.getSelect(),
//                        currentOrderedInfo?.orderNo,
//                    )
//
//                } else {
//                    availableTableAdapter?.getSelect()?.let {
//                        itemClickListener?.invoke(availableTableAdapter?.getSelect())
//                    }
//
//                    dismissAllowingStateLoss()
//                }
//            }
        }
    }

//    private fun checkEnableBtnConfirm() {
//        binding?.apply {
//            btnConfirm.setEnableWithAlpha(availableTableAdapter?.getSelect() != null)
//            tvInfo.isVisible =
//                availableTableAdapter?.getSelect() == null && availableTableAdapter?.list?.isNotEmpty() == true
//        }
//
//    }


    private fun dealOrderChangeTable(
        data: List<TableOrderStatus>?,
        newTable: TableResponseItem?
    ) {
        //订单换桌
        dialog?.hide()
        //目标桌子，已确认订单数
        var targetConfirmOrderNum = 0
        //目标桌子，待确认订单数
        var targetToBeConfirmOrderNum = 0
        //目标桌子，未支付订单数
        var targetUnpaidOrderNum = 0
        //原桌子，已确认订单数
        var srcConfirmOrderNum = 0
        //原桌子，待确认订单数
        var srcToBeConfirmOrderNum = 0
        //原桌子，未支付订单数
        var srcUnpaidOrderNum = 0
        data?.forEach { item ->
            if (item.tableUuid == newTable?.uuid) {
                if (item.payStatus == OrderedStatusEnum.BE_CONFIRM.id) {
                    targetConfirmOrderNum += item.num ?: 0
                }
                if (item.payStatus == OrderedStatusEnum.UNPAID.id) {
                    targetUnpaidOrderNum += item.num ?: 0
                }
                if (item.payStatus == OrderedStatusEnum.TO_BE_CONFIRM.id) {
                    targetToBeConfirmOrderNum += item.num ?: 0
                }
            }
            if (item.tableUuid == currentOrderedInfo?.tableUuid) {
                if (item.payStatus == OrderedStatusEnum.BE_CONFIRM.id) {
                    srcConfirmOrderNum += item.num ?: 0
                }
                if (item.payStatus == OrderedStatusEnum.UNPAID.id) {
                    srcUnpaidOrderNum += item.num ?: 0
                }
                if (item.payStatus == OrderedStatusEnum.TO_BE_CONFIRM.id) {
                    srcToBeConfirmOrderNum += item.num ?: 0
                }
            }
        }


        val targetOrderNum =
            targetConfirmOrderNum + targetToBeConfirmOrderNum + targetUnpaidOrderNum
        val srcOrderNum =
            srcConfirmOrderNum + srcToBeConfirmOrderNum + srcUnpaidOrderNum

        var dialogType = 0
        //订单是否通用桌的订单
        val isUniversalTableOrder = currentOrderedInfo?.isUniversalTable() ?: false

        if ((currentOrderedInfo?.payStatus == OrderedStatusEnum.UNPAID.id && srcUnpaidOrderNum == 1 && srcOrderNum == 1) || currentOrderedInfo?.isOrderSuccess() == true) {
            //当前桌子仅有这一笔未完成订单，当前桌台仅有这一笔【待支付】订单，目标桌台的订单状态无所谓    或者 先付款门店 已付款的订单换桌
            dialogType = 0
        } else if (currentOrderedInfo?.payStatus in listOf(
                OrderedStatusEnum.TO_BE_CONFIRM.id,
                OrderedStatusEnum.BE_CONFIRM.id
            ) && (srcConfirmOrderNum + srcToBeConfirmOrderNum) <= 1 && srcOrderNum == 1 && (targetToBeConfirmOrderNum + targetConfirmOrderNum == 0)
        ) {
            //当前桌子仅有这一笔未完成订单，当前桌台仅有这一笔【已确认】或【待确认】，且目标桌台没有【已确认】或【待确认】订单
            dialogType = 0
        } else if (srcOrderNum <= 1 && (targetToBeConfirmOrderNum > 0 || targetConfirmOrderNum > 0)) {
            //当前订单对应的桌子仅有一个订单，目标桌子有已确定/待确定订单的时候
            dialogType = 2
        } else {
            dialogType = 1
        }


        Timber.e("dialogType  $dialogType")

        when (dialogType) {
            0 -> {
                SwitchTableByOrderDialog.showDialog(
                    parentFragmentManager,
                    content = getString(
                        R.string.change_table_to,
                        availableTableAdapter?.getSelect()?.name
                    ),
                    leftBtnTitle = getString(R.string.cancel),
                    rightBtnTitle = getString(R.string.confirm2),
                    rightBtnListener = {
//                                    Timber.e("确定2")
                        changeOrderLister?.invoke(
                            false,
                            newTable,
                            currentOrderedInfo?.orderNo
                        )
                        SwitchTableByOrderDialog.dismissDialog(parentFragmentManager)
                        dismissAllowingStateLoss()
                    }
                )
            }

            2 -> {
                SwitchTableByOrderDialog.showDialog(
                    parentFragmentManager,
                    content = getString(
                        R.string.current_table_have_current_order
                    ),
                    leftBtnTitle = getString(R.string.cancel),
                    rightBtnTitle = getString(R.string.confirm2),
                    rightBtnListener = {
                        changeOrderLister?.invoke(
                            false,
                            newTable,
                            currentOrderedInfo?.orderNo
                        )
                        SwitchTableByOrderDialog.dismissDialog(parentFragmentManager)
                        dismissAllowingStateLoss()
                    }
                )

            }

            else -> {
                SwitchTableByOrderDialog.showDialog(
                    parentFragmentManager,
                    content = getString(
                        R.string.current_table_have_order_no_pay,
                    ),
                    leftBtnTitle = getString(R.string.current_order),
                    rightBtnTitle = getString(R.string.all_order),
                    leftBtnListener = {
                        Timber.e("其他情况  当前订单")
                        //仅当前订单， 目标桌没待确认/已确认订单，或者当前订单是待支付订单
                        if ((targetToBeConfirmOrderNum == 0 && targetConfirmOrderNum == 0) || currentOrderedInfo?.payStatus == OrderedStatusEnum.UNPAID.id) {
                            changeOrderLister?.invoke(
                                false,
                                newTable,
                                currentOrderedInfo?.orderNo
                            )
                            SwitchTableByOrderDialog.dismissDialog(parentFragmentManager)
                            dismissAllowingStateLoss()
                        } else {
                            SwitchTableByOrderDialog.showDialog(
                                parentFragmentManager,
                                content = getString(
                                    R.string.current_table_have_current_order
                                ),
                                leftBtnTitle = getString(R.string.cancel),
                                rightBtnTitle = getString(R.string.confirm2),
                                rightBtnListener = {
                                    Timber.e("确定2")
                                    changeOrderLister?.invoke(
                                        false,
                                        newTable,
                                        currentOrderedInfo?.orderNo
                                    )
                                    SwitchTableByOrderDialog.dismissDialog(
                                        parentFragmentManager
                                    )
                                    dismissAllowingStateLoss()
                                }
                            )
                        }

                    },
                    rightBtnListener = {
                        Timber.e("其他情况  所有订单")
                        //所有订单， 目标桌没待确认/已确认订单，或者当前桌没已确认\待确认
                        if ((targetToBeConfirmOrderNum == 0 && targetConfirmOrderNum == 0) || (srcToBeConfirmOrderNum + srcConfirmOrderNum == 0)) {
                            changeOrderLister?.invoke(
                                true,
                                newTable,
                                currentOrderedInfo?.orderNo
                            )
                            SwitchTableByOrderDialog.dismissDialog(parentFragmentManager)
                            dismissAllowingStateLoss()
                        } else {
                            SwitchTableByOrderDialog.showDialog(
                                parentFragmentManager,
                                content = getString(
                                    R.string.current_table_have_current_order
                                ),
                                leftBtnTitle = getString(R.string.cancel),
                                rightBtnTitle = getString(R.string.confirm2),
                                rightBtnListener = {
                                    Timber.e("确定2")
                                    changeOrderLister?.invoke(
                                        true,
                                        newTable,
                                        currentOrderedInfo?.orderNo
                                    )
                                    SwitchTableByOrderDialog.dismissDialog(
                                        parentFragmentManager
                                    )
                                    dismissAllowingStateLoss()
                                }
                            )
                        }
                    }
                )
            }
        }
    }


    companion object {
        private const val AVAILABLE_TABLE_LIST = "AVAILABLE_TABLE_LIST"
//        private const val ACTION = "ACTION_ORDER"

        fun showDialog(
            fragmentManager: FragmentManager,
            currentOrderedInfo: OrderedInfoResponse? = null,
            changeOrderLister: ((
                allInUnpaid: Boolean,
                newTable: TableResponseItem?,
                orderNo: String?
            ) -> Unit)? = null,
            itemClickListener: ((TableResponseItem?) -> Unit)? = null,
        ) {
            var fragment = fragmentManager.findFragmentByTag(AVAILABLE_TABLE_LIST)
            if (fragment != null) {
                Timber.e("这里？？")
                (fragment as? AvailableTableListDialog)?.dialog?.show()

                return
            }
            fragment =
                newInstance(
                    itemClickListener = itemClickListener,
                    currentOrderedInfo = currentOrderedInfo,
                    changeOrderLister = changeOrderLister
                )
            fragment.show(fragmentManager, AVAILABLE_TABLE_LIST)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            var fragment =
                fragmentManager.findFragmentByTag(AVAILABLE_TABLE_LIST) as? AvailableTableListDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            currentOrderedInfo: OrderedInfoResponse? = null,
            changeOrderLister: ((
                allInUnpaid: Boolean,
                newTable: TableResponseItem?,
                orderNo: String?
            ) -> Unit)? = null,
            itemClickListener: (
                (TableResponseItem?) -> Unit)? = null,
        ): AvailableTableListDialog {
            val args = Bundle()
            val fragment = AvailableTableListDialog()
            fragment.itemClickListener = itemClickListener
            fragment.currentOrderedInfo = currentOrderedInfo
            fragment.changeOrderLister = changeOrderLister
            fragment.arguments = args
            return fragment
        }
    }


//    private fun getDisplayMetrics(context: Context): DisplayMetrics {
//        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
//        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
//        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
//        return defaultDisplayContext.resources.displayMetrics
//    }
}
