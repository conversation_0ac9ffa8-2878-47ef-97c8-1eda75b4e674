package com.metathought.food_order.casheir.ui.order.food_detail

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.MealSetGroup
import com.metathought.food_order.casheir.databinding.DialogMealSetTagDelectBinding
import com.metathought.food_order.casheir.ui.adapter.MealSetTagDeleteAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import dagger.hilt.android.AndroidEntryPoint


/**
 *<AUTHOR>
 *@time  2025/1/7
 *@desc  删除规格
 **/

@AndroidEntryPoint
class MealSetTagDeleteDialog : BaseDialogFragment() {
    private var binding: DialogMealSetTagDelectBinding? = null
    private var onConfirmClick: (() -> Unit)? = null
    private var mealSetGroup: MealSetGroup? = null
    private var mealSetGoods: MealSetGoods? = null
    private var isCanRepeat: Boolean? = false
    private var adapter: MealSetTagDeleteAdapter? = null


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogMealSetTagDelectBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)

        initObserver()
        initListener()
        initData()
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        mealSetGoods?.filterEmptyNum()
        onConfirmClick?.invoke()
    }

    private fun initData() {
        binding?.apply {
            tvGoodName.text = mealSetGoods?.name

            context?.let { it ->
                adapter =
                    MealSetTagDeleteAdapter(mealSetGroup, listOf(), it) {
                        checkBtnStatus()
                    }
                adapter?.replace(mealSetGoods?.selectItems ?: listOf())
                recyclerView.adapter = adapter
            }
            checkBtnStatus()
        }
    }

    private fun initObserver() {

    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismiss()
            }
        }
    }

    private fun checkBtnStatus() {
        var enable = true
        mealSetGoods?.tags?.forEach { tag ->
            if (tag.isMustSelect() && !tag.isTagSelect()) {
                enable = false
            }
        }

    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.8).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.4).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    companion object {
        private const val TAG = "MealSetTagDeleteDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            mealSetGroup: MealSetGroup? = null,
            mealSetGoods: MealSetGoods,
            isCanRepeat: Boolean?,
            onConfirmClick: (() -> Unit)? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(
                mealSetGroup,
                mealSetGoods,
                isCanRepeat,
                onConfirmClick
            )
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            var fragment =
                fragmentManager.findFragmentByTag(TAG) as? MealSetTagDeleteDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            mealSetGroup: MealSetGroup? = null,
            mealSetGoods: MealSetGoods,
            isCanRepeat: Boolean?,
            onConfirmClick: (() -> Unit)? = null
        ): MealSetTagDeleteDialog {
            val fragment = MealSetTagDeleteDialog()
            fragment.mealSetGroup = mealSetGroup
            fragment.mealSetGoods = mealSetGoods
            fragment.isCanRepeat = isCanRepeat
            fragment.onConfirmClick = onConfirmClick
            return fragment
        }
    }
}