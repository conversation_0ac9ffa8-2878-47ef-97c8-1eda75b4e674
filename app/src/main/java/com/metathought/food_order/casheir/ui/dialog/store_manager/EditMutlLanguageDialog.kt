package com.metathought.food_order.casheir.ui.dialog.store_manager


import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.data.model.base.response_model.login.StoreInfoResponse
import com.metathought.food_order.casheir.databinding.DialogEditStoreDetailMultLanguageBinding
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import org.greenrobot.eventbus.EventBus


@AndroidEntryPoint
class EditMutlLanguageDialog() : BaseDialogFragment() {

    companion object {
        private const val TAG = "EditMutlLanguageDialog"

        private var storeInfoResponse: StoreInfoResponse? = null

        fun showDialog(
            fragmentManager: FragmentManager,
            storeInfoResponse: StoreInfoResponse? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(storeInfoResponse)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? EditMutlLanguageDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(storeInfoResponse: StoreInfoResponse? = null): EditMutlLanguageDialog {
            val args = Bundle()
            val fragment = EditMutlLanguageDialog()
            this.storeInfoResponse = storeInfoResponse
            fragment.arguments = args
            return fragment
        }
    }

    private var binding: DialogEditStoreDetailMultLanguageBinding? = null

    private val storeManagerViewModel: StoreManagerViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogEditStoreDetailMultLanguageBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        initView()
        initListener()
        initObserver()
        openKeyBoardListener()
        onTouchOutSide(binding?.root)
    }

    private fun initView() {
        storeManagerViewModel.getI18nData(storeInfoResponse!!)
        binding?.apply {
            tvStoreName.text = storeInfoResponse?.name
            tvDesc.text = storeInfoResponse?.description
        }
    }

    private fun initListener() {
        binding?.apply {

            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }


            btnDone.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    storeManagerViewModel.saveI18nData(
                        storeInfoResponse,
                        edtCompanyNameZH.text.toString(),
                        edtCompanyNameEn.text.toString(),
                        edtCompanyNameKm.text.toString(),
                        edtDescZh.text.toString(),
                        edtDescEn.text.toString(),
                        edtDescKm.text.toString()
                    )
                }
            }
        }
    }


    private fun initObserver() {
        storeManagerViewModel.uiSaveState.observe(viewLifecycleOwner) { state ->
            if (state is ApiResponse.Success) {
                EventBus.getDefault().post(SimpleEvent(SimpleEventType.UPDATE_STORE, null))
                dismissAllowingStateLoss()
            } else if (state is ApiResponse.Error) {
                if (!state.message.isNullOrEmpty()) {
                    Toast.makeText(context, "${state.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }

        storeManagerViewModel.uiStoreInfoMultLanguageListState.observe(viewLifecycleOwner) { state ->
            if (state is ApiResponse.Success) {
                val data = state.data
                var nameZH = ""
                var nameEN = ""
                var nameKM = ""
                var descZH = ""
                var descEN = ""
                var descKM = ""
                data.name?.forEach {
                    when (it.language) {
                        "ZH" -> {
                            nameZH = it.value ?: ""
                        }

                        "EN" -> {
                            nameEN = it.value ?: ""
                        }

                        "KM" -> {
                            nameKM = it.value ?: ""
                        }
                    }
                }

                data.description?.forEach {
                    when (it.language) {
                        "ZH" -> {
                            descZH = it.value ?: ""
                        }

                        "EN" -> {
                            descEN = it.value ?: ""
                        }

                        "KM" -> {
                            descKM = it.value ?: ""
                        }
                    }

                }

                binding?.apply {
                    edtCompanyNameZH.setText(nameZH)
                    edtCompanyNameEn.setText(nameEN)
                    edtCompanyNameKm.setText(nameKM)

                    edtDescZh.setText(descZH)
                    edtDescEn.setText(descEN)
                    edtDescKm.setText(descKM)
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight =
                (displayMetrics.heightPixels * 0.9).toInt()
            val screenWidth =
                (displayMetrics.widthPixels * 0.45).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

}

