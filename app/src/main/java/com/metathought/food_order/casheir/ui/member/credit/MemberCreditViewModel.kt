package com.metathought.food_order.casheir.ui.member.credit

import android.content.Context
import android.widget.Toast
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.KitchenCheckTicketType
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.constant.PrintTemplateTypeEnum
import com.metathought.food_order.casheir.constant.PrintTicketType
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.PreSettlementRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.PrinterAgainRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.RepaymentRequest
import com.metathought.food_order.casheir.data.model.base.response_model.member.CreditInfo
import com.metathought.food_order.casheir.data.model.base.response_model.member.CreditRecordVo
import com.metathought.food_order.casheir.data.model.base.response_model.member.RepaymentRecordVo
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterTypeEnum
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.helper.OrderHelper
import com.metathought.food_order.casheir.helper.PaymentMethodHelper
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.helper.PrinterUsbDeviceHelper
import com.metathought.food_order.casheir.listener.ListenableFuture
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.widget.printer.LabelPrinter
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import timber.log.Timber
import java.util.concurrent.ExecutionException
import javax.inject.Inject

@HiltViewModel
class MemberCreditViewModel
@Inject
constructor(private val repository: Repository) : ViewModel() {


    private val _uiRecordsState = MutableLiveData<UIRecordsModel>()
    val uiRecordsState get() = _uiRecordsState
    private var pagNo = 1
    private val pageSize = 20

    data class CreditDetails(
        var creditRecordsList: List<String>? = null,
        var paymentRecordsList: List<String>? = null,
    )

    private val _uiCreditDetailsState = MutableLiveData<ApiResponse<CreditInfo>>()
    val uiCreditDetailsState get() = _uiCreditDetailsState


    //获取挂账详情
    fun getCreditDetails(consumerId: Long?) {
        viewModelScope.launch {
            try {
                _uiCreditDetailsState.value = ApiResponse.Loading
                val response = repository.getCreditInfo(consumerId)
                if (response is ApiResponse.Success) {
                    _uiCreditDetailsState.value = response
                } else if (response is ApiResponse.Error) {
                    _uiCreditDetailsState.value = response
                }
            } catch (e: Exception) {
                _uiCreditDetailsState.value = ApiResponse.Error("")
            }
        }
    }

    fun printAllCreditUnpaidOrders(
        context: Context, consumerId: Long, isRepayment: Boolean
    ) {

        val connectUSB = PrinterUsbDeviceHelper.isPosPrinterConnectUSB()
        connectUSB.addListener(
            object : ListenableFuture.Listener<Boolean> {
                //是否连接了本地USB Printer打印机
                //Is a local USB Printer printer connected?
                override fun onSuccess(isConnected: Boolean) {
                    viewModelScope.launch {
                        try {
                            var isHasWifiConnect = false
                            val printerList = PrinterDeviceHelper.getPrinterList()
                            printerList.forEach {
                                if (it.type == PrinterTypeEnum.WIFI.type && PrinterDeviceHelper.getWifiConnectState(
                                        it
                                    )
                                ) {
                                    isHasWifiConnect = true
                                }
                            }
                            Timber.e("isConnected:${isConnected}   isHasWifiConnect:${isHasWifiConnect}")
                            val isConnPrinter = isConnected || isHasWifiConnect

                            //走还款在线支付获取支付二维码
                            val response = repository.repayment(
                                RepaymentRequest(
                                    consumerId = consumerId,
                                    payType = PayTypeEnum.ONLINE_PAYMENT.id,
                                    isPosPrint = false,
                                    isRepayment = isRepayment
                                )
                            )
                            Timber.d("response: $response")
                            if (response is ApiResponse.Success) {
                                Printer.printPrinterCreditRecordReport(
                                    context,
                                    OrderedStatusEnum.CREDIT_UNPAID,
                                    response.data,
                                )
                            } else if (response is ApiResponse.Error) {
                                Toast.makeText(
                                    context,
                                    response.message,
                                    Toast.LENGTH_SHORT
                                )
                                    .show()
                            }
                        } catch (e: Exception) {

                        }
                    }
                }

                override fun onFailure(e: ExecutionException) {

                }
            }
        )
    }

    fun loadCreditRecords(consumerId: Long, isRefresh: Boolean? = null) {
        viewModelScope.launch {
            if (isRefresh != false) {
                pagNo = 1
                if (isRefresh == null) {
                    emitUIState(showLoading = true)
                }
            }
            try {
                val response =
                    repository.getCreditRecordList(consumerId, pagNo, pageSize)
                withContext(Dispatchers.Main) {
                    if (response is ApiResponse.Success) {
                        val res = response.data.records
                        if (res.isNullOrEmpty()) {
                            emitUIState(
                                showEnd = true,
                                isRefresh = isRefresh,
                                showLoading = false
                            )
                            return@withContext
                        }
                        pagNo++
                        emitUIState(
                            showCreditRecordsSuccess = res,
                            isRefresh = isRefresh,
                            showLoading = false
                        )
                    } else if (response is ApiResponse.Error) {
                        emitUIState(showError = response.message, showLoading = false)
                    }
                }
            } catch (e: Exception) {
                emitUIState(showError = e.message, showLoading = false)
            }
        }
    }

    fun loadPaymentRecords(consumerId: Long, isRefresh: Boolean? = null) {
        viewModelScope.launch {
            if (isRefresh != false) {
                pagNo = 1
                if (isRefresh == null) {
                    emitUIState(showLoading = true)
                }
            }
            try {
                val response =
                    repository.getRepaymentRecordList(consumerId, pagNo, pageSize)
                withContext(Dispatchers.Main) {
                    if (response is ApiResponse.Success) {
                        val res = response.data.records
                        if (res.isNullOrEmpty()) {
                            emitUIState(
                                showEnd = true,
                                isRefresh = isRefresh,
                                showLoading = false
                            )
                            return@withContext
                        }
                        pagNo++
                        emitUIState(
                            showPaymentRecordsSuccess = res,
                            isRefresh = isRefresh,
                            showLoading = false
                        )
                    } else if (response is ApiResponse.Error) {
                        emitUIState(showError = response.message, showLoading = false)
                    }
                }
            } catch (e: Exception) {
                emitUIState(showError = e.message, showLoading = false)
            }
        }
    }

    private suspend fun emitUIState(
        showLoading: Boolean? = null,
        showError: String? = null,
        showPaymentRecordsSuccess: List<RepaymentRecordVo>? = null,
        showCreditRecordsSuccess: List<CreditRecordVo>? = null,
        showEnd: Boolean = false,
        isRefresh: Boolean? = null
    ) {
        val uiModel = UIRecordsModel(
            showLoading,
            showError,
            showPaymentRecordsSuccess,
            showCreditRecordsSuccess,
            showEnd,
            isRefresh
        )
        withContext(Dispatchers.Main) {
            _uiRecordsState.value = uiModel
        }
    }

    data class UIRecordsModel(
        val showLoading: Boolean?,
        val showError: String?,
        val showPaymentRecordsSuccess: List<RepaymentRecordVo>?,
        val showCreditRecordsSuccess: List<CreditRecordVo>?,
        val showEnd: Boolean,
        val isRefresh: Boolean?,
    )

    private val _uiOrderDetailsState = MutableLiveData<ApiResponse<OrderedInfoResponse>>()
    val uiOrderDetailsState get() = _uiOrderDetailsState

    fun getOrderDetails(orderId: String?) {
        viewModelScope.launch {
            try {
                _uiOrderDetailsState.value = ApiResponse.Loading
                val response = repository.orderedInfo(orderId, true)
                if (response is ApiResponse.Success) {
                    _uiOrderDetailsState.value = response
                } else if (response is ApiResponse.Error) {
                    _uiOrderDetailsState.value = response
                }
            } catch (e: Exception) {
                _uiOrderDetailsState.value = ApiResponse.Error("")
            }
        }
    }

}