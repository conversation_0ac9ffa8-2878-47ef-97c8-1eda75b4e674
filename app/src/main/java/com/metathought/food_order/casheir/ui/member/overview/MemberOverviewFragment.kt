package com.metathought.food_order.casheir.ui.member.overview

import android.content.Context
import android.icu.util.Calendar
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import com.github.mikephil.charting.components.AxisBase
import com.github.mikephil.charting.components.LimitLine
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.ValueFormatter
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.MaterialDatePicker
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.ChartTimeType
import com.metathought.food_order.casheir.constant.FORMAT_DATE
import com.metathought.food_order.casheir.constant.FORMAT_DATE_REALIZED
import com.metathought.food_order.casheir.data.model.base.response_model.member.statistic.MemberOverviewVo
import com.metathought.food_order.casheir.data.model.base.response_model.member.statistic.RechargeAmountValue
import com.metathought.food_order.casheir.data.model.base.response_model.member.statistic.RechargeMembersNumValue
import com.metathought.food_order.casheir.databinding.FragmentMemberOverviewBinding
import com.metathought.food_order.casheir.databinding.PopupDateTypeBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigit
import com.metathought.food_order.casheir.extension.getDateTypeEnum
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.ui.common.BaseFragment
import com.metathought.food_order.casheir.ui.member.MemberMainViewModel
import dagger.hilt.android.AndroidEntryPoint
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


@AndroidEntryPoint
class MemberOverviewFragment : BaseFragment() {

    companion object {
        fun newInstance() = MemberOverviewFragment()
    }

    private var _binding: FragmentMemberOverviewBinding? = null
    private val binding get() = _binding
    private val viewModel: MemberMainViewModel by viewModels()
    private var startDateString: String = ""
    private var endDateString: String = ""
    private var type: String = "1"
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentMemberOverviewBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initListener()
        initObserver()
    }

    private fun initObserver() {
        dismissProgress()
        viewModel.statisticState.observe(viewLifecycleOwner) {
            when (it) {
                is ApiResponse.Loading -> {
                    showProgress()
                }

                is ApiResponse.Success -> {
                    dismissProgress()
                    it.data.memberOverviewVo?.let { it1 -> setData(it1) }
                    context?.let { context ->
                        createLineChartMember(
                            context,
                            it.data.memberOverviewLineChartVo?.rechargeMembersNumValueList
                        )
                        createLineChart(
                            context,
                            it.data.memberOverviewLineChartVo?.rechargeAmountValueList
                        )
                    }
                }

                is ApiResponse.Error -> {
                    dismissProgress()
                    it.message?.let {
                        showToast(it)
                    }
                }
            }
        }
    }

    private fun setData(data: MemberOverviewVo) {
        binding?.apply {
            tvAccountBalance.text =
                "$ ${data.currentMemberOverview?.currentAccountBalance?.priceFormatTwoDigitZero()}"
            tvTopupAmount.text =
                "$ ${data.currentMemberOverview?.rechargeAmount?.priceFormatTwoDigitZero()}"
            tvPaidAmount.text =
                "$ ${data.currentMemberOverview?.consumptionAmount?.priceFormatTwoDigitZero()}"
            tvNumberofMember.text = "${data.currentMemberOverview?.currentMembersNum}"
            tvNumberofPaidMember.text = "${data.currentMemberOverview?.consumerMembersNum}"
            tvNumberofTopupMember.text = "${data.currentMemberOverview?.rechargeMembersNum}"

            tvYesterdayAccountBalance.text =
                "${getString(R.string.yesterday)} $ ${data.yesterdayMemberOverview?.currentAccountBalance?.priceFormatTwoDigitZero() ?: "0.00"}"
            tvYesterTopupAmount.text =
                "${getString(R.string.yesterday)} $ ${data.yesterdayMemberOverview?.rechargeAmount?.priceFormatTwoDigitZero() ?: "0.00"}"
            tvYesterdayPaidAmuont.text =
                "${getString(R.string.yesterday)} $ ${data.yesterdayMemberOverview?.consumptionAmount?.priceFormatTwoDigitZero() ?: "0.00"}"
            tvYesterdayNumberofMember.text =
                "${getString(R.string.yesterday)} ${data.yesterdayMemberOverview?.currentMembersNum ?: 0}"
            tvYesterdayNumberofPaidMember.text =
                "${getString(R.string.yesterday)} ${data.yesterdayMemberOverview?.consumerMembersNum ?: 0}"
            tvYesterdayNumberofTopupMember.text =
                "${getString(R.string.yesterday)} ${data.yesterdayMemberOverview?.rechargeMembersNum ?: 0}"

            tvYesterdayAccountBalance.isVisible = type == "1"
            tvYesterTopupAmount.isVisible = type == "1"
            tvYesterdayPaidAmuont.isVisible = type == "1"
            tvYesterdayNumberofMember.isVisible = type == "1"
            tvYesterdayNumberofPaidMember.isVisible = type == "1"
            tvYesterdayNumberofTopupMember.isVisible = type == "1"
        }
    }

    private fun initListener() {
        binding?.apply {
//            setCheckListener()
            tvClearFilter.setOnClickListener {
//                radioToday.isChecked = true
                onSelectDate(isReset = true)
            }
            tvCalendar.setOnClickListener {
                datePickerDialog()
            }

            dropdownFilter.setOnClickListener {
                arrow.animate().rotation(180f).setDuration(200)
                dropdownFilter.setBackgroundResource(R.drawable.background_spinner_top)
                showPopupWindowLanguage(dropdownFilter)
            }
        }
    }

//    private fun FragmentMemberOverviewBinding.setCheckListener() {
//        radioFilter.setOnCheckedChangeListener { group, checkedId ->
//            if (checkedId != -1) {
//                tvCalendar.text = ""
//                tvCalendar.updateCalendarColor()
//
//                startDateString = ""
//                endDateString = ""
//                type = checkedId.getType()
//                getStatistic()
//            }
//        }
//    }

    private fun initView() {
        binding?.apply {
            lineChart.setNoDataText(getString(R.string.no_chart_data_available))
            lineChartMember.setNoDataText(getString(R.string.no_chart_data_available))
            context?.let {
                showProgress()
                getStatistic()
            }
        }
    }

    private fun createLineChart(context: Context, data: List<RechargeAmountValue?>?) {
        binding?.apply {

            lineChart.resetZoom()
            lineChart.fitScreen()
            lineChart.data?.clearValues()
            lineChart.xAxis.valueFormatter = null
            lineChart.notifyDataSetChanged()
            lineChart.clear()
            lineChart.invalidate()
            var entries: ArrayList<Entry> = arrayListOf()
            var listTime: ArrayList<String> = arrayListOf()
            data?.let {
                it
                for (i in 0..<it.size) {
                    val formatRechargeAmount = ((it[i]?.rechargeValue ?: 0) / 100.0).toFloat()
                    entries.add(Entry(i.toFloat(), formatRechargeAmount))

                    listTime.add(it[i]?.statisticDate ?: "")
                }
            }
            val limitLine = LimitLine(30f)
            limitLine.lineColor = ContextCompat.getColor(context, R.color.primaryColor)
            limitLine.lineWidth = 0.5f
            limitLine.enableDashedLine(10f, 10f, 0f);

            // Create a LineDataSet
            val dataSet = LineDataSet(entries, "My Data Set")
            dataSet.color = ContextCompat.getColor(context, R.color.primaryColor)
            dataSet.valueTextColor = ContextCompat.getColor(context, R.color.primaryColor)
            dataSet.valueFormatter = object : ValueFormatter() {
                override fun getPointLabel(entry: Entry?): String {
                    return entry?.y?.decimalFormatTwoDigit() ?: ""
                }
            }
            dataSet.fillDrawable = ContextCompat.getDrawable(context, R.drawable.primary_gradient)
            dataSet.setCircleColor(ContextCompat.getColor(context, R.color.primaryColor))
            dataSet.valueTextSize = 14f
            dataSet.circleRadius = 6f
            dataSet.circleHoleRadius = 3f
            dataSet.setDrawFilled(true)
            // Create a LineData object
            val lineData = LineData(dataSet)

            // Set data to the chart
            lineChart.data = lineData

            // Customize chart appearance
            lineChart.description.isEnabled = false
            lineChart.legend.isEnabled = false
            lineChart.axisRight.setDrawLabels(false)
            lineChart.axisRight.setDrawGridLines(false)
            lineChart.axisRight.setDrawAxisLine(false)
            lineChart.extraTopOffset = 25f
            lineChart.extraRightOffset = 40f
//            lineChart.extraLeftOffset = 25f
            lineChart.extraBottomOffset = 15f
            lineChart.animateY(1000)
            lineChart.xAxis.labelCount = listTime.size
            //lineChart.setScaleMinima(3f,1f)
            when (type) {
                ChartTimeType.TODAY.type -> lineChart.zoomAndCenterAnimated(
                    3.5f,
                    1f,
                    1f,
                    1f,
                    YAxis.AxisDependency.LEFT,
                    500
                )

                ChartTimeType.WEEK.type -> lineChart.zoomAndCenterAnimated(
                    1f,
                    1f,
                    1f,
                    1f,
                    YAxis.AxisDependency.LEFT,
                    500
                )

                ChartTimeType.MONTH.type -> lineChart.zoomAndCenterAnimated(
                    4.5f,
                    1f,
                    1f,
                    1f,
                    YAxis.AxisDependency.LEFT,
                    500
                )

                ChartTimeType.QUARTER.type -> lineChart.zoomAndCenterAnimated(
                    1f,
                    1f,
                    1f,
                    1f,
                    YAxis.AxisDependency.LEFT,
                    500
                )

                ChartTimeType.YEAR.type -> lineChart.zoomAndCenterAnimated(
                    1f,
                    1f,
                    1f,
                    1f,
                    YAxis.AxisDependency.LEFT,
                    500
                )

                else -> {
                    lineChart.zoomAndCenterAnimated(
                        entries.size.div(7f),
                        1f,
                        1f,
                        1f,
                        YAxis.AxisDependency.LEFT,
                        500
                    )
                }
            }
            // Customize X-axis
            val xAxis: XAxis = lineChart.xAxis
            xAxis.removeAllLimitLines()
            xAxis.setDrawGridLines(false)
            xAxis.setDrawAxisLine(false)
            xAxis.setGranularity(1f)
            xAxis.textSize = 14f
            xAxis.textColor = ContextCompat.getColor(context, R.color.black)
            xAxis.position = XAxis.XAxisPosition.BOTTOM
//            xAxis.setLabelCount(entries.size,false)
//            xAxis.labelRotationAngle = -30F
            xAxis.valueFormatter = object : ValueFormatter() {
                override fun getAxisLabel(value: Float, axis: AxisBase?): String {

                    // Customize X-axis labels if needed
                    val index = value.toInt()
                    return if (index == -1 || index >= listTime.size) "" else listTime[index]
                }
            }

            // Customize Y-axis
            val yAxis: YAxis = lineChart.axisLeft
            yAxis.removeAllLimitLines()
            yAxis.gridLineWidth = 1f
            yAxis.gridColor = ContextCompat.getColor(context, R.color.black08)
            yAxis.setDrawAxisLine(false)
            yAxis.axisMinimum = 0f
            val maxAxis = ((data?.sortedBy { it?.rechargeValue }?.reversed()
                ?.firstOrNull()?.rechargeValue ?: 0) / 100.0).toFloat()
            yAxis.axisMaximum = if (maxAxis == 0.0f) 100f else (maxAxis + 1)
            yAxis.textSize = 14f
            yAxis.textColor = ContextCompat.getColor(context, R.color.black)
//            yAxis.textColor = ContextCompat.getColor(context, R.color.black80)
            yAxis.valueFormatter = object : ValueFormatter() {
                override fun getAxisLabel(value: Float, axis: AxisBase?): String {

                    // Customize X-axis labels if needed
                    return "${value.decimalFormatTwoDigit()}    "
                }
            }
            yAxis.labelCount = 8
        }

    }

    private fun createLineChartMember(context: Context, data: List<RechargeMembersNumValue?>?) {
        binding?.apply {
            lineChartMember.resetZoom()
            lineChartMember.fitScreen()
            lineChartMember.data?.clearValues()
            lineChartMember.xAxis.valueFormatter = null
            lineChartMember.notifyDataSetChanged()
            lineChartMember.clear()
            lineChartMember.invalidate()
            var entries: ArrayList<Entry> = arrayListOf()
            var listTime: ArrayList<String> = arrayListOf()
            data?.let {
                it
                for (i in 0..<it.size) {
                    entries.add(Entry(i.toFloat(), it[i]?.rechargeValue?.toFloat() ?: 0.0f))
                    listTime.add(it[i]?.statisticDate ?: "")
                }
            }

            val limitLine = LimitLine(30f)
            limitLine.lineColor = ContextCompat.getColor(context, R.color.primaryColor)
            limitLine.lineWidth = 0.5f
            limitLine.enableDashedLine(10f, 10f, 0f);

            // Create a LineDataSet
            val dataSet = LineDataSet(entries, "My Data Set")
            dataSet.color = ContextCompat.getColor(context, R.color.primaryColor)
            dataSet.valueTextColor = ContextCompat.getColor(context, R.color.primaryColor)
            dataSet.valueFormatter = object : ValueFormatter() {
                override fun getPointLabel(entry: Entry?): String {
                    return entry?.y?.decimalFormatTwoDigit() ?: ""
                }
            }
            dataSet.fillDrawable = ContextCompat.getDrawable(context, R.drawable.primary_gradient)
            dataSet.setCircleColor(ContextCompat.getColor(context, R.color.primaryColor))
            dataSet.valueTextSize = 14f
            dataSet.circleRadius = 6f
            dataSet.circleHoleRadius = 3f
            dataSet.setDrawFilled(true)
            // Create a LineData object
            val lineData = LineData(dataSet)

            // Set data to the chart
            lineChartMember.data = lineData

            // Customize chart appearance
            lineChartMember.description.isEnabled = false
            lineChartMember.legend.isEnabled = false
            lineChartMember.axisRight.setDrawLabels(false)
            lineChartMember.axisRight.setDrawGridLines(false)
            lineChartMember.axisRight.setDrawAxisLine(false)
            lineChartMember.extraTopOffset = 25f
//            lineChartMember.extraTopOffset = 30f
            lineChartMember.extraRightOffset = 40f
//            lineChartMember.extraLeftOffset = 40f
            lineChartMember.extraBottomOffset = 15f
            lineChartMember.animateY(1000)
            lineChartMember.xAxis.labelCount = listTime.size

            when (type) {
                ChartTimeType.TODAY.type -> lineChartMember.zoomAndCenterAnimated(
                    3.5f,
                    1f,
                    1f,
                    1f,
                    YAxis.AxisDependency.LEFT,
                    500
                )

                ChartTimeType.WEEK.type -> lineChartMember.zoomAndCenterAnimated(
                    1f,
                    1f,
                    1f,
                    1f,
                    YAxis.AxisDependency.LEFT,
                    500
                )

                ChartTimeType.MONTH.type -> lineChartMember.zoomAndCenterAnimated(
                    4.5f,
                    1f,
                    1f,
                    1f,
                    YAxis.AxisDependency.LEFT,
                    500
                )

                ChartTimeType.QUARTER.type -> lineChartMember.zoomAndCenterAnimated(
                    1f,
                    1f,
                    1f,
                    1f,
                    YAxis.AxisDependency.LEFT,
                    500
                )

                ChartTimeType.YEAR.type -> lineChartMember.zoomAndCenterAnimated(
                    1f,
                    1f,
                    1f,
                    1f,
                    YAxis.AxisDependency.LEFT,
                    500
                )

                else -> {
                    lineChartMember.zoomAndCenterAnimated(
                        entries.size.div(7f),
                        1f,
                        1f,
                        1f,
                        YAxis.AxisDependency.LEFT,
                        500
                    )
                }
            }
            // Customize X-axis
            val xAxis: XAxis = lineChartMember.xAxis
            xAxis.removeAllLimitLines()
            xAxis.setDrawGridLines(false)
            xAxis.setDrawAxisLine(false)
            xAxis.setGranularity(1f)
            xAxis.textSize = 14f
            xAxis.textColor = ContextCompat.getColor(context, R.color.black)
            xAxis.position = XAxis.XAxisPosition.BOTTOM
//            xAxis.valueFormatter = object : IndexAxisValueFormatter(listTime){}
            xAxis.valueFormatter = object : ValueFormatter() {
                override fun getAxisLabel(value: Float, axis: AxisBase?): String {

                    // Customize X-axis labels if needed
                    val index = value.toInt()
                    return if (index == -1 || index >= listTime.size) "" else listTime[index]
                }
            }

            // Customize Y-axis
            val yAxis: YAxis = lineChartMember.axisLeft
            yAxis.removeAllLimitLines()
            yAxis.gridLineWidth = 1f
            yAxis.gridColor = ContextCompat.getColor(context, R.color.black08)
            yAxis.setDrawAxisLine(false)
            yAxis.axisMinimum = 0f
            var maxAxis = data?.sortedBy { it?.rechargeValue }?.reversed()
                ?.firstOrNull()?.rechargeValue?.toFloat() ?: 10f
            if (maxAxis < 2) {
                maxAxis += 3
            } else if (maxAxis < 3) {
                maxAxis += 2
            }
            yAxis.axisMaximum = if (maxAxis == 0.0f) 10f else (maxAxis + 1)
            yAxis.textSize = 14f
            yAxis.textColor = ContextCompat.getColor(context, R.color.black)
            yAxis.labelCount = 8
//            yAxis.textColor = ContextCompat.getColor(context, R.color.black80)
            yAxis.valueFormatter = object : ValueFormatter() {
                override fun getAxisLabel(value: Float, axis: AxisBase?): String {

                    // Customize X-axis labels if needed
                    return "${value.toInt()}    "
                }
            }
            if (yAxis.axisMaximum < 8) {
                yAxis.labelCount = 4
            } else {
                yAxis.labelCount = 8
            }

        }

    }

    private fun datePickerDialog() {
        // Creating a MaterialDatePicker builder for selecting a date range
        val builder = MaterialDatePicker.Builder.dateRangePicker()
//        type = ""
//        binding?.radioFilter?.clearCheck()
        val constraintsBuilder = CalendarConstraints.Builder()

        // Set the minimum year
        constraintsBuilder.setStart(Calendar.getInstance().apply {
            set(Calendar.YEAR, 2024)
            set(Calendar.MONTH, Calendar.JANUARY)
        }.timeInMillis)

        val constraints = constraintsBuilder.build()
        builder.setCalendarConstraints(constraints)
        // Building the date picker dialog
        val datePicker = builder.build()
        datePicker.addOnPositiveButtonClickListener { selection ->
            // Retrieving the selected start and end dates
            val startDate = selection.first
            val endDate = selection.second
            type = ""
            binding?.run {
                tvType.text = getString(R.string.not_selected)
//                radioFilter.setOnCheckedChangeListener(null)
//                radioFilter.clearCheck()
            }
            // Formatting the selected dates as strings
            val sdf = SimpleDateFormat(FORMAT_DATE_REALIZED, Locale.US)
            startDateString = sdf.format(Date(startDate))
            endDateString = sdf.format(Date(endDate))

            val showSdf = SimpleDateFormat(FORMAT_DATE, Locale.US)
            // Creating the date range string
//            val selectedDateRange = "$startDateString - $endDateString"

            // Displaying the selected date range in the TextView
            binding?.tvCalendar?.text =
                "${showSdf.format(Date(startDate))} - ${showSdf.format(Date(endDate))}"
            binding?.tvCalendar?.updateCalendarColor()
//            type = ""
            getStatistic()
//            binding?.run {
//                req()
//            }
        }
        // Showing the date picker dialog
        datePicker.show(parentFragmentManager, "DATE_PICKER")
    }

    private fun getStatistic() {
        viewModel.getStatisticMember(type, startDateString, endDateString)
    }


    private fun showPopupWindowLanguage(anchorView: View) {
        activity?.hideKeyboard()
        val popupView = PopupDateTypeBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupView.root,
            anchorView.width,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )

        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.showAsDropDown(anchorView)
        popupWindow.setOnDismissListener {
            binding?.arrow?.animate()?.rotation(0f)?.setDuration(200)
            anchorView.setBackgroundResource(R.drawable.background_language_spiner)
        }
        popupView.tvToday.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.today)) {
                binding?.tvType?.text = getString(R.string.today)
                onSelectDate()
            }
            popupWindow.dismiss()

        }
        popupView.tvWeek.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.this_week)) {
                binding?.tvType?.text = getString(R.string.this_week)
                onSelectDate()
            }
            popupWindow.dismiss()

        }
        popupView.tvMonth.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.this_month)) {
                binding?.tvType?.text = getString(R.string.this_month)
                onSelectDate()
            }
            popupWindow.dismiss()
        }
        popupView.tvQuarter.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.this_quarter)) {
                binding?.tvType?.text = getString(R.string.this_quarter)
                onSelectDate()
            }
            popupWindow.dismiss()
        }

        popupView.tvYear.setOnClickListener {
            if (binding?.tvType?.text != getString(R.string.this_year)) {
                binding?.tvType?.text = getString(R.string.this_year)
                onSelectDate()
            }
            popupWindow.dismiss()
        }
        context?.let {
            val type = context?.let { binding?.tvType?.text.toString().getDateTypeEnum(it) }
            when (type) {
                ChartTimeType.TODAY.type -> setSelectedLanguages(popupView.tvToday, it)
                ChartTimeType.WEEK.type -> setSelectedLanguages(popupView.tvWeek, it)
                ChartTimeType.MONTH.type -> setSelectedLanguages(popupView.tvMonth, it)
                ChartTimeType.QUARTER.type -> setSelectedLanguages(popupView.tvQuarter, it)
                ChartTimeType.YEAR.type -> setSelectedLanguages(popupView.tvYear, it)
                else -> {
//                    setSelectedLanguages(popupView.tvToday, it)
                }
            }
        }
    }

    private fun setSelectedLanguages(textView: TextView, context: Context) {
        textView.setBackgroundResource(R.drawable.background_language_selected)
        textView.setTextColor(ContextCompat.getColor(context, R.color.primaryColor))
    }


    private fun onSelectDate(isReset: Boolean? = false) {
        if (isReset == true) {
            binding?.tvType?.text = getString(R.string.today)
        }
        binding?.tvCalendar?.text = ""
        binding?.tvCalendar?.updateCalendarColor()
        type = context?.let { binding?.tvType?.text.toString().getDateTypeEnum(it) } ?: ""
        startDateString = ""
        endDateString = ""
        getStatistic()
    }

}