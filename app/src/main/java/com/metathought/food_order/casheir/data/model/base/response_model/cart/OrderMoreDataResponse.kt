package com.metathought.food_order.casheir.data.model.base.response_model.cart


import android.content.Context
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.data.model.base.request_model.CustomerInfoVo
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.OrderCouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderPrice
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2

data class OrderMoreDataResponse(
    @SerializedName("addGoodsList")
    val addGoodsList: List<Goods>? = null,
    @SerializedName("goodsJsonList")
    val goodsJsonList: GoodsJsonList? = null,
    @SerializedName("customerInfoVo")
    val customerInfoVo: CustomerInfoVo? = null,
    @SerializedName("note")
    val note: String? = null,
    @SerializedName("orderCreateTime")
    val orderCreateTime: String? = null,
    @SerializedName("orderNo")
    val orderNo: String? = null,
    @SerializedName("coupon")
    val coupon: CouponModel? = null,
    @SerializedName("totalGoodsPriceDTO")
    val totalGoodsPriceDTO: GoodsPriceDTO? = null,
    @SerializedName("ordersGoodsPriceDTO")
    val ordersGoodsPriceDTO: GoodsPriceDTO? = null,
    @SerializedName("conversionRatio")
    val conversionRatio: Long? = null,

    @SerializedName("consumePhoneNumber")
    val consumePhoneNumber: String?,

    @SerializedName("customerName")
    val customerName: String?,

    @SerializedName("payStatus")
    val payStatus: Int?,

    ) {

    //支付前
    fun isBeforePay(): Boolean {
        return payStatus == OrderedStatusEnum.UNPAID.id || payStatus == OrderedStatusEnum.TO_BE_CONFIRM.id || payStatus == OrderedStatusEnum.BE_CONFIRM.id
    }

//    fun getConsumePhoneNumber(): Pair<String, String>? {
//        if (!consumePhoneNumber.isNullOrEmpty()) {
//            val list = (consumePhoneNumber ?: "").split(" ")
//            if (list.size == 2) {
//                return Pair(list[0], if (list[1] == "null") "" else list[1])
//            }
//        } else {
//            if (!customerInfoVo?.mobile.isNullOrEmpty() && !customerInfoVo?.areaCode.isNullOrEmpty()) {
//                return Pair(customerInfoVo?.areaCode ?: "", customerInfoVo?.mobile ?: "")
//            }
//        }
//        return null
//    }

    /**
     * 获取顾客的手机号 优先获取填入的，如果没填入的获取注册用户的
     *
     * @return
     */
    fun getConsumePhoneNumber(): Pair<String, String>? {
        if (!customerInfoVo?.mobile.isNullOrEmpty() && !customerInfoVo?.areaCode.isNullOrEmpty()) {
            return Pair(customerInfoVo?.areaCode ?: "", customerInfoVo?.mobile ?: "")
        }

        if (!consumePhoneNumber.isNullOrEmpty()) {
            val list = (consumePhoneNumber ?: "").split(" ")
            if (list.size == 2) {
                return Pair(list[0], if (list[1] == "null") "" else list[1])
            }
        }

        return null
    }

    /**
     * 获取顾客姓名  优先获取 手动输入的，如果没填，则获取注册用户的名字
     *
     * @return
     */
    fun getLastCustomerName(): String {
        if (!customerInfoVo?.name.isNullOrEmpty()) {
            return customerInfoVo?.name ?: ""
        }
        return customerName ?: ""
    }


    fun getOrderPriceNoVat(context: Context): String {
        if (goodsJsonList?.isHasNeedProcess() == true) {
            return "${context.getString(R.string.to_be_confirmed)}"
        }
        return ordersGoodsPriceDTO?.price?.getPayableAmountNoVatToLong()?.priceFormatTwoDigitZero2()
            ?: ""
    }

}

data class GoodsPriceDTO(
    val price: OrderPrice? = null,
    val vipPrice: OrderPrice? = null,
)