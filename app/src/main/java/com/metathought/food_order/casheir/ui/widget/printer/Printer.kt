package com.metathought.food_order.casheir.ui.widget.printer

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.hardware.usb.UsbDevice
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.core.view.get
import androidx.core.view.isGone
import androidx.core.view.isVisible
import com.github.alexzhirkevich.customqrgenerator.QrData
import com.github.alexzhirkevich.customqrgenerator.vector.QrCodeDrawable
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.PrintTemplateTypeEnum
import com.metathought.food_order.casheir.constant.PrintTicketType
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.ShiftReportPrint
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.RepaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.NoPrintModel
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterConfigInfo
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.report.PaymentMethodReportResponse
import com.metathought.food_order.casheir.data.model.base.response_model.report.ProductReportResponse
import com.metathought.food_order.casheir.data.model.base.response_model.table.CreateTempTableCodeResponse
import com.metathought.food_order.casheir.databinding.TicketPrinterTemporaryTableBinding
import com.metathought.food_order.casheir.extension.getStringByLocale
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.OrderHelper
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.helper.PrinterTemplateHelper
import com.metathought.food_order.casheir.helper.PrinterUsbDeviceHelper
import com.metathought.food_order.casheir.helper.getPrinterStatus
import com.metathought.food_order.casheir.listener.ListenableFuture
import com.metathought.food_order.casheir.listener.SettableFuture
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.app_dashbord.selectPrinter
import com.metathought.food_order.casheir.ui.widget.printer.TicketPrinter.PrinterResult
import com.metathought.food_order.casheir.utils.PrinterLogUtils
import com.sankuai.mtcashboxsdk.MTCashboxSdkHelper
import com.sunmi.printerx.api.LineApi
import com.sunmi.printerx.enums.PrinterInfo
import com.sunmi.printerx.style.BitmapStyle
import kotlinx.coroutines.Delay
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import net.posprinter.POSConst
import net.posprinter.POSPrinter
import timber.log.Timber
import java.nio.charset.Charset
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.ExecutionException
import kotlin.coroutines.resume


/**
 * 小票打印和打开钱箱
 * <AUTHOR>
 * @date 2024/5/1717:46
 * @description
 */
object Printer {

    fun getPrintTicketType(): Int {
        return PrintTicketType.NORMAL.id
    }

    /**
     * 判断是否走XPrinter
     * 如果商米打印机为空则走XPrinter
     */
    fun isXPrinter(): Boolean {
        return selectPrinter == null
    }

    /**
     * 打开钱箱
     */
    fun openCashierBox() {
        try {
            Timber.e("现金支付打开钱箱")
            if (isXPrinter()) {
                //美团RJ11连接的第三方钱箱
                MTCashboxSdkHelper.openCashbox()
            } else {
                //商米收银机RJ11连接的第三方钱箱，回调和状态无效，只能发送脉冲打开钱箱
                selectPrinter?.cashDrawerApi()?.open(null)
            }
        } catch (e: Exception) {
            Timber.e("打开钱箱失败")
        }

    }

    fun sunmiIsConnect(): Boolean {
        return try {
            selectPrinter?.queryApi()?.status?.name == "READY"
        } catch (e: Exception) {
            false
        }
    }

    /**
     * usb 是否连接 如果是summi 内置就判断是否有初始化summi  其余的判断usbDevice
     *
     * @return
     */
    fun isConnectUSB(usbDevice: UsbDevice?): ListenableFuture<Boolean> {
        val future = SettableFuture<Boolean>()
        return if (sunmiIsConnect()) {
            future.set(true)
            future
        } else {
            PrinterUsbDeviceHelper.isConnectUSB(usbDevice?.deviceName ?: "")
        }
    }

    /**
     *  判断是否有summi 内置打印机 或者是 小票打印机连接
     *
     * @return
     */
    fun isPosPrinterConnectUSB(): ListenableFuture<Boolean> {
        val future = SettableFuture<Boolean>()
        return if (sunmiIsConnect()) {
            future.set(true)
            future
        } else {
            PrinterUsbDeviceHelper.isPosPrinterConnectUSB()
        }
    }


    //判断商米打印机的纸张规格，是否是80mm
//    private fun isSunmi80mm(): Boolean {
//        if (selectPrinter != null) {
//            val paper = selectPrinter?.queryApi()?.getInfo(PrinterInfo.PAPER)
//            val type = selectPrinter?.queryApi()?.getInfo(PrinterInfo.TYPE)
//            val name = selectPrinter?.queryApi()?.getInfo(PrinterInfo.NAME)
//            return when (paper) {
//                "80mm" -> true
//                else -> false
//            }
//        } else {
//            return false
//        }
//    }

    private fun getPrinterEightyWidth(): Int {
        return if (isXPrinter()) {
            575
        } else {
            try {
                Timber.e(
                    "111111 ${
                        selectPrinter?.queryApi()?.getInfo(PrinterInfo.PAPER)
                    }"
                )
                selectPrinter?.queryApi()?.getInfo(PrinterInfo.PAPER)?.toInt() ?: 380
            } catch (e: Exception) {
                Timber.e(
                    "22222 ${
                        selectPrinter?.queryApi()?.getInfo(PrinterInfo.PAPER)
                    }"
                )
                380
            }
        }
    }

    private fun getPrinterWidth(): Int {
        return if (isXPrinter()) {
            380
        } else {
            try {
                selectPrinter?.queryApi()?.getInfo(PrinterInfo.PAPER)?.toInt() ?: 380
            } catch (e: Exception) {
                380
            }
        }
    }

    //判断商米打印机的纸张规格，是否是58mm
//    fun isSunmi58mm(): Boolean {
//        if (selectPrinter != null) {
//            val paper = selectPrinter?.queryApi()?.getInfo(PrinterInfo.PAPER)
//            val printerType = selectPrinter?.queryApi()?.getInfo(PrinterInfo.TYPE)
//            return when (paper) {
//                "58mm" -> true
//                else -> {
//                    return printerType == PrinterType.THERMAL.toString()
//                }
//            }
//        } else {
//            return false
//        }
//
//    }

    /**
     * 小票导出
     *
     * @param context
     * @param printTemplateResponseItem
     * @param currentOrderedInfo
     * @param isPrinterAgain
     * @param paymentQrCode
     * @return
     */
    fun exportTicket(
        context: Context,
        printTemplateResponseItem: PrintTamplateResponseItem,
        currentOrderedInfo: OrderedInfoResponse?,
        isPrinterAgain: Boolean = false,
        paymentQrCode: String? = null
    ): PrinterResult? {
        var copyOrderInfo = currentOrderedInfo?.copy()
        val isOrderMore = copyOrderInfo?.currentOrderMoreList?.isNotEmpty() == true
        val isKitchen =
            printTemplateResponseItem.type == PrintTemplateTypeEnum.KITCHEN.id
        copyOrderInfo?.goods =
            OrderHelper.mergeGoodsForPrint(copyOrderInfo, isKitchen)
        //判断订单中是否只有待称重的菜
        var onlyToBeWeighed = false
        copyOrderInfo?.let { it ->
            val unProcessCount = if (isOrderMore) {
                it.currentOrderMoreList?.count { !it.isHasProcessed() }
            } else {

                it.goods?.count { !it.isHasProcessed() }
            }
            Timber.e("isOrderMore: $isOrderMore   toBeWeighedCount: $unProcessCount   goods:${it.goods?.size}")
            onlyToBeWeighed = if (isOrderMore) {
                unProcessCount == it.currentOrderMoreList?.size
            } else {
                unProcessCount == it.goods?.size
            }
        }
        if (onlyToBeWeighed) {
            //只有待称重的菜不打印
            Timber.e("只有待称重的菜不打印")
//            PrintQueueManager.onPrintTaskCompleted()
            return null
        }
        val printerResult = TicketPrinter.initTicketBitmap(
            context,
            printTemplateResponseItem,
            copyOrderInfo,
            isPrinterAgain,
            paymentQrCode = paymentQrCode,
            isEightyWidth = true
        )
        return printerResult
    }

    /**
     * 打印小票
     *
     * @param context
     * @param printTemplateResponseItem
     * @param currentOrderedInfo
     * @param isPrinterAgain
     * @param paymentQrCode
     * @param printerConfigInfo
     * @param timeStamp
     */
    fun printTicket(
        context: Context,
        printTemplateResponseItem: PrintTamplateResponseItem,
        currentOrderedInfo: OrderedInfoResponse?,
        isPrinterAgain: Boolean = false,
        paymentQrCode: String? = null,
        printerConfigInfo: PrinterConfigInfo?,
        timeStamp: Long? = Date().time,
    ) {
        var copyOrderInfo = currentOrderedInfo?.copy()
        val copies = 1  //PrinterHelper.getUsbPrinterInfo()?.copies ?: 1
        Timber.e("打印几份  $copies")
        //有加购，优先打印加购
        val isOrderMore = copyOrderInfo?.currentOrderMoreList?.isNotEmpty() == true
        //判断订单中是否只有待称重的菜
        var onlyToBeWeighed = false

        val isKitchen =
            printTemplateResponseItem.type == PrintTemplateTypeEnum.KITCHEN.id
        copyOrderInfo?.goods =
            OrderHelper.mergeGoodsForPrint(copyOrderInfo, isKitchen)

        copyOrderInfo?.let { it ->
            val toBeWeighedCount = if (isOrderMore) {
                it.currentOrderMoreList?.count { !it.isHasProcessed() }
            } else {
                it.goods?.count { !it.isHasProcessed() }
            }
            Timber.e("isOrderMore: $isOrderMore   toBeWeighedCount: $toBeWeighedCount   goods:${it.goods?.size}")
            onlyToBeWeighed = if (isOrderMore) {
                toBeWeighedCount == it.currentOrderMoreList?.size
            } else {
                toBeWeighedCount == it.goods?.size
            }
        }
        if (onlyToBeWeighed) {
            //只有待称重的菜不打印
            Timber.e("只有待称重的菜不打印")
            return
        }
        //没有打印机测试
//        initPrinterTicketBitmap(
//            context,
//            printTemplateResponseItem,
//            currentOrderedInfo,
//            isPrinterAgain
//        )?.let {
//            MyApplication.myAppInstance.orderedScreen?.setBitmap(it)
//        }

        if (isKitchen) {
            Timber.e("${printerConfigInfo?.ipAddress} 准备打印厨打")
//            //这里去判断是否是厨打，如果是厨打，则防止队列阻塞
            val kitchenFinalList = TicketPrinter.getKitchenFinalList(
                TicketPrinter.getOrderedGoods(
                    copyOrderInfo?.currentOrderMoreList?.isNotEmpty() == true,
                    copyOrderInfo!!.clone()
                ), printTemplateResponseItem
            )
            //是否满足打印赠品条件
            val isCanPrintZp =
                copyOrderInfo.getZsCouponValid() && !isOrderMore && copyOrderInfo?.isAfterPayStatus() == true

            val pairModel =
                PrinterTemplateHelper.getKitchenPrintModel(printTemplateResponseItem)
            //是否合并打印
            var isMergePrint = pairModel.first
            //是否分开打印
            var isSplitPrint = pairModel.second

            Timber.e("ip:${printerConfigInfo?.ipAddress}   isMergePrint:${isMergePrint}   isSplitPrint:${isSplitPrint}")

            if (isMergePrint) {
                //合并打印下  如果 正常商品没有就 不打印了
                if (kitchenFinalList.isEmpty()) {
                    isMergePrint = false
                }
            }

            if (isSplitPrint) {
                //厨打且菜品分开打印
                if (isOrderMore) {
                    //如果加购里的商品没有当前打印机需要打印的商品 就不打印了
                    if (kitchenFinalList.isEmpty()) {
                        isSplitPrint = false
                    }
                } else {
                    //过滤出当前打印机能打的菜品
                    val giftFilterList = copyOrderInfo?.getGiftGoodsList()
                        ?.filter { it.kitchenMaking == true && it.storeKitchenId == printTemplateResponseItem.storeKitchenId }

                    if (kitchenFinalList.isEmpty() && (giftFilterList.isNullOrEmpty() || (giftFilterList.isNotEmpty() && !isCanPrintZp))) {
                        //如果 当前没有菜品 && （没有赠品 || 有赠品 但是不满足打印赠品条件） 就不分开打印
                        isSplitPrint = false
                    }
                }
            }
            Timber.e("${printerConfigInfo?.ipAddress} =>  判断结束  isSplitPrint:${isSplitPrint}  isMergePrint:${isMergePrint}     ${kitchenFinalList.size}")
            if (!isSplitPrint && !isMergePrint) {
                Timber.e("${printerConfigInfo?.ipAddress} =>  :${printTemplateResponseItem.storeKitchenName} : 不需要合并打印 也不需要 分开打印")
                return
            }
            Timber.e("这个分区 id:${printTemplateResponseItem.storeKitchenId}  name:${printTemplateResponseItem.storeKitchenName}  有打印菜品 ")
        }

//        if (printerConfigInfo?.type == PrinterTypeEnum.USB.type) {
//            val deviceName = printerConfigInfo.usbDevice?.deviceName ?: "unknown"
//            // 创建优化的打印任务
//            val optimizedPrintTask = USBPrintTask(
//                deviceName = deviceName,
//                task = {
//                    executePrintTask(
//                        deviceName,
//                        printTemplateResponseItem,
//                        context,
//                        copyOrderInfo,
//                        isPrinterAgain,
//                        printerConfigInfo,
//                        isOrderMore,
//                        paymentQrCode
//                    )
//                },
//                priority = if (printTemplateResponseItem.type == PrintTemplateTypeEnum.KITCHEN.id) {
//                    USBPrintTask.HIGH_PRIORITY
//                } else {
//                    USBPrintTask.DEFAULT_PRIORITY
//                }
//            )
//            // 添加到优化的队列管理器
//            USBPrintQueueManager.addPrintTask(optimizedPrintTask)
//        } else if (printerConfigInfo?.type == PrinterTypeEnum.WIFI.type) {
//            Timber.e("Chetwyn 先走的wifi 639")


        if (isKitchen) {
            val pairModel =
                PrinterTemplateHelper.getKitchenPrintModel(printTemplateResponseItem!!)
            //是否合并打印
            val isMergePrint = pairModel.first
            //是否分开打印
            val isSplitPrint = pairModel.second

            if (isMergePrint) {
                if (printerConfigInfo?.type == PrinterTypeEnum.USB.type) {
                    NewUsbPrintQueueManager.addTask(
                        NewUsbPrintTask(
                            noPrintModel = NoPrintModel(
                                printTemplateResponseItem = printTemplateResponseItem,
                                currentOrderedInfo = currentOrderedInfo?.clone(),
                                isPrinterAgain = isPrinterAgain,
                                paymentQrCode = paymentQrCode,
                                printerConfigInfo = printerConfigInfo,
                                isOrderMore = isOrderMore,
                                timeStamp = timeStamp ?: Date().time,
                                isMargePrint = true
                            )
                        )
                    )
                } else {
                    WifiPrintQueueManager.addTask(
                        PrintTask(
                            noPrintModel = NoPrintModel(
                                printTemplateResponseItem = printTemplateResponseItem,
                                currentOrderedInfo = currentOrderedInfo?.clone(),
                                isPrinterAgain = isPrinterAgain,
                                paymentQrCode = paymentQrCode,
                                printerConfigInfo = printerConfigInfo,
                                isOrderMore = isOrderMore,
                                timeStamp = timeStamp ?: Date().time,
                                isMargePrint = true
                            )
                        )
                    )
                }

            }

            if (isSplitPrint) {
                //厨打且菜品分开打印
                if (isOrderMore == true) {
                    currentOrderedInfo?.currentOrderMoreList?.forEachIndexed { index, orderedGoods ->
                        Timber.e("it  $index")
                        val orderInfo = currentOrderedInfo.clone()
                        orderInfo?.currentOrderMoreList = ArrayList<OrderedGoods>()
                        if (orderedGoods.orderMealSetGoodsDTOList.isNullOrEmpty() || (orderedGoods.orderMealSetGoodsDTOList?.isNotEmpty() == true && orderedGoods.isKitchenMergePrint())) {
                            //非套餐 或者 套餐 需要 合并打印
                            val cloneOrderedGoods = orderedGoods.clone()
                            orderInfo?.currentOrderMoreList?.add(cloneOrderedGoods)
                            if (printerConfigInfo?.type == PrinterTypeEnum.USB.type) {
                                NewUsbPrintQueueManager.addTask(
                                    NewUsbPrintTask(
                                        noPrintModel = NoPrintModel(
                                            printTemplateResponseItem = printTemplateResponseItem,
                                            currentOrderedInfo = orderInfo,
                                            isPrinterAgain = isPrinterAgain,
                                            paymentQrCode = paymentQrCode,
                                            printerConfigInfo = printerConfigInfo,
                                            isOrderMore = isOrderMore,
                                            timeStamp = timeStamp ?: Date().time,
                                            isMargePrint = false
                                        )
                                    )
                                )
                            } else {
                                WifiPrintQueueManager.addTask(
                                    PrintTask(
                                        noPrintModel = NoPrintModel(
                                            printTemplateResponseItem = printTemplateResponseItem,
                                            currentOrderedInfo = orderInfo,
                                            isPrinterAgain = isPrinterAgain,
                                            paymentQrCode = paymentQrCode,
                                            printerConfigInfo = printerConfigInfo,
                                            isOrderMore = isOrderMore,
                                            timeStamp = timeStamp ?: Date().time,
                                            isMargePrint = false
                                        )
                                    )
                                )
                            }

                        } else {
                            Timber.e("套餐 分开打印 ${orderedGoods.isKitchenMergePrint()}")
                            //套餐 切套餐内的商品 要分开打印
                            orderedGoods.orderMealSetGoodsDTOList?.forEach {
                                val cloneOrderedGoods = orderedGoods.clone()
                                cloneOrderedGoods.orderMealSetGoodsDTOList = listOf(it)
                                orderInfo?.currentOrderMoreList?.clear()
                                orderInfo?.currentOrderMoreList?.add(cloneOrderedGoods)
                                if (printerConfigInfo?.type == PrinterTypeEnum.USB.type) {
                                    NewUsbPrintQueueManager.addTask(
                                        NewUsbPrintTask(
                                            noPrintModel = NoPrintModel(
                                                printTemplateResponseItem = printTemplateResponseItem,
                                                currentOrderedInfo = orderInfo,
                                                isPrinterAgain = isPrinterAgain,
                                                paymentQrCode = paymentQrCode,
                                                printerConfigInfo = printerConfigInfo,
                                                isOrderMore = isOrderMore,
                                                timeStamp = timeStamp ?: Date().time,
                                                isMargePrint = false
                                            )
                                        )
                                    )
                                } else {
                                    WifiPrintQueueManager.addTask(
                                        PrintTask(
                                            noPrintModel = NoPrintModel(
                                                printTemplateResponseItem = printTemplateResponseItem,
                                                currentOrderedInfo = orderInfo,
                                                isPrinterAgain = isPrinterAgain,
                                                paymentQrCode = paymentQrCode,
                                                printerConfigInfo = printerConfigInfo,
                                                isOrderMore = isOrderMore,
                                                timeStamp = timeStamp ?: Date().time,
                                                isMargePrint = false
                                            )
                                        )
                                    )
                                }

                            }
                        }
                    }
                } else {
                    currentOrderedInfo?.goods?.forEachIndexed { index, orderedGoods ->
                        Timber.e("it  $index")
                        val orderInfo = currentOrderedInfo.clone()
                        orderInfo?.goods?.clear()
                        //分开打这里要清掉赠品
                        orderInfo?.giftGoods = listOf()
                        if (orderedGoods.orderMealSetGoodsDTOList.isNullOrEmpty() || (orderedGoods.orderMealSetGoodsDTOList?.isNotEmpty() == true && orderedGoods.isKitchenMergePrint())) {
                            //非套餐
                            Timber.e("套餐 正常商品打印 ${orderedGoods.isKitchenMergePrint()}")
                            val cloneOrderedGoods = orderedGoods.clone()
                            orderInfo?.goods?.add(cloneOrderedGoods)
                            if (printerConfigInfo?.type == PrinterTypeEnum.USB.type) {
                                NewUsbPrintQueueManager.addTask(
                                    NewUsbPrintTask(
                                        noPrintModel = NoPrintModel(
                                            printTemplateResponseItem = printTemplateResponseItem,
                                            currentOrderedInfo = orderInfo,
                                            isPrinterAgain = isPrinterAgain,
                                            paymentQrCode = paymentQrCode,
                                            printerConfigInfo = printerConfigInfo,
                                            isOrderMore = isOrderMore,
                                            timeStamp = timeStamp ?: Date().time,
                                            isMargePrint = false
                                        )
                                    )
                                )
                            } else {
                                WifiPrintQueueManager.addTask(
                                    PrintTask(
                                        noPrintModel = NoPrintModel(
                                            printTemplateResponseItem = printTemplateResponseItem,
                                            currentOrderedInfo = orderInfo,
                                            isPrinterAgain = isPrinterAgain,
                                            paymentQrCode = paymentQrCode,
                                            printerConfigInfo = printerConfigInfo,
                                            isOrderMore = isOrderMore,
                                            timeStamp = timeStamp ?: Date().time,
                                            isMargePrint = false
                                        )
                                    )
                                )
                            }
                        } else {
                            Timber.e("套餐 分开打印 ${orderedGoods.isKitchenMergePrint()}")
                            //套餐 切套餐内的商品 要分开打印
                            orderedGoods.orderMealSetGoodsDTOList?.forEach {
                                val cloneOrderedGoods = orderedGoods.clone()
                                cloneOrderedGoods.orderMealSetGoodsDTOList = listOf(it)
                                orderInfo?.goods?.clear()
                                orderInfo?.goods?.add(cloneOrderedGoods)
                                if (printerConfigInfo?.type == PrinterTypeEnum.USB.type) {
                                    NewUsbPrintQueueManager.addTask(
                                        NewUsbPrintTask(
                                            noPrintModel = NoPrintModel(
                                                printTemplateResponseItem = printTemplateResponseItem,
                                                currentOrderedInfo = orderInfo,
                                                isPrinterAgain = isPrinterAgain,
                                                paymentQrCode = paymentQrCode,
                                                printerConfigInfo = printerConfigInfo,
                                                isOrderMore = isOrderMore,
                                                timeStamp = timeStamp ?: Date().time,
                                                isMargePrint = false
                                            )
                                        )
                                    )
                                } else {
                                    WifiPrintQueueManager.addTask(
                                        PrintTask(
                                            noPrintModel = NoPrintModel(
                                                printTemplateResponseItem = printTemplateResponseItem,
                                                currentOrderedInfo = orderInfo,
                                                isPrinterAgain = isPrinterAgain,
                                                paymentQrCode = paymentQrCode,
                                                printerConfigInfo = printerConfigInfo,
                                                isOrderMore = isOrderMore,
                                                timeStamp = timeStamp ?: Date().time,
                                                isMargePrint = false
                                            )
                                        )
                                    )
                                }

                            }
                        }
                    }
                    currentOrderedInfo?.getGiftGoodsList()
                        ?.forEachIndexed { index, usageGoods ->
                            val orderInfo = currentOrderedInfo?.clone()
                            orderInfo?.goods?.clear()
                            orderInfo?.giftGoods = listOf(usageGoods)
                            if (printerConfigInfo?.type == PrinterTypeEnum.USB.type) {
                                NewUsbPrintQueueManager.addTask(
                                    NewUsbPrintTask(
                                        noPrintModel = NoPrintModel(
                                            printTemplateResponseItem = printTemplateResponseItem,
                                            currentOrderedInfo = orderInfo,
                                            isPrinterAgain = isPrinterAgain,
                                            paymentQrCode = paymentQrCode,
                                            printerConfigInfo = printerConfigInfo,
                                            isOrderMore = isOrderMore,
                                            timeStamp = timeStamp ?: Date().time,
                                            isMargePrint = false
                                        )
                                    )
                                )
                            } else {
                                WifiPrintQueueManager.addTask(
                                    PrintTask(
                                        noPrintModel = NoPrintModel(
                                            printTemplateResponseItem = printTemplateResponseItem,
                                            currentOrderedInfo = orderInfo,
                                            isPrinterAgain = isPrinterAgain,
                                            paymentQrCode = paymentQrCode,
                                            printerConfigInfo = printerConfigInfo,
                                            isOrderMore = isOrderMore,
                                            timeStamp = timeStamp ?: Date().time,
                                            isMargePrint = false
                                        )
                                    )
                                )
                            }
                        }

                }
            }
        } else {
            if (printerConfigInfo?.type == PrinterTypeEnum.USB.type) {
                NewUsbPrintQueueManager.addTask(
                    NewUsbPrintTask(
                        noPrintModel = NoPrintModel(
                            printTemplateResponseItem = printTemplateResponseItem,
                            currentOrderedInfo = copyOrderInfo,
                            isPrinterAgain = isPrinterAgain,
                            paymentQrCode = paymentQrCode,
                            printerConfigInfo = printerConfigInfo,
                            isOrderMore = isOrderMore,
                            timeStamp = timeStamp ?: Date().time,
                        )
                    )
                )
            } else {
                WifiPrintQueueManager.addTask(
                    PrintTask(
                        noPrintModel = NoPrintModel(
                            printTemplateResponseItem = printTemplateResponseItem,
                            currentOrderedInfo = copyOrderInfo,
                            isPrinterAgain = isPrinterAgain,
                            paymentQrCode = paymentQrCode,
                            printerConfigInfo = printerConfigInfo,
                            isOrderMore = isOrderMore,
                            timeStamp = timeStamp ?: Date().time,
                        )
                    )
                )
            }
//            }
        }
    }


    /**
     * 最后打印的逻辑 非sunmi 的wifi 和usd 打印逻辑
     */
    fun finalPrintHandler(
        printTemplateResponseItem: PrintTamplateResponseItem?,
        context: Context,
        currentOrderedInfo: OrderedInfoResponse?,
        isPrinterAgain: Boolean? = false,
        printerConfigInfo: PrinterConfigInfo? = null,
        isOrderMore: Boolean? = false,
        paymentQrCode: String? = null,
        isMargePrint: Boolean? = null,
        callback: (PrinterResult?) -> Unit = {}
    ) {
        val isKitchen =
            printTemplateResponseItem?.type == PrintTemplateTypeEnum.KITCHEN.id
        if (isKitchen) {
            val pairModel =
                PrinterTemplateHelper.getKitchenPrintModel(printTemplateResponseItem!!)
            if (isMargePrint != null) {
                val result = TicketPrinter.initKitchenBitmap(
                    context,
                    printTemplateResponseItem,
                    currentOrderedInfo?.clone(),
                    isPrinterAgain!!,
                    printerConfigInfo, isMargePrint
                )
                if (result == null) {
                    callback?.invoke(null)
                } else {
                    callback?.invoke(result)
                }
            } else {
                //是否合并打印
                val isMergePrint = pairModel.first
                //是否分开打印
                val isSplitPrint = pairModel.second
                if (isMergePrint) {
                    val result = TicketPrinter.initKitchenBitmap(
                        context,
                        printTemplateResponseItem,
                        currentOrderedInfo?.clone(),
                        isPrinterAgain!!,
                        printerConfigInfo, true
                    )
                    if (result == null) {
                        callback?.invoke(null)
                    } else {
                        callback?.invoke(result)
                    }
                }

                if (isSplitPrint) {
                    //厨打且菜品分开打印
                    if (isOrderMore == true) {
                        currentOrderedInfo?.currentOrderMoreList?.forEachIndexed { index, orderedGoods ->
                            Timber.e("it  $index")
                            val orderInfo = currentOrderedInfo.clone()
                            orderInfo?.currentOrderMoreList = ArrayList<OrderedGoods>()

                            if (orderedGoods.orderMealSetGoodsDTOList.isNullOrEmpty() || (orderedGoods.orderMealSetGoodsDTOList?.isNotEmpty() == true && orderedGoods.isKitchenMergePrint())) {
                                //非套餐 或者 套餐 需要 合并打印
                                val cloneOrderedGoods = orderedGoods.clone()
                                orderInfo?.currentOrderMoreList?.add(cloneOrderedGoods)
                                val result = TicketPrinter.initKitchenBitmap(
                                    context,
                                    printTemplateResponseItem!!,
                                    orderInfo,
                                    isPrinterAgain!!,
                                    printerConfigInfo, false
                                )
                                if (result == null) {
                                    callback?.invoke(null)
                                } else {
                                    callback?.invoke(result)
                                }
                            } else {
                                Timber.e("套餐 分开打印 ${orderedGoods.isKitchenMergePrint()}")
                                //套餐 切套餐内的商品 要分开打印
                                orderedGoods.orderMealSetGoodsDTOList?.forEach {
                                    val cloneOrderedGoods = orderedGoods.clone()
                                    cloneOrderedGoods.orderMealSetGoodsDTOList = listOf(it)
                                    orderInfo?.currentOrderMoreList?.clear()
                                    orderInfo?.currentOrderMoreList?.add(cloneOrderedGoods)
                                    val result = TicketPrinter.initKitchenBitmap(
                                        context,
                                        printTemplateResponseItem!!,
                                        orderInfo,
                                        isPrinterAgain!!,
                                        printerConfigInfo, false
                                    )
                                    if (result == null) {
                                        callback?.invoke(null)
                                    } else {
                                        callback?.invoke(result)
                                    }
                                }
                            }

                        }
                    } else {
                        currentOrderedInfo?.goods?.forEachIndexed { index, orderedGoods ->
                            Timber.e("it  $index")
                            val orderInfo = currentOrderedInfo.clone()
                            orderInfo?.goods?.clear()
                            //分开打这里要清掉赠品
                            orderInfo?.giftGoods = listOf()
                            if (orderedGoods.orderMealSetGoodsDTOList.isNullOrEmpty() || (orderedGoods.orderMealSetGoodsDTOList?.isNotEmpty() == true && orderedGoods.isKitchenMergePrint())) {
                                //非套餐
                                val cloneOrderedGoods = orderedGoods.clone()
                                orderInfo?.goods?.add(cloneOrderedGoods)
                                val result = TicketPrinter.initKitchenBitmap(
                                    context,
                                    printTemplateResponseItem!!,
                                    orderInfo,
                                    isPrinterAgain!!,
                                    printerConfigInfo, false
                                )
                                if (result == null) {
                                    callback?.invoke(null)
                                } else {
                                    callback?.invoke(result)
                                }
                            } else {
                                Timber.e("套餐 分开打印 ${orderedGoods.isKitchenMergePrint()}")
                                //套餐 切套餐内的商品 要分开打印
                                orderedGoods.orderMealSetGoodsDTOList?.forEach {
                                    val cloneOrderedGoods = orderedGoods.clone()
                                    cloneOrderedGoods.orderMealSetGoodsDTOList = listOf(it)
                                    orderInfo?.goods?.clear()
                                    orderInfo?.goods?.add(cloneOrderedGoods)
                                    val result = TicketPrinter.initKitchenBitmap(
                                        context,
                                        printTemplateResponseItem!!,
                                        orderInfo,
                                        isPrinterAgain!!,
                                        printerConfigInfo, false
                                    )
                                    if (result == null) {
                                        callback?.invoke(null)
                                    } else {
                                        callback?.invoke(result)
                                    }
                                }

                            }
                        }
                        currentOrderedInfo?.getGiftGoodsList()
                            ?.forEachIndexed { index, usageGoods ->
                                val orderInfo = currentOrderedInfo?.clone()
                                orderInfo?.goods?.clear()
                                orderInfo?.giftGoods = listOf(usageGoods)
                                val result = TicketPrinter.initKitchenBitmap(
                                    context,
                                    printTemplateResponseItem!!,
                                    orderInfo,
                                    isPrinterAgain!!,
                                    printerConfigInfo, false
                                )
                                if (result == null) {
                                    callback?.invoke(null)
                                } else {
                                    callback?.invoke(result)
                                }
                            }
                    }
                }
            }
        } else {
            val result = TicketPrinter.initTicketBitmap(
                context,
                printTemplateResponseItem!!,
                currentOrderedInfo,
                isPrinterAgain!!,
                paymentQrCode = paymentQrCode,
                isEightyWidth = printerConfigInfo?.isEightyWidth()
            )
            if (result == null) {
                callback?.invoke(null)
            } else {
                callback?.invoke(result)
            }
        }
    }


    //summi 打印小票
    fun printBySummi(printer: LineApi?, printerResult: PrinterResult?, copies: Int? = 1) {
        printer?.apply {
            printerResult?.bitmap?.let {
                for (i in 0..<copies!!) {
                    val tmpBitmap = it?.copy(it.config, false)
                    if (tmpBitmap != null) {
                        printBitmap(tmpBitmap, BitmapStyle.getStyle())
                        autoOut()
                    }
                }
                it?.recycle()
            }
        }
    }

    suspend fun printByPrinter(
        printer: POSPrinter?,
        printerResult: PrinterResult?,
        noPrintModel: NoPrintModel
    ): Boolean {
        printer?.apply {
            // 打印前校验打印机状态（挂起）
            initializePrinter()
            if (noPrintModel?.printerConfigInfo?.usbDevice == null) {
                //如果是wifi这边等看看会不会有收到断掉的情况
                delay(100)
            }
            val printerStatus = getPrinterStatus()
            Timber.e("printByPrinter  $printerStatus")
            if (printerStatus != POSConst.STS_NORMAL) {
                if (noPrintModel.printerConfigInfo?.usbDevice != null) {
                    PrinterLogUtils.writeUsbPrintLog(
                        printerIp = noPrintModel.printerConfigInfo?.usbDevice?.deviceName ?: "",
                        taskInfo = noPrintModel.getPrintContentAsJson(),
                        status = PrinterLogUtils.PrintStatus.PRINT_FAILURE,
                        message = "printByPrinter状态异常，状态码：$printerStatus，跳过打印"
                    )
                } else {
                    PrinterLogUtils.writePrintLog(
                        printerIp = noPrintModel.printerConfigInfo?.ipAddress ?: "",
                        taskInfo = noPrintModel.getPrintContentAsJson(),
                        status = PrinterLogUtils.PrintStatus.PRINT_FAILURE,
                        message = "printByPrinter状态异常，状态码：$printerStatus，跳过打印"
                    )
                }
                return false
            }
//            delay(500)
            val safeCopies = noPrintModel?.printTemplateResponseItem?.copies ?: 1
            printerResult?.bitmap?.let { originalBitmap ->
                for (i in 0 until safeCopies) {
                    val tmpBitmap = originalBitmap.copy(originalBitmap.config, false)
                    tmpBitmap?.let {
                        if (noPrintModel?.printerConfigInfo?.usbDevice != null) {
                            PrinterLogUtils.writeUsbPrintLog(
                                printerIp = noPrintModel.printerConfigInfo?.usbDevice?.deviceName
                                    ?: "",
                                taskInfo = noPrintModel.getPrintContentAsJson(),
                                status = PrinterLogUtils.PrintStatus.PRINT_SEND,
                                message = "正在发送  copies:${i + 1}/${noPrintModel.printTemplateResponseItem?.copies}"
                            )
                        } else {
                            PrinterLogUtils.writePrintLog(
                                printerIp = noPrintModel.printerConfigInfo?.ipAddress ?: "",
                                taskInfo = noPrintModel.getPrintContentAsJson(),
                                status = PrinterLogUtils.PrintStatus.PRINT_SEND,
                                message = "正在发送  copies:${i + 1}/${noPrintModel.printTemplateResponseItem?.copies}"
                            )
                        }
                        printBitmap(it, POSConst.ALIGNMENT_LEFT, it.width).cutHalfAndFeed(1)
                    }
                }
                Handler(Looper.getMainLooper()).postDelayed({
                    if (!originalBitmap.isRecycled) {
                        originalBitmap.recycle()
                    }
                }, 7000)
                return true
            }
        }
        return false
    }

    /**
     * 打印闭店报表
     *
     * @param context
     * @param shiftReport
     */
    fun printPrinterClosingReport(
        context: Context,
        shiftReport: ShiftReportPrint
    ) {
        val printerList = PrinterDeviceHelper.getPrinterList()
        printerList.forEach {
            if (it.isPrintShiftHandoverReport()) {
                val printTamplateResponseItem = it.getPrintShiftHandoverReportTmp()
                if (it.type == PrinterTypeEnum.USB.type) {
//                    if (!isXPrinter() && sunmiIsConnect()) {
//                        selectPrinter?.lineApi()?.apply {
//                            ReportPrinter.initClosingReportTickerBitmap(
//                                context, it, shiftReport, printTamplateResponseItem
//                            ).let {
//                                if (it != null) {
//                                    printBitmap(it, BitmapStyle.getStyle())
//                                    autoOut()
//                                }
//                            }
//                        }
//                    } else {
//                        val printer = PrinterUsbDeviceHelper.getPosPrinter(usbDevice = it.usbDevice)
//                        printer.addListener(object : ListenableFuture.Listener<POSPrinter?> {
//                            override fun onSuccess(result: POSPrinter?) {
//                                result?.apply {
//                                    initializePrinter()
//                                    val bitmap =
//                                        ReportPrinter.initClosingReportTickerBitmap(
//                                            context, it, shiftReport, printTamplateResponseItem
//                                        )
//                                    if (bitmap != null) {
//                                        printBitmap(bitmap, POSConst.ALIGNMENT_LEFT, bitmap.width)
//                                        cutHalfAndFeed(1)
//                                    }
//                                }
//                            }
//
//                            override fun onFailure(e: ExecutionException) {
//                            }
//                        })
//                    }
                    NewUsbPrintQueueManager.addTask(
                        NewUsbPrintTask(
                            noPrintModel = NoPrintModel(
                                printerConfigInfo = it,
                                shiftReport = shiftReport,
                                printTemplateResponseItem = printTamplateResponseItem,
                                timeStamp = Date().time,
                            )
                        )
                    )

                } else if (it.type == PrinterTypeEnum.WIFI.type) {
//                    val connection = PrinterDeviceHelper.getWifiConnection(it?.ipAddress)
//                    connection?.isConnect(byteArrayOf(0)) { status -> //1: connected 0: disconnect
//                        Timber.e("wifi打印 报表 status $status")
//                        val isConnect = status == 1
//                        if (isConnect) {
//                            POSPrinter(connection).apply {
//                                initializePrinter()
//                                val bitmap =
//                                    ReportPrinter.initClosingReportTickerBitmap(
//                                        context, it, shiftReport, printTamplateResponseItem
//                                    )
//                                if (bitmap != null) {
//                                    printBitmap(bitmap, POSConst.ALIGNMENT_LEFT, bitmap.width)
//                                    cutHalfAndFeed(1)
//                                }
//                            }
//                        }
//                    }
//
                    WifiPrintQueueManager.addTask(
                        PrintTask(
                            noPrintModel = NoPrintModel(
                                printerConfigInfo = it,
                                shiftReport = shiftReport,
                                printTemplateResponseItem = printTamplateResponseItem,
                                timeStamp = Date().time,
                            )
                        )
                    )
                }
            }
        }
    }


    /**
     * 打印商品报表
     *
     * @param context
     * @param productReport
     */
    fun printPrinterProductReport(
        context: Context,
        productReport: ProductReportResponse?
    ) {
        val printerList = PrinterDeviceHelper.getPrinterList()
        printerList.forEach {
            if (it.isPrintProductReport()) {
                if (it.type == PrinterTypeEnum.USB.type) {
//                    if (!isXPrinter() && sunmiIsConnect()) {
//                        selectPrinter?.lineApi()?.apply {
//                            ReportPrinter.initProductReportTickerBitmap(
//                                context, it, productReport
//                            ).let {
//                                if (it != null) {
//                                    printBitmap(it, BitmapStyle.getStyle())
//                                    autoOut()
//                                }
//                            }
//                        }
//                    } else {
//                        val printer = PrinterUsbDeviceHelper.getPosPrinter(usbDevice = it.usbDevice)
//                        printer.addListener(object : ListenableFuture.Listener<POSPrinter?> {
//                            override fun onSuccess(result: POSPrinter?) {
//                                result?.apply {
//                                    initializePrinter()
//                                    val bitmap =
//                                        ReportPrinter.initProductReportTickerBitmap(
//                                            context, it, productReport
//                                        )
//                                    if (bitmap != null) {
//                                        printBitmap(bitmap, POSConst.ALIGNMENT_LEFT, bitmap.width)
//                                        cutHalfAndFeed(1)
//                                    }
//                                }
//                            }
//
//                            override fun onFailure(e: ExecutionException) {
//                            }
//                        })
//                    }
                    NewUsbPrintQueueManager.addTask(
                        NewUsbPrintTask(
                            noPrintModel = NoPrintModel(
                                printerConfigInfo = it,
                                productReport = productReport,
                                timeStamp = Date().time,
                            )
                        )
                    )
                } else if (it.type == PrinterTypeEnum.WIFI.type) {
                    Timber.e("Chetwyn 先走的wifi 834")
//                    val connection = PrinterDeviceHelper.getWifiConnection(it?.ipAddress)
//                    connection?.isConnect(byteArrayOf(0)) { status -> //1: connected 0: disconnect
//                        Timber.e("wifi打印 报表 status $status")
//                        val isConnect = status == 1
//                        if (isConnect) {
//                            POSPrinter(connection).apply {
//                                initializePrinter()
//                                val bitmap =
//                                    ReportPrinter.initProductReportTickerBitmap(
//                                        context, it, productReport
//                                    )
//                                if (bitmap != null) {
//                                    printBitmap(bitmap, POSConst.ALIGNMENT_LEFT, bitmap.width)
//                                    cutHalfAndFeed(1)
//                                }
//                            }
//                        }
//                    }
                    WifiPrintQueueManager.addTask(
                        PrintTask(
                            noPrintModel = NoPrintModel(
                                printerConfigInfo = it,
                                productReport = productReport,
                                timeStamp = Date().time,
                            )
                        )
                    )
                }
            }
        }
    }


    /**
     * 打印支付渠道报表
     *
     * @param context
     * @param productReport
     */
    fun printPrinterPaymentMethodReport(
        context: Context,
        productReport: PaymentMethodReportResponse?
    ) {
        val printerList = PrinterDeviceHelper.getPrinterList()
        printerList.forEach {
            if (it.isPrintPaymentMethodReport()) {
                if (it.type == PrinterTypeEnum.USB.type) {
//                    if (!isXPrinter() && sunmiIsConnect()) {
//                        selectPrinter?.lineApi()?.apply {
//                            ReportPrinter.initPaymentTickerBitmap(
//                                context, it, productReport
//                            ).let {
//                                if (it != null) {
//                                    printBitmap(it, BitmapStyle.getStyle())
//                                    autoOut()
//                                }
//                            }
//                        }
//                    } else {
//                        val printer = PrinterUsbDeviceHelper.getPosPrinter(usbDevice = it.usbDevice)
//                        printer.addListener(object : ListenableFuture.Listener<POSPrinter?> {
//                            override fun onSuccess(result: POSPrinter?) {
//                                result?.apply {
//                                    initializePrinter()
//                                    val bitmap =
//                                        ReportPrinter.initPaymentTickerBitmap(
//                                            context, it, productReport
//                                        )
//                                    if (bitmap != null) {
//                                        printBitmap(bitmap, POSConst.ALIGNMENT_LEFT, bitmap.width)
//                                        cutHalfAndFeed(1)
//                                    }
//                                }
//                            }
//
//                            override fun onFailure(e: ExecutionException) {
//                            }
//                        })
//                    }
                    NewUsbPrintQueueManager.addTask(
                        NewUsbPrintTask(
                            noPrintModel = NoPrintModel(
                                printerConfigInfo = it,
                                paymentMethodReport = productReport,
                                timeStamp = Date().time,
                            )
                        )
                    )
                } else if (it.type == PrinterTypeEnum.WIFI.type) {
                    Timber.e("Chetwyn 先走的wifi 918")
                    WifiPrintQueueManager.addTask(
                        PrintTask(
                            noPrintModel = NoPrintModel(
                                printerConfigInfo = it,
                                paymentMethodReport = productReport,
                                timeStamp = Date().time,
                            )
                        )
                    )
//                    val connection = PrinterDeviceHelper.getWifiConnection(it?.ipAddress)
//                    connection?.isConnect(byteArrayOf(0)) { status -> //1: connected 0: disconnect
//                        Timber.e("wifi打印 报表 status $status")
//                        val isConnect = status == 1
//                        if (isConnect) {
//                            POSPrinter(connection).apply {
//                                initializePrinter()
//                                val bitmap =
//                                    ReportPrinter.initPaymentTickerBitmap(
//                                        context, it, productReport
//                                    )
//                                if (bitmap != null) {
//                                    printBitmap(bitmap, POSConst.ALIGNMENT_LEFT, bitmap.width)
//                                    cutHalfAndFeed(1)
//                                }
//                            }
//                        }
//                    }
                }
            }
        }
    }


    fun printPrinterCreditRecordReport(
        context: Context,
        printerOrderStatus: OrderedStatusEnum,
        repaymentResponse: RepaymentResponse
    ) {
        val outTradeNo = repaymentResponse.khqrCode?.outTradeNo
        if (printerOrderStatus == OrderedStatusEnum.CREDIT_PAID) {
            /*
            挂账已支付是ws和还款后自动打印触发的，这里需要判断是否重复打印
            未支付只有手动触发打印,不用判断
             */
            if (PrinterDeviceHelper.isCreditRecordPrinter(outTradeNo)) {
                return
            }
            PrinterDeviceHelper.setCreditRecordPrinter(outTradeNo)
        }

        val printerList = PrinterDeviceHelper.getPrinterList()
        printerList.forEach {
//            if (it.isPrintPaymentMethodReport()) {
            if (it.type == PrinterTypeEnum.USB.type) {
//                if (!isXPrinter() && sunmiIsConnect()) {
//                    selectPrinter?.lineApi()?.apply {
//                        ReportPrinter.initCreditRecordBitmap(
//                            context, it, printerOrderStatus, repaymentResponse
//                        ).let {
//                            if (it != null) {
//                                printBitmap(it, BitmapStyle.getStyle())
//                                autoOut()
//                            }
//                        }
//                    }
//                } else {
//                    val printer = PrinterUsbDeviceHelper.getPosPrinter(usbDevice = it.usbDevice)
//                    printer.addListener(object : ListenableFuture.Listener<POSPrinter?> {
//                        override fun onSuccess(result: POSPrinter?) {
//                            result?.apply {
//                                initializePrinter()
//                                val bitmap =
//                                    ReportPrinter.initCreditRecordBitmap(
//                                        context, it, printerOrderStatus, repaymentResponse
//                                    )
//                                if (bitmap != null) {
//                                    printBitmap(bitmap, POSConst.ALIGNMENT_LEFT, bitmap.width)
//                                    cutHalfAndFeed(1)
//                                }
//                            }
//                        }
//
//                        override fun onFailure(e: ExecutionException) {
//                        }
//                    })
//                }
                NewUsbPrintQueueManager.addTask(
                    NewUsbPrintTask(
                        noPrintModel = NoPrintModel(
                            printerConfigInfo = it,
                            repaymentResponse = repaymentResponse,
                            printerOrderStatus = printerOrderStatus,
                            timeStamp = Date().time,
                        )
                    )
                )
            } else if (it.type == PrinterTypeEnum.WIFI.type) {
                //TODO
                WifiPrintQueueManager.addTask(
                    PrintTask(
                        noPrintModel = NoPrintModel(
                            printerConfigInfo = it,
                            repaymentResponse = repaymentResponse,
                            printerOrderStatus = printerOrderStatus,
                            timeStamp = Date().time,
                        )
                    )
                )
            }
        }
    }

    /**
     * 打印临时码
     *
     * @param context
     * @param createTempTableResponse
     */
    fun printPrinterTempTable(
        context: Context,
        createTempTableResponse: CreateTempTableCodeResponse
    ) {
        val printerList = PrinterDeviceHelper.getPrinterList()
        printerList.forEach {
            if (it.type == PrinterTypeEnum.USB.type) {
//                if (!isXPrinter() && sunmiIsConnect()) {
//                    selectPrinter?.lineApi()?.apply {
//
//                        initPrinterTempTableBitmap(
//                            context, createTempTableResponse, it
//                        ).let {
//                            if (it != null) {
//                                printBitmap(it, BitmapStyle.getStyle())
//                                autoOut()
//                            }
//                        }
//                    }
//                } else {
//                    val printer = PrinterUsbDeviceHelper.getPosPrinter(usbDevice = it.usbDevice)
//                    printer.addListener(object : ListenableFuture.Listener<POSPrinter?> {
//                        override fun onSuccess(result: POSPrinter?) {
//                            result?.apply {
//                                initializePrinter()
//                                val bitmap =
//                                    initPrinterTempTableBitmap(context, createTempTableResponse, it)
//                                if (bitmap != null) {
//                                    printBitmap(bitmap, POSConst.ALIGNMENT_LEFT, bitmap.width)
//                                    cutHalfAndFeed(1)
//                                }
//                            }
//                        }
//
//                        override fun onFailure(e: ExecutionException) {
//                        }
//                    })
//                }
                NewUsbPrintQueueManager.addTask(
                    NewUsbPrintTask(
                        noPrintModel = NoPrintModel(
                            createTempTableResponse = createTempTableResponse,
                            printerConfigInfo = it,
                            timeStamp = Date().time,
                        )
                    )
                )

            } else if (it.type == PrinterTypeEnum.WIFI.type) {
                Timber.e("Chetwyn 先走的wifi 1000")
                WifiPrintQueueManager.addTask(
                    PrintTask(
                        noPrintModel = NoPrintModel(
                            createTempTableResponse = createTempTableResponse,
                            printerConfigInfo = it,
                            timeStamp = Date().time,
                        )
                    )
                )
            }
        }
    }


    val dateFormat = SimpleDateFormat("yyyy/MM/dd HH:mm:ss", Locale.US)

//    private fun getStoreName(): List<String?> {
//        //店铺名打印规则
//        //三选二
//        //优先级1：英文/柬文
//        //优先级2：没设置柬文，就打印英文/原始的店名
//        //原始店名绝对有值
//        //如果没有配置英文或柬文，就打印原始店名
//        //原始店名|跟随系统
//        //不打印中文店名
//        val originalStore = MainDashboardFragment.CURRENT_USER?.storeName
//        //英文店名
//        val storeNameEn = MainDashboardFragment.CURRENT_USER?.storeNameEN
//        //柬文店名
//        val storeNameKh = MainDashboardFragment.CURRENT_USER?.storeNameKH
//        var firstStoreName: String? = null
//        var secondStoreName: String? = null
//        if (!storeNameEn.isNullOrEmpty() && !storeNameKh.isNullOrEmpty()) {
//            //优先级1：英文/柬文
//            firstStoreName = storeNameEn
//            secondStoreName = storeNameKh
//        } else if (storeNameKh.isNullOrEmpty() && !storeNameEn.isNullOrEmpty()) {
//            //优先级2：没设置柬文，就打印英文/原始的店名
//            firstStoreName = originalStore
//            secondStoreName = storeNameEn
//        } else if (!storeNameKh.isNullOrEmpty() && storeNameEn.isNullOrEmpty()) {
//            //优先级3：没设置英文，就打印柬文/原始的店名
//            firstStoreName = originalStore
//            secondStoreName = storeNameKh
//        } else {
//            //优先级4：没有设置柬文和英文，就打印原始店名
//            firstStoreName = originalStore
//        }
//        return arrayListOf(firstStoreName, secondStoreName)
//    }


    fun initPrinterTempTableBitmap(
        context: Context,
        createTempTableResponse: CreateTempTableCodeResponse,
        printerConfigInfo: PrinterConfigInfo?,
    ): Bitmap? {
        val binding = TicketPrinterTemporaryTableBinding.inflate(LayoutInflater.from(context))
        val createBitmap: Bitmap
        binding.apply {
            var width = getPrinterWidth()
            printerConfigInfo?.apply {
                if (printerConfigInfo.printTempTableCode != true) {
                    return null
                }
                val tmpLangList =
                    (createTempTableResponse?.tempTableCodeLang ?: "en").split(",").map {
                        Locale(it)
                    }

                tmpLangList.forEachIndexed { index, locale ->

                    (llStoreName[index] as TextView).isVisible = true
                    (llStoreName[index] as TextView).text =
                        MainDashboardFragment.CURRENT_USER?.getStoreNameByLan(locale)

                    tableNameTitle[index].isVisible = true
                    (tableNameTitle[index] as TextView).text =
                        context.getStringByLocale(R.string.print_title_table_name, locale)
                    expiryTimeTitle[index].isVisible = true
                    (expiryTimeTitle[index] as TextView).text =
                        context.getStringByLocale(R.string.expiry_time, locale)
                    descTopTitle[index].isVisible = true
                    (descTopTitle[index] as TextView).text =
                        context.getStringByLocale(R.string.please_scan_to_order_food, locale)
                    descBottomTitle[index].isVisible = true
                    (descBottomTitle[index] as TextView).text =
                        context.getStringByLocale(
                            R.string.bring_this_receipt_with_you_when_checking_out,
                            locale
                        )
                }


                if (isEightyWidth()) {
                    width = getPrinterEightyWidth()
                }

                tvStoreNameKH.isVisible =
                    tvStoreNameKH.text.isNotEmpty() && tvStoreName.text != tvStoreNameKH.text

                tvTableId.text = createTempTableResponse.name
                tvTableExpirationTime.text = createTempTableResponse.expireTime

                tvThankYouWords.isGone = createTempTableResponse.welcomeSlogans.isNullOrEmpty()
                tvThankYouWords.text = createTempTableResponse.welcomeSlogans
                ivOrderQr.isGone = createTempTableResponse.qrCode.isNullOrEmpty()
                ivOrderQr.setImageDrawable(
                    QrCodeDrawable(
                        QrData.Url(createTempTableResponse.qrCode ?: ""),
                        charset = Charset.forName("UTF-8")
                    )
                )
                val isEightyWidth = printerConfigInfo?.isEightyWidth()
                if (isEightyWidth == true) {
                    vFooter.thankYou.text =
                        context.getString(R.string.print_footer_thank_you_eigthy)
                } else {
                    vFooter.thankYou.text = context.getString(R.string.print_footer_thank_you)
                    vFooter.root.setPadding(0, 0, 0, 80)
                }
            }

            //380 58mm
            root.measure(
                View.MeasureSpec.makeMeasureSpec(
                    width,
                    View.MeasureSpec.EXACTLY
                ), View.MeasureSpec.makeMeasureSpec(
                    0,
                    View.MeasureSpec.UNSPECIFIED
                )
            )
            root.layout(0, 0, root.measuredWidth, root.measuredHeight)
            //方法一
//            root.setBackgroundColor(Color.WHITE)
//            root.buildDrawingCache()
            //方法二
            createBitmap =
                Bitmap.createBitmap(
                    root.measuredWidth,
                    root.measuredHeight,
                    Bitmap.Config.RGB_565
                )
            val canvas = Canvas(createBitmap).apply {
                drawColor(Color.WHITE)
            }
            root.draw(canvas)
        }
        //方法一
//        return binding.root.drawingCache
        //方法二
        return createBitmap
    }

    /**
     * 打印机错误信息类
     */
    private data class PrinterErrorInfo(
        val message: String,
        val canRetry: Boolean = true,
        val retryDelayMs: Long = 5000
    )

    /**
     * 获取打印机错误信息
     */
//    private fun getPrinterErrorInfo(status: Int, context: Context): PrinterErrorInfo {
//        return when (status) {
//            POSConst.STS_COVEROPEN -> {
//                // 打印机开盖 - 可重试
//                PrinterErrorInfo(
//                    message = context.getString(R.string.printer_front_cover_open),
//                    canRetry = false,
//                    retryDelayMs = 8000 // 给用户更多时间关闭打印机盖
//                )
//            }
//
//            POSConst.STS_PAPEREMPTY -> {
//                // 缺纸 - 可重试
//                PrinterErrorInfo(
//                    message = context.getString(R.string.printer_out_of_paper),
//                    canRetry = false,
//                    retryDelayMs = 10000 // 给用户更多时间添加纸张
//                )
//            }
//
//            POSConst.STS_PRESS_FEED -> {
//                // 按下进纸按钮 - 可重试
//                PrinterErrorInfo(
//                    message = context.getString(R.string.feed_button_pressed),
//                    canRetry = false,
//                    retryDelayMs = 3000
//                )
//            }
//
//            POSConst.STS_PRINTER_ERR -> {
//                // 打印机错误 - 可重试
//                PrinterErrorInfo(
//                    message = context.getString(R.string.printer_error),
//                    canRetry = true,
//                    retryDelayMs = 5000
//                )
//            }
//
//            else -> {
//                // 未知错误 - 可重试
//                PrinterErrorInfo(
//                    message = context.getString(R.string.printer_unknow_error_restart),
//                    canRetry = true,
//                    retryDelayMs = 5000
//                )
//            }
//        }
//    }

    /**
     * 处理USB打印机状态检查和打印执行
     */
//    private fun handleUsbPrinterStatus(
//        posPrinter: POSPrinter,
//        deviceName: String,
//        printTemplateResponseItem: PrintTamplateResponseItem?,
//        context: Context,
//        currentOrderedInfo: OrderedInfoResponse?,
//        isPrinterAgain: Boolean,
//        printerConfigInfo: PrinterConfigInfo?,
//        isOrderMore: Boolean,
//        paymentQrCode: String?,
//        retryCount: Int = 0
//    ) {
//        posPrinter.printerStatus { status ->
//            Timber.e("USB打印机状态: $status, 重试次数: $retryCount")
//            if (status == POSConst.STS_NORMAL) {
//
//                posPrinter.initializePrinter()
//                finalPrintHandler(
//                    printTemplateResponseItem,
//                    context,
//                    currentOrderedInfo,
//                    isPrinterAgain,
//                    printerConfigInfo,
//                    isOrderMore,
//                    paymentQrCode
//                ) {
////                    printByPrinter(
////                        posPrinter,
////                        it,
////                        printTemplateResponseItem?.copies
////                    )
//                    try {
//                        PrinterLogUtils.writeUsbPrintLog(
//                            printerIp = deviceName,
//                            taskInfo = if (currentOrderedInfo != null) mapOf(
//                                "order" to currentOrderedInfo.toJson(),
//                                "printTemplateResponseItem" to (printTemplateResponseItem
//                                    ?: "").toJson()
//                            ).toJson() else "",
//                            status = PrinterLogUtils.PrintStatus.PRINT_SUCCESS,
//                            message = "发送打印成功  ${status}"
//                        )
//                    } catch (e: Exception) {
//                        e.printStackTrace()
//                    }
//                }
//            } else {
//                // 获取错误信息
//                val errorInfo = getPrinterErrorInfo(status, context)
//
//                // 根据错误类型决定是否重试
//                if (errorInfo.canRetry) {
//                    // 创建重试任务
//                    scheduleRetryPrintTask(
//                        deviceName = deviceName,
//                        printTemplateResponseItem = printTemplateResponseItem,
//                        context = context,
//                        currentOrderedInfo = currentOrderedInfo,
//                        isPrinterAgain = isPrinterAgain,
//                        printerConfigInfo = printerConfigInfo,
//                        isOrderMore = isOrderMore,
//                        paymentQrCode = paymentQrCode,
//                        errorStatus = status,
//                        retryDelay = errorInfo.retryDelayMs,
//                        retryCount = retryCount  // 传递当前重试次数
//                    )
//                } else {
//                    Timber.e("打印机错误不可重试: $status - ${errorInfo.message}")
//                }
//            }
//            Timber.e("USB打印状态检查完成")
//        }
//    }

    /**
     * 执行打印任务，处理不同类型的打印机
     */
//    private fun executePrintTask(
//        deviceName: String,
//        printTemplateResponseItem: PrintTamplateResponseItem?,
//        context: Context,
//        currentOrderedInfo: OrderedInfoResponse?,
//        isPrinterAgain: Boolean,
//        printerConfigInfo: PrinterConfigInfo?,
//        isOrderMore: Boolean,
//        paymentQrCode: String?,
//        retryCount: Int = 0
//    ) {
//        if (!isXPrinter() && sunmiIsConnect()) {
//            // 商米打印机
//            selectPrinter?.lineApi()?.apply {
//                finalPrintHandler(
//                    printTemplateResponseItem,
//                    context,
//                    currentOrderedInfo,
//                    isPrinterAgain,
//                    printerConfigInfo,
//                    isOrderMore,
//                    paymentQrCode
//                ) {
//                    printBySummi(this, it, printTemplateResponseItem?.copies)
//                }
//            }
////            USBPrintQueueManager.onPrintTaskCompleted()
//        } else {
////            // USB打印机
////            if (printerConfigInfo?.usbDevice == null) {
////                //USB离线断开
////                MyApplication.myAppInstance.showToast(context.getString(R.string.usb_printer_not_connected))
////                USBPrintQueueManager.onPrintTaskCompleted()
////                return
////            }
//            val printer = PrinterUsbDeviceHelper.getPosPrinter(printerConfigInfo?.usbDevice)
//            printer.addListener(object : ListenableFuture.Listener<POSPrinter?> {
//                override fun onSuccess(result: POSPrinter?) {
//                    result?.apply {
//                        handleUsbPrinterStatus(
//                            this,
//                            deviceName,
//                            printTemplateResponseItem,
//                            context,
//                            currentOrderedInfo,
//                            isPrinterAgain,
//                            printerConfigInfo,
//                            isOrderMore,
//                            paymentQrCode,
//                            retryCount
//                        )
//                    }
//                }
//
//                override fun onFailure(e: ExecutionException) {
//                    Timber.e(e, "USB打印机连接失败")
//                    USBPrintQueueManager.onPrintTaskCompleted()
//                }
//            })
//        }
//    }

    //    /**
//     * 安排打印任务重试
//     */
//    private fun scheduleRetryPrintTask(
//        deviceName: String,
//        printTemplateResponseItem: PrintTamplateResponseItem?,
//        context: Context,
//        currentOrderedInfo: OrderedInfoResponse?,
//        isPrinterAgain: Boolean,
//        printerConfigInfo: PrinterConfigInfo?,
//        isOrderMore: Boolean,
//        paymentQrCode: String?,
//        errorStatus: Int,
//        retryDelay: Long = 5000,
//        retryCount: Int = 0
//    ) {
//        // 检查最大重试次数
//        val MAX_RETRY_COUNT = USBPrintTask.MAX_RETRY
//        if (retryCount >= MAX_RETRY_COUNT) {
//            Timber.e("达到最大重试次数($MAX_RETRY_COUNT)，放弃打印任务: 设备=$deviceName, 错误=$errorStatus")
//            val errorInfo = getPrinterErrorInfo(errorStatus, context)
//            //重试达到上限的时候给出打印报错提示
//            MyApplication.myAppInstance.showToast("${errorStatus}: ${errorInfo.message}")
////            MyApplication.myAppInstance.showToast(context.getString(R.string.printer_max_retry_reached))
//            return
//        }
//
//        val nextRetryCount = retryCount + 1
//        MyApplication.myAppInstance.showToast(
//            context.getString(R.string.printer_retrying_message, nextRetryCount, MAX_RETRY_COUNT)
//        )
//
//        // 创建重试任务
//        val retryTask = USBPrintTask(
//            deviceName = deviceName,
//            task = {
//                Timber.d("执行打印重试任务，之前错误状态: $errorStatus, 重试次数: ${retryCount + 1}/$MAX_RETRY_COUNT")
//                executePrintTask(
//                    deviceName,
//                    printTemplateResponseItem,
//                    context,
//                    currentOrderedInfo,
//                    isPrinterAgain,
//                    printerConfigInfo,
//                    isOrderMore,
//                    paymentQrCode,
//                    nextRetryCount  // 传递增加后的重试次数
//                )
//            },
//            priority = if (printTemplateResponseItem?.type == PrintTemplateTypeEnum.KITCHEN.id) {
//                USBPrintTask.HIGH_PRIORITY - 1 // 提高优先级，比原来的厨房打印还要高
//            } else {
//                USBPrintTask.DEFAULT_PRIORITY - 1 // 提高优先级
//            },
//            retryCount = nextRetryCount  // 设置重试计数
//        )
//
//        // 使用Handler延迟执行
//        Handler(Looper.getMainLooper()).postDelayed({
//            Timber.i("添加打印重试任务到队列，延迟: ${retryDelay}ms, 错误状态: $errorStatus, 重试次数: ${retryCount + 1}/$MAX_RETRY_COUNT")
//            USBPrintQueueManager.addPrintTask(retryTask)
//        }, retryDelay)
//    }
}
