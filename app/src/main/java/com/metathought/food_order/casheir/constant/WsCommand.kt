package com.metathought.food_order.casheir.constant


/**
 *<AUTHOR>
 *@time  2024/9/24
 *@desc
 **/

object WsCommand {
    /**
     * 购物车变动
     */
    const val CART_CHANGE = 2

    /**
     * 预订单取消
     */
    const val RESERVE_ORDER_CANCEL = 8


    /**
     * 现金支付成功
     */
    const val OFFLINE_PAY_SUCCEED = 9

    /**
     * 扫码支付成功
     */
    const val QRCODE_PAY_SUCCEED = 10


    /**
     * 生成已确认订单 或 已确认订单添加菜品
     */
    const val CONFIRMED_GOODS_INFO = 14

    /**
     * 普通订单取消
     */
    const val CONVENTION_ORDER_CANCEL = 15

    /**
     * 员工翻台
     */
    const val EMPLOYEE_EMPTY_TABLE = 16

    /**
     * 员工切换设备
     */
    const val EMPLOYEE_SWITCHOVER_DEVICE = 17

    /**
     * 菜品下架或删除
     */
    const val GOODS_SOLD_OUT_OR_DEL = 18

    /**
     * 已确认到待支付订单状态变更
     */
    const val CONFIRMED_TO_UNPAID_STATUS_ALTER = 19

    /**
     * 用户余额支付成功
     */
    const val BALANCE_PAY_SUCCEED = 20

    /**
     * 商户修改运营模式
     */
    const val UPDATE_OPERATION_MODEL = 21

    /**
     * 员工修改商品重量
     */
    const val GOODS_CHANGE_WEIGHT = 22

    /**
     * 用户端取消订单
     */
    const val USER_CANCEL_ORDER = 23

    /**
     * 订单状态变为已确认
     */
    const val ORDER_STATUS_BE_CONFIRM = 24


    /**
     * 减免折扣修改
     */
    const val WHOLE_DISCOUNT_MODIFY = 28


    /**
     * 修改打印配置
     */
    const val MODIFY_PRINTER_CONFIG = 29

    /**
     * 其他端下单
     */
    const val CREATE_ORDER = 30

    /**
     * 下单并 订单付款
     */
    const val PAY_ORDER = 31

    /**
     * 用户加购
     */
    const val USER_ADD_GOODS = 32

    /**
     * 修改重量（需要确认一下）
     */
    const val MODIFY_WEIGHT = 33

    /**
     * 播放新订单语音
     */
    const val PLAY_ORDER_SOUND = 34

    /**
     * 订单已读
     */
    const val ORDER_HAS_READ = 35

    /**
     * 打印预结小票
     */
    const val PRINT_PRE_SETTLEMENT_TICKET = 36

    /**
     * 员工端手动打印小票
     */
    const val STAFF_PRINT_TICKET = 37

    /**
     * 新的待接单订单 下发的消息内容和详情一致
     */
    const val NEW_ACCEPT_ORDER = 38

    /**
     * 订单接单 下发接单id
     */
    const val ACCEPT_ORDER = 39

    /**
     * 取消接单 下发接单id
     */
    const val CANCEL_ACCEPT_ORDER = 40

    /**
     * 待接单 有更新
     */
    const val ACCEPT_ORDER_UPDATE = 41

    /**
     * 反结账 事件
     */
    const val ANTI_SETTLEMENT = 44

    /**
     * 公告变动
     */
    const val NOTICE_CHANGE = 45

    /**
     * 合并订单
     */
    const val MERGE_ORDER = 47

    /**
     * 拆分订单
     */
    const val SPLIT_ORDER = 48

    /**
     * 同步单品折扣
     */
    const val GOODS_REDUCE_DISCOUNT_CHANGE = 49

    /**
     *  优惠活动变动
     */
    const val ACTIVITY_COUPON_CHANGE = 50


    /**
     * 开班下发事件
     */
    const val SHIFT_HANDOVER_START = 52

    /**
     * 订单已打印预结单状态变更
     */
    const val PRINT_PRE_SETTLEMENT_STATUS_CHANGE = 53

    /**
     * 交班退出
     */
    const val SHIFT_HANDOVER_EXIT = 54

    /**
     * 店铺配置变动
     */
    const val STORE_CONFIG_CHANGE = 55

    /**
     * 顾客信息变动
     */
    const val CUSTOM_INFO_CHANGE = 56

    /**
     * 外卖平台变动
     */
    const val TAKE_OUT_PLATFORM_CHANGE = 57

    /**
     * 修改订单备注/修改订单商品备注
     */
    const val CHANGE_ORDER_REMARK = 58

    /**
     * 取消挂账
     */
    const val CANCEL_CREDIT = 59

    /**
     *  购物车重量/价格变更   目前没购物车重量变动
     */
    const val CART_CHANGE_WEIGHT = 60

    /**
     * 挂账
     */
    const val ORDER_CREDIT = 61

    /**
     * 挂账还款
     */
    const val REPAYMENT = 62


    /**
     * 上传日志
     */
    const val UPLOAD_PRINTER_LOG = 63

}