package com.metathought.food_order.casheir.ui.dialog.tmp_good

import android.content.Context
import android.widget.Toast
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.request_model.menu.CreateTmpGoodRequest
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.GOOD_MAX_NUM
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import timber.log.Timber
import java.math.BigDecimal
import javax.inject.Inject

@HiltViewModel
class TmpGoodViewModel @Inject
constructor(val repository: Repository) : ViewModel() {

    private val _uiListState = MutableLiveData<UIListModel>()
    val uiListState get() = _uiListState

    private val _uiCreateGoodModel = MutableLiveData<UICreateGoodModel>()
    val uiCreateGoodModel get() = _uiCreateGoodModel

    private val _uiDeleteGoodModel = MutableLiveData<UIDeleteGoodModel>()
    val uiDeleteGoodModel get() = _uiDeleteGoodModel

    private val _uiCartModel = MutableLiveData<UICartModel>()
    val uiCartModel get() = _uiCartModel

    val goodsList = mutableListOf<Goods>()

    var isRequest = false

    /**
     * 获取临时菜列表
     *
     */
    fun getTmpGoodList() {
        viewModelScope.launch {
            try {
                val response = repository.getGoodsTemporaryList()
                uiListState.postValue(UIListModel(response))
            } catch (e: Exception) {
                uiListState.postValue(UIListModel(ApiResponse.Error("")))
            }
        }
    }

    /**
     * 创建临时菜/修改临时菜
     *
     */
    fun createTmpGood(
        id: Long? = null,
        name: String? = null,
        sellPrice: BigDecimal? = null,
        note: String? = null
    ) {
        val request = CreateTmpGoodRequest().apply {
            this.id = id
            this.name = name
            this.sellPrice = sellPrice
            this.note = note
        }
        viewModelScope.launch {
            try {
                isRequest = true
//                uiCreateGoodModel.postValue(UICreateGoodModel())
                val response = repository.createGoodsTemporary(request)
                isRequest = false
                uiCreateGoodModel.postValue(UICreateGoodModel(response))
            } catch (e: Exception) {
                isRequest = false
                uiCreateGoodModel.postValue(UICreateGoodModel(ApiResponse.Error("")))
            }
        }
    }

    /**
     * 删除菜品
     *
     * @param id
     */
    fun deleteTmpGood(
        id: Long? = null,
    ) {
        if (id == null) {
            return
        }
        viewModelScope.launch {
            try {
                isRequest = true
                uiDeleteGoodModel.postValue(UIDeleteGoodModel())
                val response = repository.deleteGoodsTemporary(id)
                isRequest = false
                uiDeleteGoodModel.postValue(UIDeleteGoodModel(response))
            } catch (e: Exception) {
                isRequest = false
                uiDeleteGoodModel.postValue(UIDeleteGoodModel(ApiResponse.Error("")))
            }
        }
    }


    fun plus(
        context: Context,
        goods: Goods,
        cartCount: Int = 1,
    ) {
        viewModelScope.launch {
            val index = goodsList.indexOfFirst { it.id == goods.id }
            Timber.e("index : $index")
            if (index != -1) {
                val maxNum = GOOD_MAX_NUM
                val canAddNum = maxNum - (goodsList[index].totalCount ?: 0)
                if (canAddNum <= 0) {
                    Toast.makeText(
                        context,
                        context.getString(
                            R.string.you_have_reached_the_maximum_quantity_limit_of,
                            maxNum
                        ), Toast.LENGTH_SHORT
                    ).show()
                    return@launch
                }
                goodsList[index].totalCount = (goodsList[index].totalCount ?: 0) + cartCount
            } else {
                goodsList.add(goods.copy(totalCount = 1))
            }
            uiCartModel.postValue(UICartModel(goodsList = goodsList))
        }
    }

    fun sub(
        goods: Goods,
        cartCount: Int = 1,
    ) {
        viewModelScope.launch {
            val index = goodsList.indexOfFirst { it.id == goods.id }
            Timber.e("index : $index")
            if (index != -1) {
                goodsList[index].totalCount = (goodsList[index].totalCount ?: 0) - cartCount
                if ((goodsList[index].totalCount ?: 0) <= 0) {
                    goodsList.removeAt(index)
                }
            }

            uiCartModel.postValue(UICartModel(goodsList = goodsList))
        }
    }

    fun updateGoodsNum(good: Goods, numStr: String) {
        viewModelScope.launch {
            val num = numStr.toInt()
            val index = goodsList.indexOfFirst { it.id == good.id }
            if (index != -1) {
                goodsList[index].totalCount = num
                if ((goodsList[index].totalCount ?: 0) <= 0) {
                    goodsList.removeAt(index)
                }
                uiCartModel.postValue(UICartModel(goodsList = goodsList))
            }
        }
    }


    fun removeAllGood() {
        viewModelScope.launch {
            goodsList.clear()
            uiCartModel.postValue(UICartModel(goodsList = goodsList))
        }
    }

    /**
     * 更新临时购物车 对应菜品信息
     *
     * @param list
     */
    fun updateCartGoodInfo(list: List<Goods>) {
        var removeList = mutableListOf<String>()
        goodsList.forEach {
            val index = list.indexOfFirst { good -> it.id == good.id }
            if (index != -1) {
                it.sellPrice = list[index].sellPrice
                it.name = list[index].name
            } else {
                removeList.add(it.id)
            }
        }

        removeList.forEach {
            goodsList.removeIf { good -> good.id == it }
        }

        uiCartModel.postValue(UICartModel(goodsList = goodsList))
    }

    data class UIListModel(
        val response: ApiResponse<List<Goods>>,
    )


    data class UICreateGoodModel(
        val response: ApiResponse<BaseBooleanResponse>? = null,
    )

    data class UIDeleteGoodModel(
        val response: ApiResponse<BaseBooleanResponse>? = null,
    )

    data class UICartModel(
        val goodsList: MutableList<Goods>? = null,
        var errorResponse: ApiResponse.Error? = null
    )
}